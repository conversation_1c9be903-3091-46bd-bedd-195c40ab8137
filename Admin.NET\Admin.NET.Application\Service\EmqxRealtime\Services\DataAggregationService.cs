using Admin.NET.Application.Service.EmqxRealtime.DTOs;
using Admin.NET.Application.Service.EmqxRealtime.Interfaces;
using Admin.NET.Application.Entity;
using Admin.NET.Core;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using SqlSugar;
using System.Collections.Concurrent;
using System.Text.Json;

namespace Admin.NET.Application.Service.EmqxRealtime.Services;

/// <summary>
/// 数据聚合服务实现
/// </summary>
public class DataAggregationService : IDataAggregationService, ITransient
{
    private readonly ISqlSugarClient _db;
    private readonly ILogger<DataAggregationService> _logger;
    private readonly IRedisCacheService _redisCacheService;
    
    // 内存队列用于批量聚合
    private readonly ConcurrentQueue<MessageDto> _messageQueue = new();
    private readonly Timer? _flushTimer;
    private readonly SemaphoreSlim _flushSemaphore = new(1, 1);
    
    // 配置参数
    private readonly int _batchSize = 1000;
    private readonly int _flushIntervalSeconds = 300; // 5分钟
    
    // 统计信息
    private long _totalMessagesQueued = 0;
    private long _totalMessagesFlushed = 0;
    private long _totalFlushOperations = 0;
    private DateTime _lastFlushTime = DateTime.Now;
    private bool _isAutoAggregationRunning = false;

    public DataAggregationService(
        ISqlSugarClient db,
        ILogger<DataAggregationService> logger,
        IRedisCacheService redisCacheService)
    {
        _db = db;
        _logger = logger;
        _redisCacheService = redisCacheService;
        
        // 启动定时刷新任务
        _flushTimer = new Timer(async _ => await AutoFlushAsync(), null, 
            TimeSpan.FromSeconds(_flushIntervalSeconds), 
            TimeSpan.FromSeconds(_flushIntervalSeconds));
        
        _isAutoAggregationRunning = true;
        _logger.LogInformation("数据聚合服务已启动，批量大小: {BatchSize}, 刷新间隔: {FlushInterval}秒", 
            _batchSize, _flushIntervalSeconds);
    }

    public async Task AddMessageToQueueAsync(MessageDto message)
    {
        try
        {
            _messageQueue.Enqueue(message);
            Interlocked.Increment(ref _totalMessagesQueued);
            
            _logger.LogDebug("消息已添加到聚合队列: {Topic}, 队列大小: {QueueSize}", 
                message.Topic, _messageQueue.Count);
            
            // 如果队列达到批量大小，触发刷新
            if (_messageQueue.Count >= _batchSize)
            {
                _ = Task.Run(async () => await FlushMessagesToDatabaseAsync());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加消息到聚合队列失败: {Topic}", message.Topic);
        }
    }

    public async Task<int> FlushMessagesToDatabaseAsync()
    {
        if (!await _flushSemaphore.WaitAsync(1000)) // 1秒超时
        {
            _logger.LogWarning("聚合操作正在进行中，跳过本次刷新");
            return 0;
        }

        try
        {
            var messages = new List<MessageDto>();
            var processedCount = 0;
            
            // 从队列中取出消息
            while (messages.Count < _batchSize && _messageQueue.TryDequeue(out var message))
            {
                messages.Add(message);
            }
            
            if (!messages.Any())
            {
                return 0;
            }
            
            // 转换为数据库实体
            var entities = messages.Select(m => new MqttMessageHistory
            {
                MessageId = m.Id,
                Topic = m.Topic,
                Payload = m.Payload,
                QoS = m.QoS,
                Retain = m.Retain,
                Timestamp = m.Timestamp,
                InstanceId = m.InstanceId,
                DeviceId = m.DeviceId,
                MessageSize = m.MessageSize,
                ClientId = m.ClientId,
                CreateTime = DateTime.Now,
                IsAggregated = true
            }).ToList();
            
            // 批量插入数据库
            processedCount = await _db.Insertable(entities).ExecuteCommandAsync();
            
            Interlocked.Add(ref _totalMessagesFlushed, processedCount);
            Interlocked.Increment(ref _totalFlushOperations);
            _lastFlushTime = DateTime.Now;
            
            _logger.LogInformation("批量聚合完成: 处理 {ProcessedCount} 条消息，剩余队列: {QueueSize}", 
                processedCount, _messageQueue.Count);
            
            // 更新统计信息到Redis
            await UpdateAggregationStatisticsAsync();
            
            return processedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量聚合消息到数据库失败");
            return 0;
        }
        finally
        {
            _flushSemaphore.Release();
        }
    }

    public async Task<int> GetQueueSizeAsync()
    {
        return await Task.FromResult(_messageQueue.Count);
    }

    public async Task ClearQueueAsync()
    {
        try
        {
            while (_messageQueue.TryDequeue(out _))
            {
                // 清空队列
            }
            
            _logger.LogInformation("聚合队列已清空");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清空聚合队列失败");
        }
        
        await Task.CompletedTask;
    }

    public async Task<object> GetAggregationStatisticsAsync()
    {
        try
        {
            return new
            {
                QueueSize = _messageQueue.Count,
                TotalMessagesQueued = _totalMessagesQueued,
                TotalMessagesFlushed = _totalMessagesFlushed,
                TotalFlushOperations = _totalFlushOperations,
                LastFlushTime = _lastFlushTime,
                IsAutoAggregationRunning = _isAutoAggregationRunning,
                BatchSize = _batchSize,
                FlushIntervalSeconds = _flushIntervalSeconds,
                AverageMessagesPerFlush = _totalFlushOperations > 0 ? _totalMessagesFlushed / _totalFlushOperations : 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取聚合统计信息失败");
            return new { Error = "获取聚合统计信息失败" };
        }
    }

    public async Task StartAutoAggregationAsync()
    {
        _isAutoAggregationRunning = true;
        _logger.LogInformation("自动聚合任务已启动");
        await Task.CompletedTask;
    }

    public async Task StopAutoAggregationAsync()
    {
        _isAutoAggregationRunning = false;
        
        // 停止前先刷新剩余消息
        await FlushMessagesToDatabaseAsync();
        
        _logger.LogInformation("自动聚合任务已停止");
    }

    public bool IsAutoAggregationRunning()
    {
        return _isAutoAggregationRunning;
    }

    public async Task<int> TriggerManualAggregationAsync()
    {
        try
        {
            _logger.LogInformation("手动触发聚合操作");
            return await FlushMessagesToDatabaseAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "手动触发聚合失败");
            return 0;
        }
    }

    public async Task<List<MessageDto>> GetHistoryMessagesAsync(int instanceId, List<string> topics, DateTime? startTime = null, DateTime? endTime = null, int maxCount = 50)
    {
        try
        {
            var query = _db.Queryable<MqttMessageHistory>()
                .Where(m => m.InstanceId == instanceId)
                .WhereIF(topics.Any(), m => topics.Contains(m.Topic))
                .WhereIF(startTime.HasValue, m => m.Timestamp >= startTime.Value)
                .WhereIF(endTime.HasValue, m => m.Timestamp <= endTime.Value)
                .OrderByDescending(m => m.Timestamp)
                .Take(maxCount);
            
            var messages = await query.ToListAsync();
            
            return messages.Select(m => new MessageDto
            {
                Id = (int)m.Id,
                Topic = m.TopicName,
                Payload = m.Payload,
                QoS = m.QosLevel ?? 0,
                Retain = m.IsRetained ?? false,
                Timestamp = m.ReceivedAt ?? m.CreateTime,
                InstanceId = (int)m.InstanceId,
                DeviceId = m.ClientId,
                MessageSize = (int)(m.MessageSize ?? 0),
                ClientId = m.ClientId
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取历史消息失败: InstanceId={InstanceId}, Topics={Topics}", 
                instanceId, string.Join(", ", topics));
            return new List<MessageDto>();
        }
    }

    public async Task<int> CleanupExpiredMessagesAsync(int retentionDays = 30)
    {
        try
        {
            var cutoffDate = DateTime.Now.AddDays(-retentionDays);
            var deletedCount = await _db.Deleteable<MqttMessageHistory>()
                .Where(m => m.CreateTime < cutoffDate)
                .ExecuteCommandAsync();
            
            _logger.LogInformation("清理过期消息完成: 删除 {DeletedCount} 条消息，保留天数: {RetentionDays}", 
                deletedCount, retentionDays);
            
            return deletedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期消息失败");
            return 0;
        }
    }

    private async Task AutoFlushAsync()
    {
        if (!_isAutoAggregationRunning)
            return;
            
        try
        {
            if (_messageQueue.Count > 0)
            {
                await FlushMessagesToDatabaseAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "自动聚合任务执行失败");
        }
    }

    private async Task UpdateAggregationStatisticsAsync()
    {
        try
        {
            var statistics = await GetAggregationStatisticsAsync();
            await _redisCacheService.CacheStatisticsAsync("aggregation_stats", statistics, 300);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新聚合统计信息失败");
        }
    }

    public void Dispose()
    {
        _flushTimer?.Dispose();
        _flushSemaphore?.Dispose();
    }
}