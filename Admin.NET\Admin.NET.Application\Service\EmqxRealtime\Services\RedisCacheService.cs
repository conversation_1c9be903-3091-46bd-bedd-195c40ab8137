using Admin.NET.Application.Service.EmqxRealtime.DTOs;
using Admin.NET.Application.Service.EmqxRealtime.Interfaces;
using Admin.NET.Application.Entity;
using Admin.NET.Application.Service.EmqxRealtime.Configuration;
using Admin.NET.Core;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Admin.NET.Application.Service.EmqxRealtime.Services;

/// <summary>
/// Redis缓存服务实现
/// </summary>
public class RedisCacheService : IRedisCacheService, ITransient
{
    private readonly ILogger<RedisCacheService> _logger;
    private readonly IDatabase _database;
    private readonly RedisOptions _options;
    private readonly JsonSerializerOptions _jsonOptions;

    public RedisCacheService(
        ILogger<RedisCacheService> logger,
        IConnectionMultiplexer redis,
        IOptions<RedisOptions> options)
    {
        _logger = logger;
        _database = redis.GetDatabase(options.Value.Database);
        _options = options.Value;
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
        };
    }

    public async Task<bool> CacheMessageAsync(MessageDto message, int? expireSeconds = null)
    {
        try
        {
            var key = GetMessageKey(message.InstanceId, message.Topic, message.Id);
            var value = JsonSerializer.Serialize(message, _jsonOptions);
            var expiry = TimeSpan.FromSeconds(expireSeconds ?? _options.MessageCacheExpireSeconds);
            
            var result = await _database.StringSetAsync(key, value, expiry);
            
            // 添加到主题消息列表
            var topicKey = GetTopicMessagesKey(message.InstanceId, message.Topic);
            await _database.ListLeftPushAsync(topicKey, message.Id);
            await _database.ExpireAsync(topicKey, expiry);
            
            // 限制列表长度
            await _database.ListTrimAsync(topicKey, 0, _options.MaxCachedMessages - 1);
            
            _logger.LogDebug("缓存消息成功: {Key}", key);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "缓存消息失败: {MessageId}", message.Id);
            return false;
        }
    }

    public async Task<List<MessageDto>> GetCachedMessagesAsync(int instanceId, List<string> topics, int maxCount = 50)
    {
        try
        {
            var messages = new List<MessageDto>();
            
            foreach (var topic in topics)
            {
                var topicKey = GetTopicMessagesKey(instanceId, topic);
                var messageIds = await _database.ListRangeAsync(topicKey, 0, maxCount - 1);
                
                foreach (var messageId in messageIds)
                {
                    var messageKey = GetMessageKey(instanceId, topic, messageId!);
                    var messageJson = await _database.StringGetAsync(messageKey);
                    
                    if (messageJson.HasValue)
                    {
                        var message = JsonSerializer.Deserialize<MessageDto>(messageJson!, _jsonOptions);
                        if (message != null)
                        {
                            messages.Add(message);
                        }
                    }
                }
                
                if (messages.Count >= maxCount)
                    break;
            }
            
            return messages.OrderByDescending(m => m.Timestamp).Take(maxCount).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取缓存消息失败: InstanceId={InstanceId}, Topics={Topics}", 
                instanceId, string.Join(", ", topics));
            return new List<MessageDto>();
        }
    }

    public async Task<bool> CacheSubscriptionAsync(string connectionId, SubscriptionInfo subscriptionInfo)
    {
        try
        {
            var key = GetSubscriptionKey(connectionId);
            var value = JsonSerializer.Serialize(subscriptionInfo, _jsonOptions);
            var expiry = TimeSpan.FromSeconds(_options.DefaultExpireSeconds);
            
            var result = await _database.StringSetAsync(key, value, expiry);
            
            // 添加到活跃连接集合
            var activeKey = GetActiveConnectionsKey();
            await _database.SetAddAsync(activeKey, connectionId);
            await _database.ExpireAsync(activeKey, expiry);
            
            _logger.LogDebug("缓存订阅信息成功: {ConnectionId}", connectionId);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "缓存订阅信息失败: {ConnectionId}", connectionId);
            return false;
        }
    }

    public async Task<SubscriptionInfo?> GetSubscriptionAsync(string connectionId)
    {
        try
        {
            var key = GetSubscriptionKey(connectionId);
            var value = await _database.StringGetAsync(key);
            
            if (!value.HasValue)
                return null;
                
            return JsonSerializer.Deserialize<SubscriptionInfo>(value!, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取订阅信息失败: {ConnectionId}", connectionId);
            return null;
        }
    }

    public async Task<bool> RemoveSubscriptionAsync(string connectionId)
    {
        try
        {
            var key = GetSubscriptionKey(connectionId);
            var result = await _database.KeyDeleteAsync(key);
            
            // 从活跃连接集合移除
            var activeKey = GetActiveConnectionsKey();
            await _database.SetRemoveAsync(activeKey, connectionId);
            
            _logger.LogDebug("移除订阅信息成功: {ConnectionId}", connectionId);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除订阅信息失败: {ConnectionId}", connectionId);
            return false;
        }
    }

    public async Task<List<SubscriptionInfo>> GetAllSubscriptionsAsync()
    {
        try
        {
            var subscriptions = new List<SubscriptionInfo>();
            var activeKey = GetActiveConnectionsKey();
            var connectionIds = await _database.SetMembersAsync(activeKey);
            
            foreach (var connectionId in connectionIds)
            {
                var subscription = await GetSubscriptionAsync(connectionId!);
                if (subscription != null)
                {
                    subscriptions.Add(subscription);
                }
            }
            
            return subscriptions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有订阅信息失败");
            return new List<SubscriptionInfo>();
        }
    }

    public async Task<bool> CacheStatisticsAsync(string key, object value, int? expireSeconds = null)
    {
        try
        {
            var redisKey = GetStatisticsKey(key);
            var jsonValue = JsonSerializer.Serialize(value, _jsonOptions);
            var expiry = TimeSpan.FromSeconds(expireSeconds ?? _options.DefaultExpireSeconds);
            
            var result = await _database.StringSetAsync(redisKey, jsonValue, expiry);
            _logger.LogDebug("缓存统计信息成功: {Key}", key);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "缓存统计信息失败: {Key}", key);
            return false;
        }
    }

    public async Task<T?> GetStatisticsAsync<T>(string key)
    {
        try
        {
            var redisKey = GetStatisticsKey(key);
            var value = await _database.StringGetAsync(redisKey);
            
            if (!value.HasValue)
                return default;
                
            return JsonSerializer.Deserialize<T>(value!, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取统计信息失败: {Key}", key);
            return default;
        }
    }

    public async Task<long> IncrementCounterAsync(string key, long increment = 1)
    {
        try
        {
            var redisKey = GetCounterKey(key);
            var result = await _database.StringIncrementAsync(redisKey, increment);
            
            // 设置过期时间（如果是新键）
            if (result == increment)
            {
                await _database.ExpireAsync(redisKey, TimeSpan.FromSeconds(_options.DefaultExpireSeconds));
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "增加计数器失败: {Key}", key);
            return 0;
        }
    }

    public async Task<long> GetCounterAsync(string key)
    {
        try
        {
            var redisKey = GetCounterKey(key);
            var value = await _database.StringGetAsync(redisKey);
            
            if (!value.HasValue)
                return 0;
                
            return value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取计数器失败: {Key}", key);
            return 0;
        }
    }

    public async Task<bool> SetExpireAsync(string key, int expireSeconds)
    {
        try
        {
            var redisKey = GetFullKey(key);
            return await _database.ExpireAsync(redisKey, TimeSpan.FromSeconds(expireSeconds));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置过期时间失败: {Key}", key);
            return false;
        }
    }

    public async Task<bool> DeleteAsync(string key)
    {
        try
        {
            var redisKey = GetFullKey(key);
            return await _database.KeyDeleteAsync(redisKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除键失败: {Key}", key);
            return false;
        }
    }

    public async Task<long> DeleteAsync(List<string> keys)
    {
        try
        {
            var redisKeys = keys.Select(GetFullKey).ToArray();
            return await _database.KeyDeleteAsync(redisKeys);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量删除键失败: {Keys}", string.Join(", ", keys));
            return 0;
        }
    }

    #region 私有方法

    private string GetFullKey(string key)
    {
        return $"{_options.KeyPrefix}{key}";
    }

    private string GetMessageKey(int instanceId, string topic, string messageId)
    {
        return GetFullKey($"message:{instanceId}:{topic}:{messageId}");
    }

    private string GetTopicMessagesKey(int instanceId, string topic)
    {
        return GetFullKey($"topic_messages:{instanceId}:{topic}");
    }

    private string GetSubscriptionKey(string connectionId)
    {
        return GetFullKey($"subscription:{connectionId}");
    }

    private string GetActiveConnectionsKey()
    {
        return GetFullKey("active_connections");
    }

    private string GetStatisticsKey(string key)
    {
        return GetFullKey($"statistics:{key}");
    }

    private string GetCounterKey(string key)
    {
        return GetFullKey($"counter:{key}");
    }

    #endregion
}