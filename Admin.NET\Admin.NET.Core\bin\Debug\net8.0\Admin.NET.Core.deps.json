{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {"defines": ["TRACE", "DEBUG", "NET", "NET8_0", "NETCOREAPP", "NET5_0_OR_GREATER", "NET6_0_OR_GREATER", "NET7_0_OR_GREATER", "NET8_0_OR_GREATER", "NETCOREAPP1_0_OR_GREATER", "NETCOREAPP1_1_OR_GREATER", "NETCOREAPP2_0_OR_GREATER", "NETCOREAPP2_1_OR_GREATER", "NETCOREAPP2_2_OR_GREATER", "NETCOREAPP3_0_OR_GREATER", "NETCOREAPP3_1_OR_GREATER"], "languageVersion": "12.0", "platform": "", "allowUnsafe": false, "warningsAsErrors": false, "optimize": false, "keyFile": "", "emitEntryPoint": false, "xmlDoc": true, "debugType": "portable"}, "targets": {".NETCoreApp,Version=v8.0": {"Admin.NET.Core/1.0.0": {"dependencies": {"AlibabaCloud.SDK.Dysmsapi20170525": "4.0.0", "AlipaySDKNet.Standard": "4.9.627", "AngleSharp": "1.3.0", "AspNet.Security.OAuth.Gitee": "8.3.0", "AspNet.Security.OAuth.Weixin": "8.3.0", "AspNetCoreRateLimit": "5.0.0", "AspectCore.Extensions.Reflection": "2.4.0", "BouncyCastle.Cryptography": "2.6.1", "Elastic.Clients.Elasticsearch": "9.0.7", "Furion.Extras.Authentication.JwtBearer": "*********", "Furion.Extras.ObjectMapper.Mapster": "*********", "Furion.Pure": "*********", "Hardware.Info": "*********", "Hashids.net": "1.7.0", "IPTools.China": "1.6.0", "IPTools.International": "1.6.0", "Lazy.Captcha.Core": "2.2.0", "Magicodes.IE.Excel": "2.7.6", "Magicodes.IE.Pdf": "2.7.6", "Magicodes.IE.Word": "2.7.6", "MailKit": "4.13.0", "Microsoft.AspNetCore.DataProtection.StackExchangeRedis": "8.0.11", "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson": "8.0.11", "Microsoft.AspNetCore.SignalR.StackExchangeRedis": "8.0.11", "Microsoft.PowerShell.SDK": "7.4.11", "MiniExcel": "1.41.3", "MiniWord": "0.9.2", "NewLife.Redis": "6.3.2025.701", "Novell.Directory.Ldap.NETStandard": "4.0.0", "OnceMi.AspNetCore.OSS": "1.2.0", "QRCoder": "1.6.0", "RabbitMQ.Client": "7.1.2", "SKIT.FlurlHttpClient.Wechat.Api": "3.11.0", "SKIT.FlurlHttpClient.Wechat.TenpayV3": "3.13.0", "SSH.NET": "2025.0.0", "SixLabors.ImageSharp.Web": "3.1.5", "SqlSugar.MongoDbCore": "*********", "SqlSugarCore": "*********", "System.Linq.Dynamic.Core": "1.6.6", "System.Net.Http": "4.3.4", "System.Private.Uri": "4.3.2", "TencentCloudSDK.Sms": "3.0.1273", "UAParser": "3.1.47", "Yitter.IdGenerator": "1.0.14", "log4net": "3.1.0", "Microsoft.AspNetCore.Antiforgery": "*******", "Microsoft.AspNetCore.Authentication.Abstractions": "*******", "Microsoft.AspNetCore.Authentication.BearerToken": "*******", "Microsoft.AspNetCore.Authentication.Cookies": "*******", "Microsoft.AspNetCore.Authentication.Core": "*******", "Microsoft.AspNetCore.Authentication": "*******", "Microsoft.AspNetCore.Authentication.OAuth": "*******", "Microsoft.AspNetCore.Authorization": "*******", "Microsoft.AspNetCore.Authorization.Policy": "*******", "Microsoft.AspNetCore.Components.Authorization": "*******", "Microsoft.AspNetCore.Components": "*******", "Microsoft.AspNetCore.Components.Endpoints": "*******", "Microsoft.AspNetCore.Components.Forms": "*******", "Microsoft.AspNetCore.Components.Server": "*******", "Microsoft.AspNetCore.Components.Web": "*******", "Microsoft.AspNetCore.Connections.Abstractions.Reference": "*******", "Microsoft.AspNetCore.CookiePolicy": "*******", "Microsoft.AspNetCore.Cors": "*******", "Microsoft.AspNetCore.Cryptography.Internal.Reference": "*******", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "*******", "Microsoft.AspNetCore.DataProtection.Abstractions.Reference": "*******", "Microsoft.AspNetCore.DataProtection.Reference": "*******", "Microsoft.AspNetCore.DataProtection.Extensions": "*******", "Microsoft.AspNetCore.Diagnostics.Abstractions": "*******", "Microsoft.AspNetCore.Diagnostics": "*******", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "*******", "Microsoft.AspNetCore": "*******", "Microsoft.AspNetCore.HostFiltering": "*******", "Microsoft.AspNetCore.Hosting.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Hosting": "*******", "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Html.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Http.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Http.Connections.Common": "*******", "Microsoft.AspNetCore.Http.Connections": "*******", "Microsoft.AspNetCore.Http": "*******", "Microsoft.AspNetCore.Http.Extensions.Reference": "*******", "Microsoft.AspNetCore.Http.Features.Reference": "*******", "Microsoft.AspNetCore.Http.Results": "*******", "Microsoft.AspNetCore.HttpLogging": "*******", "Microsoft.AspNetCore.HttpOverrides": "*******", "Microsoft.AspNetCore.HttpsPolicy": "*******", "Microsoft.AspNetCore.Identity": "*******", "Microsoft.AspNetCore.Localization": "*******", "Microsoft.AspNetCore.Localization.Routing": "*******", "Microsoft.AspNetCore.Metadata": "*******", "Microsoft.AspNetCore.Mvc.Abstractions": "*******", "Microsoft.AspNetCore.Mvc.ApiExplorer": "*******", "Microsoft.AspNetCore.Mvc.Core": "*******", "Microsoft.AspNetCore.Mvc.Cors": "*******", "Microsoft.AspNetCore.Mvc.DataAnnotations": "*******", "Microsoft.AspNetCore.Mvc": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Json": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "*******", "Microsoft.AspNetCore.Mvc.Localization": "*******", "Microsoft.AspNetCore.Mvc.Razor": "*******", "Microsoft.AspNetCore.Mvc.RazorPages": "*******", "Microsoft.AspNetCore.Mvc.TagHelpers": "*******", "Microsoft.AspNetCore.Mvc.ViewFeatures": "*******", "Microsoft.AspNetCore.OutputCaching": "*******", "Microsoft.AspNetCore.RateLimiting": "*******", "Microsoft.AspNetCore.Razor.Reference": "*******", "Microsoft.AspNetCore.Razor.Runtime.Reference": "*******", "Microsoft.AspNetCore.RequestDecompression": "*******", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "*******", "Microsoft.AspNetCore.ResponseCaching": "*******", "Microsoft.AspNetCore.ResponseCompression": "*******", "Microsoft.AspNetCore.Rewrite": "*******", "Microsoft.AspNetCore.Routing.Abstractions": "*******", "Microsoft.AspNetCore.Routing": "*******", "Microsoft.AspNetCore.Server.HttpSys": "*******", "Microsoft.AspNetCore.Server.IIS": "*******", "Microsoft.AspNetCore.Server.IISIntegration": "*******", "Microsoft.AspNetCore.Server.Kestrel.Core": "*******", "Microsoft.AspNetCore.Server.Kestrel": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "*******", "Microsoft.AspNetCore.Session": "*******", "Microsoft.AspNetCore.SignalR.Common.Reference": "*******", "Microsoft.AspNetCore.SignalR.Core": "*******", "Microsoft.AspNetCore.SignalR": "*******", "Microsoft.AspNetCore.SignalR.Protocols.Json": "*******", "Microsoft.AspNetCore.StaticFiles.Reference": "*******", "Microsoft.AspNetCore.WebSockets": "*******", "Microsoft.AspNetCore.WebUtilities": "*******", "Microsoft.CSharp.Reference": "*******", "Microsoft.Extensions.Configuration.CommandLine": "*******", "Microsoft.Extensions.Configuration": "*******", "Microsoft.Extensions.Configuration.EnvironmentVariables": "*******", "Microsoft.Extensions.Configuration.FileExtensions": "*******", "Microsoft.Extensions.Configuration.Ini": "*******", "Microsoft.Extensions.Configuration.Json": "*******", "Microsoft.Extensions.Configuration.KeyPerFile": "*******", "Microsoft.Extensions.Configuration.UserSecrets": "*******", "Microsoft.Extensions.Configuration.Xml": "*******", "Microsoft.Extensions.DependencyInjection.Reference": "*******", "Microsoft.Extensions.Diagnostics.Abstractions.Reference": "*******", "Microsoft.Extensions.Diagnostics": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks": "*******", "Microsoft.Extensions.Features.Reference": "*******", "Microsoft.Extensions.FileProviders.Abstractions.Reference": "*******", "Microsoft.Extensions.FileProviders.Composite": "*******", "Microsoft.Extensions.FileProviders.Embedded": "*******", "Microsoft.Extensions.FileProviders.Physical": "*******", "Microsoft.Extensions.FileSystemGlobbing": "*******", "Microsoft.Extensions.Hosting.Abstractions.Reference": "*******", "Microsoft.Extensions.Hosting": "*******", "Microsoft.Extensions.Http": "*******", "Microsoft.Extensions.Identity.Core": "*******", "Microsoft.Extensions.Identity.Stores": "*******", "Microsoft.Extensions.Localization.Abstractions": "*******", "Microsoft.Extensions.Localization": "*******", "Microsoft.Extensions.Logging.Configuration": "*******", "Microsoft.Extensions.Logging.Console": "*******", "Microsoft.Extensions.Logging.Debug": "*******", "Microsoft.Extensions.Logging": "*******", "Microsoft.Extensions.Logging.EventLog": "*******", "Microsoft.Extensions.Logging.EventSource": "*******", "Microsoft.Extensions.Logging.TraceSource": "*******", "Microsoft.Extensions.ObjectPool.Reference": "*******", "Microsoft.Extensions.Options.DataAnnotations": "*******", "Microsoft.Extensions.WebEncoders.Reference": "*******", "Microsoft.JSInterop": "*******", "Microsoft.Net.Http.Headers.Reference": "*******", "Microsoft.VisualBasic.Core": "********", "Microsoft.VisualBasic": "10.0.0.0", "Microsoft.Win32.Primitives": "*******", "Microsoft.Win32.Registry.Reference": "*******", "mscorlib": "*******", "netstandard": "2.1.0.0", "System.AppContext": "*******", "System.Buffers.Reference": "*******", "System.Collections.Concurrent.Reference": "*******", "System.Collections.Reference": "*******", "System.Collections.Immutable.Reference": "*******", "System.Collections.NonGeneric": "*******", "System.Collections.Specialized": "*******", "System.ComponentModel.Annotations.Reference": "*******", "System.ComponentModel.DataAnnotations": "*******", "System.ComponentModel": "*******", "System.ComponentModel.EventBasedAsync": "*******", "System.ComponentModel.Primitives": "*******", "System.ComponentModel.TypeConverter": "*******", "System.Configuration": "*******", "System.Console": "*******", "System.Core": "*******", "System.Data.Common.Reference": "*******", "System.Data.DataSetExtensions": "*******", "System.Data": "*******", "System.Diagnostics.Contracts": "*******", "System.Diagnostics.Debug.Reference": "*******", "System.Diagnostics.EventLog.Reference": "*******", "System.Diagnostics.FileVersionInfo": "*******", "System.Diagnostics.Process": "*******", "System.Diagnostics.StackTrace": "*******", "System.Diagnostics.TextWriterTraceListener": "*******", "System.Diagnostics.Tools": "*******", "System.Diagnostics.TraceSource": "*******", "System.Diagnostics.Tracing.Reference": "*******", "System": "*******", "System.Drawing": "*******", "System.Drawing.Primitives": "*******", "System.Dynamic.Runtime": "*******", "System.Formats.Asn1.Reference": "*******", "System.Formats.Tar": "*******", "System.Globalization.Calendars.Reference": "*******", "System.Globalization.Reference": "*******", "System.Globalization.Extensions.Reference": "*******", "System.IO.Compression.Brotli": "*******", "System.IO.Compression": "*******", "System.IO.Compression.FileSystem": "*******", "System.IO.Compression.ZipFile": "*******", "System.IO.Reference": "*******", "System.IO.FileSystem.AccessControl": "*******", "System.IO.FileSystem.Reference": "*******", "System.IO.FileSystem.DriveInfo": "*******", "System.IO.FileSystem.Primitives.Reference": "*******", "System.IO.FileSystem.Watcher": "*******", "System.IO.IsolatedStorage": "*******", "System.IO.MemoryMappedFiles": "*******", "System.IO.Pipelines.Reference": "*******", "System.IO.Pipes.AccessControl": "*******", "System.IO.Pipes": "*******", "System.IO.UnmanagedMemoryStream": "*******", "System.Linq.Reference": "*******", "System.Linq.Expressions": "*******", "System.Linq.Parallel": "*******", "System.Linq.Queryable": "*******", "System.Memory.Reference": "*******", "System.Net": "*******", "System.Net.Http.Reference": "*******", "System.Net.Http.Json": "*******", "System.Net.HttpListener": "*******", "System.Net.Mail": "*******", "System.Net.NameResolution": "*******", "System.Net.NetworkInformation": "*******", "System.Net.Ping": "*******", "System.Net.Primitives.Reference": "*******", "System.Net.Quic": "*******", "System.Net.Requests": "*******", "System.Net.Security": "*******", "System.Net.ServicePoint": "*******", "System.Net.Sockets": "*******", "System.Net.WebClient": "*******", "System.Net.WebHeaderCollection": "*******", "System.Net.WebProxy": "*******", "System.Net.WebSockets.Client": "*******", "System.Net.WebSockets": "*******", "System.Numerics": "*******", "System.Numerics.Vectors.Reference": "*******", "System.ObjectModel": "*******", "System.Reflection.DispatchProxy.Reference": "*******", "System.Reflection.Reference": "*******", "System.Reflection.Emit.Reference": "*******", "System.Reflection.Emit.ILGeneration.Reference": "*******", "System.Reflection.Emit.Lightweight.Reference": "*******", "System.Reflection.Extensions": "*******", "System.Reflection.Metadata.Reference": "*******", "System.Reflection.Primitives.Reference": "*******", "System.Reflection.TypeExtensions.Reference": "*******", "System.Resources.Reader": "*******", "System.Resources.ResourceManager.Reference": "*******", "System.Resources.Writer": "*******", "System.Runtime.CompilerServices.Unsafe.Reference": "*******", "System.Runtime.CompilerServices.VisualC": "*******", "System.Runtime.Reference": "*******", "System.Runtime.Extensions.Reference": "*******", "System.Runtime.Handles.Reference": "*******", "System.Runtime.InteropServices.Reference": "*******", "System.Runtime.InteropServices.JavaScript": "*******", "System.Runtime.InteropServices.RuntimeInformation": "*******", "System.Runtime.Intrinsics": "*******", "System.Runtime.Loader": "*******", "System.Runtime.Numerics.Reference": "*******", "System.Runtime.Serialization": "*******", "System.Runtime.Serialization.Formatters": "*******", "System.Runtime.Serialization.Json": "*******", "System.Runtime.Serialization.Primitives": "*******", "System.Runtime.Serialization.Xml": "*******", "System.Security.AccessControl.Reference": "*******", "System.Security.Claims": "*******", "System.Security.Cryptography.Algorithms.Reference": "*******", "System.Security.Cryptography.Cng.Reference": "*******", "System.Security.Cryptography.Csp.Reference": "*******", "System.Security.Cryptography": "*******", "System.Security.Cryptography.Encoding.Reference": "*******", "System.Security.Cryptography.OpenSsl.Reference": "*******", "System.Security.Cryptography.Primitives.Reference": "*******", "System.Security.Cryptography.X509Certificates.Reference": "*******", "System.Security.Cryptography.Xml.Reference": "*******", "System.Security": "*******", "System.Security.Principal": "*******", "System.Security.Principal.Windows.Reference": "*******", "System.Security.SecureString": "*******", "System.ServiceModel.Web": "*******", "System.ServiceProcess": "*******", "System.Text.Encoding.CodePages.Reference": "*******", "System.Text.Encoding.Reference": "*******", "System.Text.Encoding.Extensions": "*******", "System.Text.Encodings.Web.Reference": "*******", "System.Text.Json.Reference": "*******", "System.Text.RegularExpressions.Reference": "*******", "System.Threading.Channels": "*******", "System.Threading.Reference": "*******", "System.Threading.Overlapped": "*******", "System.Threading.RateLimiting.Reference": "*******", "System.Threading.Tasks.Dataflow": "*******", "System.Threading.Tasks.Reference": "*******", "System.Threading.Tasks.Extensions.Reference": "*******", "System.Threading.Tasks.Parallel": "*******", "System.Threading.Thread.Reference": "*******", "System.Threading.ThreadPool": "*******", "System.Threading.Timer": "*******", "System.Transactions": "*******", "System.Transactions.Local": "*******", "System.ValueTuple": "*******", "System.Web": "*******", "System.Web.HttpUtility": "*******", "System.Windows": "*******", "System.Xml": "*******", "System.Xml.Linq": "*******", "System.Xml.ReaderWriter": "*******", "System.Xml.Serialization": "*******", "System.Xml.XDocument": "*******", "System.Xml.XmlDocument": "*******", "System.Xml.XmlSerializer": "*******", "System.Xml.XPath": "*******", "System.Xml.XPath.XDocument": "*******", "WindowsBase": "*******"}, "runtime": {"Admin.NET.Core.dll": {}}, "compile": {"Admin.NET.Core.dll": {}}}, "AlibabaCloud.EndpointUtil/0.1.1": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Tea": "1.1.3"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.EndpointUtil.dll": {"assemblyVersion": "0.1.1.0", "fileVersion": "0.1.1.0"}}, "compile": {"lib/netstandard2.0/AlibabaCloud.EndpointUtil.dll": {}}}, "AlibabaCloud.GatewaySpi/0.0.3": {"dependencies": {"Aliyun.Credentials": "1.5.0", "Tea": "1.1.3"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.GatewaySpi.dll": {"assemblyVersion": "0.0.1.0", "fileVersion": "0.0.1.0"}}, "compile": {"lib/netstandard2.0/AlibabaCloud.GatewaySpi.dll": {}}}, "AlibabaCloud.OpenApiClient/0.1.14": {"dependencies": {"AlibabaCloud.GatewaySpi": "0.0.3", "AlibabaCloud.OpenApiUtil": "1.1.2", "AlibabaCloud.TeaUtil": "0.1.19", "AlibabaCloud.TeaXML": "0.0.5", "Aliyun.Credentials": "1.5.0", "Tea": "1.1.3"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.OpenApiClient.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.0/AlibabaCloud.OpenApiClient.dll": {}}}, "AlibabaCloud.OpenApiUtil/1.1.2": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Tea": "1.1.3"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.OpenApiUtil.dll": {"assemblyVersion": "1.1.2.0", "fileVersion": "1.1.2.0"}}, "compile": {"lib/netstandard2.0/AlibabaCloud.OpenApiUtil.dll": {}}}, "AlibabaCloud.SDK.Dysmsapi20170525/4.0.0": {"dependencies": {"AlibabaCloud.EndpointUtil": "0.1.1", "AlibabaCloud.OpenApiClient": "0.1.14", "AlibabaCloud.OpenApiUtil": "1.1.2", "AlibabaCloud.TeaUtil": "0.1.19", "Tea": "1.1.3"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.SDK.Dysmsapi20170525.dll": {"assemblyVersion": "3.1.3.0", "fileVersion": "3.1.3.0"}}, "compile": {"lib/netstandard2.0/AlibabaCloud.SDK.Dysmsapi20170525.dll": {}}}, "AlibabaCloud.TeaUtil/0.1.19": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Tea": "1.1.3"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.TeaUtil.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.0/AlibabaCloud.TeaUtil.dll": {}}}, "AlibabaCloud.TeaXML/0.0.5": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Tea": "1.1.3"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.TeaXML.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/AlibabaCloud.TeaXML.dll": {}}}, "AlipaySDKNet.Standard/4.9.627": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Portable.BouncyCastle": "*******", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/netstandard2.0/AlipaySDKNet.Standard.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/AlipaySDKNet.Standard.dll": {}}}, "Aliyun.Credentials/1.5.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3", "Tea": "1.1.3"}, "runtime": {"lib/netstandard2.0/Aliyun.Credentials.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Aliyun.Credentials.dll": {}}}, "Aliyun.OSS.SDK.NetCore/2.13.0": {"runtime": {"lib/netstandard2.0/Aliyun.OSS.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.0/Aliyun.OSS.Core.dll": {}}}, "AngleSharp/1.3.0": {"runtime": {"lib/net8.0/AngleSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/AngleSharp.dll": {}}}, "AspectCore.Extensions.Reflection/2.4.0": {"runtime": {"lib/net7.0/AspectCore.Extensions.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net7.0/AspectCore.Extensions.Reflection.dll": {}}}, "AspNet.Security.OAuth.Gitee/8.3.0": {"runtime": {"lib/net8.0/AspNet.Security.OAuth.Gitee.dll": {"assemblyVersion": "8.3.0.0", "fileVersion": "8.300.24.55742"}}, "compile": {"lib/net8.0/AspNet.Security.OAuth.Gitee.dll": {}}}, "AspNet.Security.OAuth.Weixin/8.3.0": {"runtime": {"lib/net8.0/AspNet.Security.OAuth.Weixin.dll": {"assemblyVersion": "8.3.0.0", "fileVersion": "8.300.24.55742"}}, "compile": {"lib/net8.0/AspNet.Security.OAuth.Weixin.dll": {}}}, "AspNetCoreRateLimit/5.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/AspNetCoreRateLimit.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.0.2.0"}}, "compile": {"lib/net6.0/AspNetCoreRateLimit.dll": {}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "9.0.0", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.38.0.0", "fileVersion": "1.3800.24.12602"}}, "compile": {"lib/net6.0/Azure.Core.dll": {}}}, "Azure.Identity/1.11.4": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.6.0", "System.Security.Cryptography.ProtectedData": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.11.4.0", "fileVersion": "1.1100.424.31005"}}, "compile": {"lib/netstandard2.0/Azure.Identity.dll": {}}}, "BceSdkDotNetCore/1.0.2.911": {"dependencies": {"Newtonsoft.Json": "13.0.3", "log4net": "3.1.0"}, "runtime": {"lib/netstandard2.0/BceSdkDotNetCore.dll": {"assemblyVersion": "1.0.2.911", "fileVersion": "1.0.2.911"}}, "compile": {"lib/netstandard2.0/BceSdkDotNetCore.dll": {}}}, "Ben.Demystifier/0.4.1": {"dependencies": {"System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/netstandard2.1/Ben.Demystifier.dll": {"assemblyVersion": "0.4.0.0", "fileVersion": "0.4.0.2"}}, "compile": {"lib/netstandard2.1/Ben.Demystifier.dll": {}}}, "BouncyCastle.Cryptography/2.6.1": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "2.6.1.59591"}}, "compile": {"lib/net6.0/BouncyCastle.Cryptography.dll": {}}}, "CommunityToolkit.HighPerformance/8.1.0": {"runtime": {"lib/net7.0/CommunityToolkit.HighPerformance.dll": {"assemblyVersion": "8.1.0.0", "fileVersion": "8.1.0.1"}}, "compile": {"lib/net7.0/CommunityToolkit.HighPerformance.dll": {}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/net5.0/DnsClient.dll": {"assemblyVersion": "1.6.1.0", "fileVersion": "1.6.1.0"}}, "compile": {"lib/net5.0/DnsClient.dll": {}}}, "DocumentFormat.OpenXml/3.1.1": {"dependencies": {"DocumentFormat.OpenXml.Framework": "3.1.1"}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.dll": {"assemblyVersion": "3.1.1.0", "fileVersion": "3.1.1.0"}}, "compile": {"lib/net8.0/DocumentFormat.OpenXml.dll": {}}}, "DocumentFormat.OpenXml.Framework/3.1.1": {"dependencies": {"System.IO.Packaging": "8.0.1"}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {"assemblyVersion": "3.1.1.0", "fileVersion": "3.1.1.0"}}, "compile": {"lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {}}}, "DynamicExpresso.Core/2.3.3": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.0/DynamicExpresso.Core.dll": {"assemblyVersion": "2.3.3.0", "fileVersion": "2.3.3.0"}}, "compile": {"lib/netstandard2.0/DynamicExpresso.Core.dll": {}}}, "Elastic.Clients.Elasticsearch/9.0.7": {"dependencies": {"Elastic.Transport": "0.9.2"}, "runtime": {"lib/net8.0/Elastic.Clients.Elasticsearch.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.7.0"}}, "compile": {"lib/net8.0/Elastic.Clients.Elasticsearch.dll": {}}}, "Elastic.Transport/0.9.2": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/net8.0/Elastic.Transport.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.9.2.0"}}, "compile": {"lib/net8.0/Elastic.Transport.dll": {}}}, "Flurl/4.0.0": {"runtime": {"lib/netstandard2.0/Flurl.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Flurl.dll": {}}}, "Flurl.Http/4.0.2": {"dependencies": {"Flurl": "4.0.0"}, "runtime": {"lib/net6.0/Flurl.Http.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.0.2.0"}}, "compile": {"lib/net6.0/Flurl.Http.dll": {}}}, "Furion.Extras.Authentication.JwtBearer/*********": {"dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.18"}, "runtime": {"lib/net8.0/Furion.Extras.Authentication.JwtBearer.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "compile": {"lib/net8.0/Furion.Extras.Authentication.JwtBearer.dll": {}}}, "Furion.Extras.ObjectMapper.Mapster/*********": {"dependencies": {"Mapster": "7.4.0", "Mapster.DependencyInjection": "1.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Furion.Extras.ObjectMapper.Mapster.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "compile": {"lib/net8.0/Furion.Extras.ObjectMapper.Mapster.dll": {}}}, "Furion.Pure/*********": {"dependencies": {"Furion.Pure.Extras.DependencyModel.CodeAnalysis": "*********", "Swashbuckle.AspNetCore": "9.0.3"}, "runtime": {"lib/net8.0/Furion.Pure.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "compile": {"lib/net8.0/Furion.Pure.dll": {}}}, "Furion.Pure.Extras.DependencyModel.CodeAnalysis/*********": {"dependencies": {"Ben.Demystifier": "0.4.1", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.18", "Microsoft.AspNetCore.Razor.Language": "6.0.36", "Microsoft.CodeAnalysis.CSharp": "4.9.2", "Microsoft.Extensions.DependencyModel": "8.0.2"}, "runtime": {"lib/net8.0/Furion.Pure.Extras.DependencyModel.CodeAnalysis.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "compile": {"lib/net8.0/Furion.Pure.Extras.DependencyModel.CodeAnalysis.dll": {}}}, "Hardware.Info/*********": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Diagnostics.PerformanceCounter": "8.0.1", "System.Management": "8.0.0"}, "runtime": {"lib/netstandard2.0/Hardware.Info.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "compile": {"lib/netstandard2.0/Hardware.Info.dll": {}}}, "Hashids.net/1.7.0": {"runtime": {"lib/net7.0/Hashids.net.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}}, "compile": {"lib/net7.0/Hashids.net.dll": {}}}, "Haukcode.WkHtmlToPdfDotNet/1.5.95": {"dependencies": {"System.Collections.Concurrent": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection.TypeExtensions": "4.7.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices": "4.3.0", "System.Threading.Thread": "4.3.0"}, "runtime": {"lib/netstandard2.0/WkHtmlToPdfDotNet.dll": {"assemblyVersion": "1.5.95.0", "fileVersion": "1.5.95.0"}}, "runtimeTargets": {"runtimes/linux-arm64/native/libwkhtmltox.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libwkhtmltox.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libwkhtmltox.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libwkhtmltox.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/wkhtmltox.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.12.6.0"}, "runtimes/win-x86/native/wkhtmltox.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.12.6.0"}}, "compile": {"lib/netstandard2.0/WkHtmlToPdfDotNet.dll": {}}}, "HtmlToOpenXml.dll/3.1.0": {"dependencies": {"AngleSharp": "1.3.0", "DocumentFormat.OpenXml": "3.1.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/HtmlToOpenXml.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/HtmlToOpenXml.dll": {}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "********", "fileVersion": "2.14.1.48190"}}, "compile": {"lib/net6.0/Humanizer.dll": {}}}, "IP2Region.Ex/1.2.0": {"runtime": {"lib/netstandard2.0/IP2Region.Ex.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/IP2Region.Ex.dll": {}}}, "IPTools.China/1.6.0": {"dependencies": {"IP2Region.Ex": "1.2.0", "IPTools.Core": "1.6.0"}, "runtime": {"lib/net5.0/IPTools.China.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/IPTools.China.dll": {}}}, "IPTools.Core/1.6.0": {"dependencies": {"IP2Region.Ex": "1.2.0"}, "runtime": {"lib/net5.0/IPTools.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/IPTools.Core.dll": {}}}, "IPTools.International/1.6.0": {"dependencies": {"IPTools.Core": "1.6.0", "MaxMind.GeoIP2": "3.0.0"}, "runtime": {"lib/net5.0/IPTools.International.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/IPTools.International.dll": {}}}, "Json.More.Net/2.0.2": {"runtime": {"lib/net8.0/Json.More.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Json.More.dll": {}}}, "JsonPointer.Net/5.0.2": {"dependencies": {"Humanizer.Core": "2.14.1", "Json.More.Net": "2.0.2"}, "runtime": {"lib/net8.0/JsonPointer.Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/JsonPointer.Net.dll": {}}}, "JsonSchema.Net/7.0.4": {"dependencies": {"JsonPointer.Net": "5.0.2"}, "runtime": {"lib/net8.0/JsonSchema.Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/JsonSchema.Net.dll": {}}}, "Lazy.Captcha.Core/2.2.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.0", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "SkiaSharp": "3.119.0", "SkiaSharp.NativeAssets.Linux.NoDependencies": "3.119.0"}, "runtime": {"lib/netstandard2.0/Lazy.Captcha.Core.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.0"}}, "compile": {"lib/netstandard2.0/Lazy.Captcha.Core.dll": {}}}, "log4net/3.1.0": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.1"}, "runtime": {"lib/netstandard2.0/log4net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/log4net.dll": {}}}, "Magicodes.IE.Core/2.7.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyModel": "8.0.2", "SixLabors.ImageSharp": "3.1.8", "System.ComponentModel.Annotations": "4.7.0"}, "runtime": {"lib/net8.0/Magicodes.IE.Core.dll": {"assemblyVersion": "2.7.6.0", "fileVersion": "2.7.6.0"}}, "resources": {"lib/net8.0/zh-Hans/Magicodes.IE.Core.resources.dll": {"locale": "zh-Hans"}}, "compile": {"lib/net8.0/Magicodes.IE.Core.dll": {}}}, "Magicodes.IE.EPPlus/2.7.6": {"dependencies": {"SixLabors.ImageSharp": "3.1.8", "SkiaSharp": "3.119.0", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/net8.0/Magicodes.IE.EPPlus.dll": {"assemblyVersion": "2.7.6.0", "fileVersion": "2.7.6.0"}}, "compile": {"lib/net8.0/Magicodes.IE.EPPlus.dll": {}}}, "Magicodes.IE.Excel/2.7.6": {"dependencies": {"DynamicExpresso.Core": "2.3.3", "Magicodes.IE.Core": "2.7.6", "Magicodes.IE.EPPlus": "2.7.6", "SkiaSharp.NativeAssets.Linux.NoDependencies": "3.119.0", "System.Linq.Dynamic.Core": "1.6.6"}, "runtime": {"lib/net8.0/Magicodes.IE.Excel.dll": {"assemblyVersion": "2.7.6.0", "fileVersion": "2.7.6.0"}}, "compile": {"lib/net8.0/Magicodes.IE.Excel.dll": {}}}, "Magicodes.IE.Html/2.7.6": {"dependencies": {"Magicodes.IE.Core": "2.7.6", "Magicodes.RazorEngine.NetCore": "2.2.0"}, "runtime": {"lib/net8.0/Magicodes.IE.Html.dll": {"assemblyVersion": "2.7.6.0", "fileVersion": "2.7.6.0"}}, "compile": {"lib/net8.0/Magicodes.IE.Html.dll": {}}}, "Magicodes.IE.Pdf/2.7.6": {"dependencies": {"Haukcode.WkHtmlToPdfDotNet": "1.5.95", "Magicodes.IE.Html": "2.7.6"}, "runtime": {"lib/net8.0/Magicodes.IE.Pdf.dll": {"assemblyVersion": "2.7.6.0", "fileVersion": "2.7.6.0"}}, "compile": {"lib/net8.0/Magicodes.IE.Pdf.dll": {}}}, "Magicodes.IE.Word/2.7.6": {"dependencies": {"HtmlToOpenXml.dll": "3.1.0", "Magicodes.IE.Html": "2.7.6"}, "runtime": {"lib/net8.0/Magicodes.IE.Word.dll": {"assemblyVersion": "2.7.6.0", "fileVersion": "2.7.6.0"}}, "compile": {"lib/net8.0/Magicodes.IE.Word.dll": {}}}, "Magicodes.RazorEngine.NetCore/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.36", "Microsoft.AspNetCore.Razor.Runtime": "2.2.0", "Microsoft.CSharp": "4.7.0", "Microsoft.CodeAnalysis.CSharp": "4.9.2", "System.Reflection.Emit": "4.3.0", "System.Security.Permissions": "8.0.0"}, "runtime": {"lib/netstandard2.1/RazorEngine.NetCore.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}, "compile": {"lib/netstandard2.1/RazorEngine.NetCore.dll": {}}}, "MailKit/4.13.0": {"dependencies": {"MimeKit": "4.13.0", "System.Formats.Asn1": "8.0.2"}, "runtime": {"lib/net8.0/MailKit.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/net8.0/MailKit.dll": {}}}, "Mapster/7.4.0": {"dependencies": {"Mapster.Core": "1.2.1"}, "runtime": {"lib/net7.0/Mapster.dll": {"assemblyVersion": "7.4.0.0", "fileVersion": "7.4.0.0"}}, "compile": {"lib/net7.0/Mapster.dll": {}}}, "Mapster.Core/1.2.1": {"runtime": {"lib/net7.0/Mapster.Core.dll": {"assemblyVersion": "1.2.1.0", "fileVersion": "1.2.1.0"}}, "compile": {"lib/net7.0/Mapster.Core.dll": {}}}, "Mapster.DependencyInjection/1.0.1": {"dependencies": {"Mapster": "7.4.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "runtime": {"lib/net7.0/Mapster.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net7.0/Mapster.DependencyInjection.dll": {}}}, "Markdig.Signed/0.33.0": {"runtime": {"lib/net6.0/Markdig.Signed.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/net6.0/Markdig.Signed.dll": {}}}, "MaxMind.Db/2.4.0": {"runtime": {"lib/netstandard2.0/MaxMind.Db.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/MaxMind.Db.dll": {}}}, "MaxMind.GeoIP2/3.0.0": {"dependencies": {"MaxMind.Db": "2.4.0", "Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/MaxMind.GeoIP2.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/MaxMind.GeoIP2.dll": {}}}, "MessagePack/2.5.108": {"dependencies": {"MessagePack.Annotations": "2.5.108", "Microsoft.NET.StringTools": "17.4.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/MessagePack.dll": {"assemblyVersion": "*******", "fileVersion": "2.5.108.51978"}}, "compile": {"lib/net6.0/MessagePack.dll": {}}}, "MessagePack.Annotations/2.5.108": {"runtime": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "2.5.108.51978"}}, "compile": {"lib/netstandard2.0/MessagePack.Annotations.dll": {}}}, "Microsoft.ApplicationInsights/2.21.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"assemblyVersion": "2.21.0.429", "fileVersion": "2.21.0.429"}}, "compile": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.18": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1825.31706"}}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {}}}, "Microsoft.AspNetCore.Connections.Abstractions/8.0.11": {"dependencies": {"Microsoft.Extensions.Features": "8.0.11", "System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1124.52116"}}}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.11": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1124.52116"}}}, "Microsoft.AspNetCore.DataProtection/8.0.11": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "8.0.11", "Microsoft.AspNetCore.DataProtection.Abstractions": "8.0.11", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "System.Security.Cryptography.Xml": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1124.52116"}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/8.0.11": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1124.52116"}}}, "Microsoft.AspNetCore.DataProtection.StackExchangeRedis/8.0.11": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "8.0.11", "StackExchange.Redis": "2.7.27"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.DataProtection.StackExchangeRedis.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1124.52116"}}, "compile": {"lib/net8.0/Microsoft.AspNetCore.DataProtection.StackExchangeRedis.dll": {}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "8.0.0"}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.1"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.AspNetCore.JsonPatch/8.0.18": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1825.31706"}}, "compile": {"lib/net8.0/Microsoft.AspNetCore.JsonPatch.dll": {}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/8.0.18": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "8.0.18", "Newtonsoft.Json": "13.0.3", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1825.31706"}}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {}}}, "Microsoft.AspNetCore.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Razor.Language/6.0.36": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "6.0.36.0", "fileVersion": "6.0.3624.51604"}}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0", "Microsoft.AspNetCore.Razor": "2.2.0"}}, "Microsoft.AspNetCore.SignalR.Common/8.0.11": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "8.0.11", "Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1124.52116"}}}, "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson/8.0.11": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "8.0.11", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1124.52116"}}, "compile": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson.dll": {}}}, "Microsoft.AspNetCore.SignalR.StackExchangeRedis/8.0.11": {"dependencies": {"MessagePack": "2.5.108", "Microsoft.Extensions.Options": "9.0.0", "StackExchange.Redis": "2.7.27"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.StackExchangeRedis.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1124.52116"}}, "compile": {"lib/net8.0/Microsoft.AspNetCore.SignalR.StackExchangeRedis.dll": {}}}, "Microsoft.AspNetCore.StaticFiles/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.WebEncoders": "2.2.0"}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.Common/4.9.2": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.900.224.12906"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {}}}, "Microsoft.CodeAnalysis.CSharp/4.9.2": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.9.2"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.900.224.12906"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/5.2.2": {"dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Data.SqlClient.SNI.runtime": "5.2.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.IdentityModel.JsonWebTokens": "7.1.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.1", "System.Runtime.Caching": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}}, "resources": {"lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}, "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}}, "compile": {"ref/net8.0/Microsoft.Data.SqlClient.dll": {}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.Data.Sqlite/9.0.0": {"dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.0", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10"}}, "Microsoft.Data.Sqlite.Core/9.0.0": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52902"}}, "compile": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {}}}, "Microsoft.Extensions.ApiDescription.Server/8.0.0": {}, "Microsoft.Extensions.Caching.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {}}}, "Microsoft.Extensions.Caching.Memory/9.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}}, "Microsoft.Extensions.DependencyModel/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "8.0.0.2", "fileVersion": "8.0.1024.46610"}}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Features/8.0.11": {"runtime": {"lib/net8.0/Microsoft.Extensions.Features.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1124.52116"}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {}}}, "Microsoft.Extensions.ObjectPool/8.0.17": {"runtime": {"lib/net8.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}}}, "Microsoft.Extensions.Options/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}}, "Microsoft.Extensions.Primitives/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {}}}, "Microsoft.Extensions.WebEncoders/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "System.Text.Encodings.Web": "8.0.0"}}, "Microsoft.Identity.Client/4.61.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.1.2", "System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}, "compile": {"lib/net6.0/Microsoft.Identity.Client.dll": {}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}, "compile": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {}}}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {}}}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}, "compile": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {}}}, "Microsoft.IdentityModel.Logging/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {}}}, "Microsoft.IdentityModel.Protocols/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.1.2", "System.IdentityModel.Tokens.Jwt": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {}}}, "Microsoft.IdentityModel.Tokens/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}, "compile": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {}}}, "Microsoft.Management.Infrastructure/3.0.0": {"dependencies": {"Microsoft.Management.Infrastructure.Runtime.Unix": "3.0.0", "Microsoft.Management.Infrastructure.Runtime.Win": "3.0.0"}, "compile": {"ref/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {}, "ref/netstandard1.6/Microsoft.Management.Infrastructure.dll": {}}}, "Microsoft.Management.Infrastructure.CimCmdlets/7.4.11": {"dependencies": {"System.Management.Automation": "7.4.11"}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Management.Infrastructure.CimCmdlets.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}}, "compile": {"ref/net8.0/Microsoft.Management.Infrastructure.CimCmdlets.dll": {}}}, "Microsoft.Management.Infrastructure.Runtime.Unix/3.0.0": {"runtimeTargets": {"runtimes/unix/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Management.Infrastructure.Runtime.Win/3.0.0": {"runtimeTargets": {"runtimes/win-arm64/lib/netstandard1.6/microsoft.management.infrastructure.dll": {"rid": "win-arm64", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "10.0.17763.30000"}, "runtimes/win-arm64/lib/netstandard1.6/microsoft.management.infrastructure.native.dll": {"rid": "win-arm64", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "10.0.17763.30000"}, "runtimes/win-x64/lib/netstandard1.6/microsoft.management.infrastructure.dll": {"rid": "win-x64", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "10.0.17763.30000"}, "runtimes/win-x64/lib/netstandard1.6/microsoft.management.infrastructure.native.dll": {"rid": "win-x64", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "10.0.17763.30000"}, "runtimes/win-x86/lib/netstandard1.6/microsoft.management.infrastructure.dll": {"rid": "win-x86", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "10.0.17763.30000"}, "runtimes/win-x86/lib/netstandard1.6/microsoft.management.infrastructure.native.dll": {"rid": "win-x86", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "10.0.17763.30000"}, "runtimes/win-arm64/native/microsoft.management.infrastructure.native.unmanaged.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "10.0.17763.30000"}, "runtimes/win-x64/native/microsoft.management.infrastructure.native.unmanaged.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.3.9600.18144"}, "runtimes/win-x86/native/microsoft.management.infrastructure.native.unmanaged.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "6.3.9600.18144"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0", "System.Buffers": "4.5.1"}}, "Microsoft.NET.StringTools/17.4.0": {"dependencies": {"System.Memory": "4.6.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.NET.StringTools.dll": {"assemblyVersion": "*******", "fileVersion": "17.4.0.51802"}}}, "Microsoft.NETCore.Platforms/1.1.1": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.OpenApi/1.6.23": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.23.0", "fileVersion": "1.6.23.0"}}, "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {}}}, "Microsoft.PowerShell.Commands.Diagnostics/7.4.11": {"dependencies": {"System.Management.Automation": "7.4.11"}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.PowerShell.Commands.Diagnostics.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}}, "compile": {"ref/net8.0/Microsoft.PowerShell.Commands.Diagnostics.dll": {}}}, "Microsoft.PowerShell.Commands.Management/7.4.11": {"dependencies": {"Microsoft.PowerShell.Security": "7.4.11", "System.Diagnostics.EventLog": "8.0.2", "System.ServiceProcess.ServiceController": "8.0.1"}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.PowerShell.Commands.Management.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}, "runtimes/win/lib/net8.0/Microsoft.PowerShell.Commands.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}}, "compile": {"ref/net8.0/Microsoft.PowerShell.Commands.Management.dll": {}}}, "Microsoft.PowerShell.Commands.Utility/7.4.11": {"dependencies": {"Json.More.Net": "2.0.2", "JsonPointer.Net": "5.0.2", "JsonSchema.Net": "7.0.4", "Markdig.Signed": "0.33.0", "Microsoft.CodeAnalysis.CSharp": "4.9.2", "Microsoft.PowerShell.MarkdownRender": "7.2.1", "System.Drawing.Common": "8.0.17", "System.Management.Automation": "7.4.11", "System.Threading.AccessControl": "8.0.0"}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.PowerShell.Commands.Utility.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}, "runtimes/win/lib/net8.0/Microsoft.PowerShell.Commands.Utility.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}}, "compile": {"ref/net8.0/Microsoft.PowerShell.Commands.Utility.dll": {}}}, "Microsoft.PowerShell.ConsoleHost/7.4.11": {"dependencies": {"System.Management.Automation": "7.4.11"}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.PowerShell.ConsoleHost.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}, "runtimes/win/lib/net8.0/Microsoft.PowerShell.ConsoleHost.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}}, "compile": {"ref/net8.0/Microsoft.PowerShell.ConsoleHost.dll": {}}}, "Microsoft.PowerShell.CoreCLR.Eventing/7.4.11": {"dependencies": {"System.Diagnostics.EventLog": "8.0.2"}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.PowerShell.CoreCLR.Eventing.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}}, "compile": {"ref/net8.0/Microsoft.PowerShell.CoreCLR.Eventing.dll": {}}}, "Microsoft.PowerShell.MarkdownRender/7.2.1": {"dependencies": {"Markdig.Signed": "0.33.0"}, "runtime": {"lib/netstandard2.0/Microsoft.PowerShell.MarkdownRender.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Microsoft.PowerShell.MarkdownRender.dll": {}}}, "Microsoft.PowerShell.Native/7.4.0": {"runtimeTargets": {"runtimes/linux-arm/native/libpsl-native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libpsl-native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libpsl-native.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libpsl-native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libpsl-native.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/PowerShell.Core.Instrumentation.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-arm/native/pwrshplugin.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-arm64/native/PowerShell.Core.Instrumentation.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-arm64/native/pwrshplugin.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-x64/native/PowerShell.Core.Instrumentation.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-x64/native/pwrshplugin.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-x86/native/PowerShell.Core.Instrumentation.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-x86/native/pwrshplugin.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "10.0.10011.16384"}}}, "Microsoft.PowerShell.SDK/7.4.11": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.ObjectPool": "8.0.17", "Microsoft.Management.Infrastructure.CimCmdlets": "7.4.11", "Microsoft.PowerShell.Commands.Diagnostics": "7.4.11", "Microsoft.PowerShell.Commands.Management": "7.4.11", "Microsoft.PowerShell.Commands.Utility": "7.4.11", "Microsoft.PowerShell.ConsoleHost": "7.4.11", "Microsoft.PowerShell.Security": "7.4.11", "Microsoft.WSMan.Management": "7.4.11", "Microsoft.Windows.Compatibility": "8.0.17", "System.Data.SqlClient": "4.9.0", "System.Diagnostics.EventLog": "8.0.2", "System.DirectoryServices.Protocols": "8.0.2", "System.Drawing.Common": "8.0.17", "System.IO.Packaging": "8.0.1", "System.Management.Automation": "7.4.11", "System.Net.Http.WinHttpHandler": "8.0.3", "System.Private.ServiceModel": "4.10.3", "System.Runtime.Caching": "8.0.1", "System.ServiceModel.Duplex": "4.10.3", "System.ServiceModel.Http": "4.10.3", "System.ServiceModel.NetTcp": "4.10.3", "System.ServiceModel.Primitives": "4.10.3", "System.ServiceModel.Security": "4.10.3", "System.Text.Encodings.Web": "8.0.0", "System.Web.Services.Description": "4.10.3"}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.PowerShell.SDK.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}, "runtimes/win/lib/net8.0/Microsoft.PowerShell.SDK.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}}, "compile": {"ref/net8.0/Microsoft.PowerShell.Commands.Management.dll": {}, "ref/net8.0/Microsoft.PowerShell.Commands.Utility.dll": {}, "ref/net8.0/Microsoft.PowerShell.ConsoleHost.dll": {}, "ref/net8.0/System.Management.Automation.dll": {}}}, "Microsoft.PowerShell.Security/7.4.11": {"dependencies": {"System.Management.Automation": "7.4.11"}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.PowerShell.Security.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}, "runtimes/win/lib/net8.0/Microsoft.PowerShell.Security.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}}, "compile": {"ref/net8.0/Microsoft.PowerShell.Security.dll": {}}}, "Microsoft.Security.Extensions/1.4.0": {"runtimeTargets": {"runtimes/win-arm64/lib/net5.0/getfilesiginforedistwrapper.dll": {"rid": "win-arm64", "assetType": "runtime", "assemblyVersion": "10.0.0.0", "fileVersion": "10.0.27797.1000"}, "runtimes/win-x64/lib/net5.0/getfilesiginforedistwrapper.dll": {"rid": "win-x64", "assetType": "runtime", "assemblyVersion": "10.0.0.0", "fileVersion": "10.0.27797.1000"}, "runtimes/win-x86/lib/net5.0/getfilesiginforedistwrapper.dll": {"rid": "win-x86", "assetType": "runtime", "assemblyVersion": "10.0.0.0", "fileVersion": "10.0.27797.1000"}, "runtimes/win-arm64/native/getfilesiginforedist.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/getfilesiginforedist.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/getfilesiginforedist.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}, "compile": {"ref/netstandard2.0/getfilesiginforedistwrapper.dll": {}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {}}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.1", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.Registry.AccessControl/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.Registry.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/Microsoft.Win32.Registry.AccessControl.dll": {}}}, "Microsoft.Win32.SystemEvents/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {}}}, "Microsoft.Windows.Compatibility/8.0.17": {"dependencies": {"Microsoft.Win32.Registry.AccessControl": "8.0.0", "Microsoft.Win32.SystemEvents": "8.0.0", "System.CodeDom": "8.0.0", "System.ComponentModel.Composition": "8.0.0", "System.ComponentModel.Composition.Registration": "8.0.0", "System.Configuration.ConfigurationManager": "8.0.1", "System.Data.Odbc": "8.0.1", "System.Data.OleDb": "8.0.1", "System.Data.SqlClient": "4.9.0", "System.Diagnostics.EventLog": "8.0.2", "System.Diagnostics.PerformanceCounter": "8.0.1", "System.DirectoryServices": "8.0.0", "System.DirectoryServices.AccountManagement": "8.0.1", "System.DirectoryServices.Protocols": "8.0.2", "System.Drawing.Common": "8.0.17", "System.IO.Packaging": "8.0.1", "System.IO.Ports": "8.0.0", "System.Management": "8.0.0", "System.Reflection.Context": "8.0.0", "System.Runtime.Caching": "8.0.1", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.ProtectedData": "8.0.0", "System.Security.Cryptography.Xml": "8.0.2", "System.Security.Permissions": "8.0.0", "System.ServiceModel.Duplex": "4.10.3", "System.ServiceModel.Http": "4.10.3", "System.ServiceModel.NetTcp": "4.10.3", "System.ServiceModel.Primitives": "4.10.3", "System.ServiceModel.Security": "4.10.3", "System.ServiceModel.Syndication": "8.0.0", "System.ServiceProcess.ServiceController": "8.0.1", "System.Speech": "8.0.0", "System.Text.Encoding.CodePages": "8.0.0", "System.Threading.AccessControl": "8.0.0", "System.Web.Services.Description": "4.10.3"}}, "Microsoft.WSMan.Management/7.4.11": {"dependencies": {"Microsoft.WSMan.Runtime": "7.4.11", "System.Diagnostics.EventLog": "8.0.2", "System.Management.Automation": "7.4.11", "System.ServiceProcess.ServiceController": "8.0.1"}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.WSMan.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}}, "compile": {"ref/net8.0/Microsoft.WSMan.Management.dll": {}}}, "Microsoft.WSMan.Runtime/7.4.11": {"runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.WSMan.Runtime.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}}, "compile": {"ref/net8.0/Microsoft.WSMan.Runtime.dll": {}}}, "MimeKit/4.13.0": {"dependencies": {"BouncyCastle.Cryptography": "2.6.1", "System.Security.Cryptography.Pkcs": "8.0.1"}, "runtime": {"lib/net8.0/MimeKit.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/net8.0/MimeKit.dll": {}}}, "MiniExcel/1.41.3": {"runtime": {"lib/net8.0/MiniExcel.dll": {"assemblyVersion": "1.41.3.0", "fileVersion": "1.41.3.0"}}, "compile": {"lib/net8.0/MiniExcel.dll": {}}}, "Minio/5.0.0": {"dependencies": {"CommunityToolkit.HighPerformance": "8.1.0", "System.IO.Hashing": "7.0.0", "System.Reactive.Linq": "5.0.0"}, "runtime": {"lib/net7.0/Minio.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net7.0/Minio.dll": {}}}, "MiniWord/0.9.2": {"dependencies": {"DocumentFormat.OpenXml": "3.1.1"}, "runtime": {"lib/netstandard2.0/MiniWord.dll": {"assemblyVersion": "0.9.2.0", "fileVersion": "0.9.2.0"}}, "compile": {"lib/netstandard2.0/MiniWord.dll": {}}}, "MongoDB.Bson/3.3.0": {"dependencies": {"System.Memory": "4.6.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/MongoDB.Bson.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.3.0.0"}}, "compile": {"lib/net6.0/MongoDB.Bson.dll": {}}}, "MongoDB.Driver/3.3.0": {"dependencies": {"DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "MongoDB.Bson": "3.3.0", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.7.3"}, "runtime": {"lib/net6.0/MongoDB.Driver.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.3.0.0"}}, "compile": {"lib/net6.0/MongoDB.Driver.dll": {}}}, "MySqlConnector/2.2.5": {"runtime": {"lib/net7.0/MySqlConnector.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.5.0"}}, "compile": {"lib/net7.0/MySqlConnector.dll": {}}}, "NewLife.Core/11.5.2025.701": {"runtime": {"lib/net8.0/NewLife.Core.dll": {"assemblyVersion": "11.5.2025.701", "fileVersion": "11.5.2025.701"}}, "compile": {"lib/net8.0/NewLife.Core.dll": {}}}, "NewLife.Redis/6.3.2025.701": {"dependencies": {"NewLife.Core": "11.5.2025.701"}, "runtime": {"lib/netstandard2.1/NewLife.Redis.dll": {"assemblyVersion": "6.3.2025.701", "fileVersion": "6.3.2025.701"}}, "compile": {"lib/netstandard2.1/NewLife.Redis.dll": {}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}, "compile": {"lib/net6.0/Newtonsoft.Json.dll": {}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}, "compile": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {}}}, "Novell.Directory.Ldap.NETStandard/4.0.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Novell.Directory.Ldap.NETStandard.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Novell.Directory.Ldap.NETStandard.dll": {}}}, "Npgsql/5.0.18": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net5.0/Npgsql.dll": {"assemblyVersion": "5.0.18.0", "fileVersion": "5.0.18.0"}}, "compile": {"lib/net5.0/Npgsql.dll": {}}}, "OnceMi.AspNetCore.OSS/1.2.0": {"dependencies": {"Aliyun.OSS.SDK.NetCore": "2.13.0", "BceSdkDotNetCore": "1.0.2.911", "Microsoft.AspNetCore.StaticFiles": "2.2.0", "Microsoft.CSharp": "4.7.0", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Options": "9.0.0", "Minio": "5.0.0", "Qiniu": "8.3.1", "Tencent.QCloud.Cos.Sdk": "5.4.34"}, "runtime": {"lib/netstandard2.1/OnceMi.AspNetCore.OSS.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.1/OnceMi.AspNetCore.OSS.dll": {}}}, "Oracle.ManagedDataAccess.Core/23.8.0": {"dependencies": {"System.Diagnostics.PerformanceCounter": "8.0.1", "System.DirectoryServices.Protocols": "8.0.2", "System.Formats.Asn1": "8.0.2", "System.Memory": "4.6.0", "System.Security.Cryptography.Pkcs": "8.0.1"}, "runtime": {"lib/net8.0/Oracle.ManagedDataAccess.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/net8.0/Oracle.ManagedDataAccess.dll": {}}}, "Oscar.Data.SqlClient/4.0.4": {"dependencies": {"System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/netstandard2.0/Oscar.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Oscar.Data.SqlClient.dll": {}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"dependencies": {"System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.8.1080"}}, "compile": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {}}}, "Portable.BouncyCastle/*******": {"runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {}}}, "Qiniu/8.3.1": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Qiniu.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Qiniu.dll": {}}}, "QRCoder/1.6.0": {"runtime": {"lib/net6.0/QRCoder.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/QRCoder.dll": {}}}, "RabbitMQ.Client/7.1.2": {"dependencies": {"System.IO.Pipelines": "8.0.0", "System.Threading.RateLimiting": "8.0.0"}, "runtime": {"lib/net8.0/RabbitMQ.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/RabbitMQ.Client.dll": {}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.linux-arm.runtime.native.System.IO.Ports/8.0.0": {"runtimeTargets": {"runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/8.0.0": {"runtimeTargets": {"runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/8.0.0": {"runtimeTargets": {"runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.IO.Ports/8.0.0": {"dependencies": {"runtime.linux-arm.runtime.native.System.IO.Ports": "8.0.0", "runtime.linux-arm64.runtime.native.System.IO.Ports": "8.0.0", "runtime.linux-x64.runtime.native.System.IO.Ports": "8.0.0", "runtime.osx-arm64.runtime.native.System.IO.Ports": "8.0.0", "runtime.osx-x64.runtime.native.System.IO.Ports": "8.0.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx-arm64.runtime.native.System.IO.Ports/8.0.0": {"runtimeTargets": {"runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/8.0.0": {"runtimeTargets": {"runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "SharpCompress/0.30.1": {"runtime": {"lib/net5.0/SharpCompress.dll": {"assemblyVersion": "0.30.1.0", "fileVersion": "0.30.1.0"}}, "compile": {"lib/net5.0/SharpCompress.dll": {}}}, "SixLabors.ImageSharp/3.1.8": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.8.0"}}, "compile": {"lib/net6.0/SixLabors.ImageSharp.dll": {}}}, "SixLabors.ImageSharp.Web/3.1.5": {"dependencies": {"Microsoft.IO.RecyclableMemoryStream": "3.0.1", "SixLabors.ImageSharp": "3.1.8"}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.Web.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.5.0"}}, "compile": {"lib/net6.0/SixLabors.ImageSharp.Web.dll": {}}}, "SkiaSharp/3.119.0": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "3.119.0", "SkiaSharp.NativeAssets.macOS": "3.119.0"}, "runtime": {"lib/net8.0/SkiaSharp.dll": {"assemblyVersion": "3.119.0.0", "fileVersion": "3.119.0.0"}}, "compile": {"ref/net8.0/SkiaSharp.dll": {}}}, "SkiaSharp.NativeAssets.Linux.NoDependencies/3.119.0": {"runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-loongarch64/native/libSkiaSharp.so": {"rid": "linux-loongarch64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libSkiaSharp.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libSkiaSharp.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-loongarch64/native/libSkiaSharp.so": {"rid": "linux-musl-loongarch64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-riscv64/native/libSkiaSharp.so": {"rid": "linux-musl-riscv64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-riscv64/native/libSkiaSharp.so": {"rid": "linux-riscv64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libSkiaSharp.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/3.119.0": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/3.119.0": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SKIT.FlurlHttpClient.Common/3.1.1": {"dependencies": {"Flurl": "4.0.0", "Flurl.Http": "4.0.2", "Newtonsoft.Json": "13.0.3", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/SKIT.FlurlHttpClient.Common.dll": {"assemblyVersion": "3.1.1.0", "fileVersion": "3.1.1.0"}}, "compile": {"lib/net8.0/SKIT.FlurlHttpClient.Common.dll": {}}}, "SKIT.FlurlHttpClient.Wechat.Api/3.11.0": {"dependencies": {"BouncyCastle.Cryptography": "2.6.1", "SKIT.FlurlHttpClient.Common": "3.1.1"}, "runtime": {"lib/net8.0/SKIT.FlurlHttpClient.Wechat.Api.dll": {"assemblyVersion": "3.11.0.0", "fileVersion": "3.11.0.0"}}, "compile": {"lib/net8.0/SKIT.FlurlHttpClient.Wechat.Api.dll": {}}}, "SKIT.FlurlHttpClient.Wechat.TenpayV3/3.13.0": {"dependencies": {"BouncyCastle.Cryptography": "2.6.1", "SKIT.FlurlHttpClient.Common": "3.1.1"}, "runtime": {"lib/net8.0/SKIT.FlurlHttpClient.Wechat.TenpayV3.dll": {"assemblyVersion": "3.13.0.0", "fileVersion": "3.13.0.0"}}, "compile": {"lib/net8.0/SKIT.FlurlHttpClient.Wechat.TenpayV3.dll": {}}}, "Snappier/1.0.0": {"runtime": {"lib/net5.0/Snappier.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/Snappier.dll": {}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}}, "SQLitePCLRaw.core/2.1.10": {"dependencies": {"System.Memory": "4.6.0"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"runtimeTargets": {"runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-s390x/native/libe_sqlite3.so": {"rid": "linux-musl-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"rid": "linux-ppc64le", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}, "compile": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}}, "SqlSugar.MongoDbCore/*********": {"dependencies": {"MongoDB.Driver": "3.3.0"}, "runtime": {"lib/net6.0/MongoDb.Ado.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/SqlSugar.MongoDbCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/MongoDb.Ado.Data.dll": {}, "lib/net6.0/SqlSugar.MongoDbCore.dll": {}}}, "SqlSugarCore/*********": {"dependencies": {"Microsoft.Data.SqlClient": "5.2.2", "Microsoft.Data.Sqlite": "9.0.0", "MySqlConnector": "2.2.5", "Newtonsoft.Json": "13.0.3", "Npgsql": "5.0.18", "Oracle.ManagedDataAccess.Core": "23.8.0", "Oscar.Data.SqlClient": "4.0.4", "SqlSugarCore.Dm": "8.8.0", "SqlSugarCore.Kdbndp": "9.3.7.613", "System.Data.Common": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Text.RegularExpressions": "4.3.1"}, "runtime": {"lib/netstandard2.1/SqlSugar.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "compile": {"lib/netstandard2.1/SqlSugar.dll": {}}}, "SqlSugarCore.Dm/8.8.0": {"runtime": {"lib/netstandard2.1/DM.DmProvider.dll": {"assemblyVersion": "8.3.1.33449", "fileVersion": "8.3.1.33449"}}, "compile": {"lib/netstandard2.1/DM.DmProvider.dll": {}}}, "SqlSugarCore.Kdbndp/9.3.7.613": {"runtime": {"lib/netstandard2.1/Kdbndp.dll": {"assemblyVersion": "9.3.7.613", "fileVersion": "9.3.7.613"}}, "compile": {"lib/netstandard2.1/Kdbndp.dll": {}}}, "SSH.NET/2025.0.0": {"dependencies": {"BouncyCastle.Cryptography": "2.6.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Renci.SshNet.dll": {"assemblyVersion": "2025.0.0.1", "fileVersion": "2025.0.0.1"}}, "compile": {"lib/net8.0/Renci.SshNet.dll": {}}}, "StackExchange.Redis/2.7.27": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Pipelines.Sockets.Unofficial": "2.2.8"}, "runtime": {"lib/net6.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.7.27.49176"}}, "compile": {"lib/net6.0/StackExchange.Redis.dll": {}}}, "Swashbuckle.AspNetCore/9.0.3": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "8.0.0", "Swashbuckle.AspNetCore.Swagger": "9.0.3", "Swashbuckle.AspNetCore.SwaggerGen": "9.0.3", "Swashbuckle.AspNetCore.SwaggerUI": "9.0.3"}}, "Swashbuckle.AspNetCore.Swagger/9.0.3": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}, "compile": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.3": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "9.0.3"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}, "compile": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.3": {"runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}, "compile": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {}}}, "System.Buffers/4.5.1": {}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}, "compile": {"lib/net6.0/System.ClientModel.dll": {}}}, "System.CodeDom/8.0.0": {"runtime": {"lib/net8.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/System.CodeDom.dll": {}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/8.0.0": {}, "System.ComponentModel.Annotations/4.7.0": {}, "System.ComponentModel.Composition/8.0.0": {"runtime": {"lib/net8.0/System.ComponentModel.Composition.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/System.ComponentModel.Composition.dll": {}}}, "System.ComponentModel.Composition.Registration/8.0.0": {"dependencies": {"System.ComponentModel.Composition": "8.0.0", "System.Reflection.Context": "8.0.0"}, "runtime": {"lib/net8.0/System.ComponentModel.Composition.Registration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/System.ComponentModel.Composition.Registration.dll": {}}}, "System.Configuration.ConfigurationManager/8.0.1": {"dependencies": {"System.Diagnostics.EventLog": "8.0.2", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "compile": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {}}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Data.Odbc/8.0.1": {"runtime": {"lib/net8.0/System.Data.Odbc.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/freebsd/lib/net8.0/System.Data.Odbc.dll": {"rid": "freebsd", "assetType": "runtime", "assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}, "runtimes/illumos/lib/net8.0/System.Data.Odbc.dll": {"rid": "illumos", "assetType": "runtime", "assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}, "runtimes/ios/lib/net8.0/System.Data.Odbc.dll": {"rid": "ios", "assetType": "runtime", "assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}, "runtimes/linux/lib/net8.0/System.Data.Odbc.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}, "runtimes/osx/lib/net8.0/System.Data.Odbc.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}, "runtimes/solaris/lib/net8.0/System.Data.Odbc.dll": {"rid": "solaris", "assetType": "runtime", "assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}, "runtimes/tvos/lib/net8.0/System.Data.Odbc.dll": {"rid": "tvos", "assetType": "runtime", "assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}, "runtimes/win/lib/net8.0/System.Data.Odbc.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}, "compile": {"lib/net8.0/System.Data.Odbc.dll": {}}}, "System.Data.OleDb/8.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.1", "System.Diagnostics.PerformanceCounter": "8.0.1"}, "runtime": {"lib/net8.0/System.Data.OleDb.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Data.OleDb.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}, "compile": {"lib/net8.0/System.Data.OleDb.dll": {}}}, "System.Data.SqlClient/4.9.0": {"dependencies": {"runtime.native.System.Data.SqlClient.sni": "4.4.0"}, "runtime": {"lib/net8.0/System.Data.SqlClient.dll": {"assemblyVersion": "4.6.1.6", "fileVersion": "4.900.24.56208"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "4.6.1.6", "fileVersion": "4.900.24.56208"}, "runtimes/win/lib/net8.0/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.6.1.6", "fileVersion": "4.900.24.56208"}}, "compile": {"lib/net8.0/System.Data.SqlClient.dll": {}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.DiagnosticSource/9.0.0": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "compile": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {}}}, "System.Diagnostics.EventLog/8.0.2": {"runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1525.16413"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1525.16413"}}}, "System.Diagnostics.PerformanceCounter/8.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.1"}, "runtime": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "compile": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {}}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.DirectoryServices/8.0.0": {"runtime": {"lib/net8.0/System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.DirectoryServices.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/System.DirectoryServices.dll": {}}}, "System.DirectoryServices.AccountManagement/8.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.1", "System.DirectoryServices": "8.0.0", "System.DirectoryServices.Protocols": "8.0.2"}, "runtime": {"lib/net8.0/System.DirectoryServices.AccountManagement.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.DirectoryServices.AccountManagement.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}, "compile": {"lib/net8.0/System.DirectoryServices.AccountManagement.dll": {}}}, "System.DirectoryServices.Protocols/8.0.2": {"runtime": {"lib/net8.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "8.0.0.2", "fileVersion": "8.0.1725.26602"}}, "runtimeTargets": {"runtimes/linux/lib/net8.0/System.DirectoryServices.Protocols.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "8.0.0.2", "fileVersion": "8.0.1725.26602"}, "runtimes/osx/lib/net8.0/System.DirectoryServices.Protocols.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "8.0.0.2", "fileVersion": "8.0.1725.26602"}, "runtimes/win/lib/net8.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "8.0.0.2", "fileVersion": "8.0.1725.26602"}}, "compile": {"lib/net8.0/System.DirectoryServices.Protocols.dll": {}}}, "System.Drawing.Common/8.0.17": {"dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}}, "compile": {"lib/net8.0/System.Drawing.Common.dll": {}}}, "System.Formats.Asn1/8.0.2": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}, "compile": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.IO.Hashing/7.0.0": {"runtime": {"lib/net7.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "compile": {"lib/net7.0/System.IO.Hashing.dll": {}}}, "System.IO.Packaging/8.0.1": {"runtime": {"lib/net8.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "compile": {"lib/net8.0/System.IO.Packaging.dll": {}}}, "System.IO.Pipelines/8.0.0": {}, "System.IO.Ports/8.0.0": {"dependencies": {"runtime.native.System.IO.Ports": "8.0.0"}, "runtime": {"lib/net8.0/System.IO.Ports.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.IO.Ports.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/win/lib/net8.0/System.IO.Ports.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/System.IO.Ports.dll": {}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/1.6.6": {"runtime": {"lib/net8.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/System.Linq.Dynamic.Core.dll": {}}}, "System.Management/8.0.0": {"dependencies": {"System.CodeDom": "8.0.0"}, "runtime": {"lib/net8.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/System.Management.dll": {}}}, "System.Management.Automation/7.4.11": {"dependencies": {"Microsoft.ApplicationInsights": "2.21.0", "Microsoft.Management.Infrastructure": "3.0.0", "Microsoft.PowerShell.CoreCLR.Eventing": "7.4.11", "Microsoft.PowerShell.Native": "7.4.0", "Microsoft.Security.Extensions": "1.4.0", "Microsoft.Win32.Registry.AccessControl": "8.0.0", "Newtonsoft.Json": "13.0.3", "System.Configuration.ConfigurationManager": "8.0.1", "System.Diagnostics.DiagnosticSource": "9.0.0", "System.Diagnostics.EventLog": "8.0.2", "System.DirectoryServices": "8.0.0", "System.Formats.Asn1": "8.0.2", "System.Management": "8.0.0", "System.Security.AccessControl": "6.0.1", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Permissions": "8.0.0", "System.Text.Encoding.CodePages": "8.0.0"}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.Management.Automation.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}, "runtimes/win/lib/net8.0/System.Management.Automation.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.4.6.500", "fileVersion": "7.4.11.500"}}, "compile": {"ref/net8.0/System.Management.Automation.dll": {}}}, "System.Memory/4.6.0": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}, "compile": {"lib/netstandard2.0/System.Memory.Data.dll": {}}}, "System.Net.Http/4.3.4": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "9.0.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Http.WinHttpHandler/8.0.3": {"runtime": {"lib/net8.0/System.Net.Http.WinHttpHandler.dll": {"assemblyVersion": "8.0.0.3", "fileVersion": "8.0.1625.21506"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Net.Http.WinHttpHandler.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "8.0.0.3", "fileVersion": "8.0.1625.21506"}}, "compile": {"lib/net8.0/System.Net.Http.WinHttpHandler.dll": {}}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Numerics.Vectors/4.5.0": {}, "System.Private.ServiceModel/4.10.3": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.ObjectPool": "8.0.17", "System.Numerics.Vectors": "4.5.0", "System.Reflection.DispatchProxy": "4.7.1", "System.Security.Cryptography.Xml": "8.0.2", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Private.ServiceModel.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}, "resources": {"lib/netstandard2.0/cs/System.Private.ServiceModel.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Private.ServiceModel.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Private.ServiceModel.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Private.ServiceModel.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Private.ServiceModel.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Private.ServiceModel.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Private.ServiceModel.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Private.ServiceModel.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Private.ServiceModel.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Private.ServiceModel.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Private.ServiceModel.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Private.ServiceModel.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Private.ServiceModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Private.Uri/4.3.2": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Reactive/5.0.0": {"runtime": {"lib/net5.0/System.Reactive.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.0.1"}}, "compile": {"lib/net5.0/System.Reactive.dll": {}}}, "System.Reactive.Linq/5.0.0": {"dependencies": {"System.Reactive": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/System.Reactive.Linq.dll": {"assemblyVersion": "3.0.6000.0", "fileVersion": "3.0.6000.0"}}, "compile": {"lib/netstandard2.0/System.Reactive.Linq.dll": {}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Context/8.0.0": {"runtime": {"lib/net8.0/System.Reflection.Context.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/System.Reflection.Context.dll": {}}}, "System.Reflection.DispatchProxy/4.7.1": {}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Metadata/8.0.0": {"dependencies": {"System.Collections.Immutable": "8.0.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Reflection.TypeExtensions/4.7.0": {}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.Caching/8.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.1"}, "runtime": {"lib/net8.0/System.Runtime.Caching.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}, "compile": {"lib/net8.0/System.Runtime.Caching.dll": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.AccessControl/6.0.1": {}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Pkcs/8.0.1": {"runtime": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "compile": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {}}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/8.0.0": {"runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Xml/8.0.2": {"dependencies": {"System.Security.Cryptography.Pkcs": "8.0.1"}, "runtime": {"lib/net8.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Security.Permissions/8.0.0": {"dependencies": {"System.Windows.Extensions": "8.0.0"}, "runtime": {"lib/net8.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/System.Security.Permissions.dll": {}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.ServiceModel.Duplex/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Duplex.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}, "compile": {"ref/net6.0/System.ServiceModel.Duplex.dll": {}}}, "System.ServiceModel.Http/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Http.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}, "compile": {"ref/net6.0/System.ServiceModel.Http.dll": {}}}, "System.ServiceModel.NetTcp/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}, "compile": {"ref/net6.0/System.ServiceModel.NetTcp.dll": {}}}, "System.ServiceModel.Primitives/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}, "lib/net6.0/System.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.1000.323.51101"}}, "compile": {"ref/net6.0/System.ServiceModel.Primitives.dll": {}, "ref/net6.0/System.ServiceModel.dll": {}}}, "System.ServiceModel.Security/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Security.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}, "compile": {"ref/net6.0/System.ServiceModel.Security.dll": {}}}, "System.ServiceModel.Syndication/8.0.0": {"runtime": {"lib/net8.0/System.ServiceModel.Syndication.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/System.ServiceModel.Syndication.dll": {}}}, "System.ServiceProcess.ServiceController/8.0.1": {"dependencies": {"System.Diagnostics.EventLog": "8.0.2"}, "runtime": {"lib/net8.0/System.ServiceProcess.ServiceController.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.ServiceProcess.ServiceController.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}, "compile": {"lib/net8.0/System.ServiceProcess.ServiceController.dll": {}}}, "System.Speech/8.0.0": {"runtime": {"lib/net8.0/System.Speech.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Speech.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/System.Speech.dll": {}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encoding.CodePages/8.0.0": {}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.5": {}, "System.Text.RegularExpressions/4.3.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.AccessControl/8.0.0": {"runtime": {"lib/net8.0/System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Threading.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/System.Threading.AccessControl.dll": {}}}, "System.Threading.RateLimiting/8.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Web.Services.Description/4.10.3": {"runtime": {"lib/netstandard2.0/System.Web.Services.Description.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}, "resources": {"lib/netstandard2.0/cs/System.Web.Services.Description.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Web.Services.Description.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Web.Services.Description.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Web.Services.Description.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Web.Services.Description.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Web.Services.Description.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Web.Services.Description.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Web.Services.Description.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Web.Services.Description.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Web.Services.Description.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Web.Services.Description.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Web.Services.Description.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Web.Services.Description.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/netstandard2.0/System.Web.Services.Description.dll": {}}}, "System.Windows.Extensions/8.0.0": {"runtime": {"lib/net8.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/System.Windows.Extensions.dll": {}}}, "Tea/1.1.3": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Tea.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Tea.dll": {}}}, "Tencent.QCloud.Cos.Sdk/5.4.34": {"runtime": {"lib/netstandard2.0/COSXML.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.0/COSXML.dll": {}}}, "TencentCloudSDK.Common/3.0.1273": {"dependencies": {"Newtonsoft.Json": "13.0.3", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/netstandard2.0/TencentCloudCommon.dll": {"assemblyVersion": "3.0.1273.0", "fileVersion": "3.0.1273.0"}}, "compile": {"lib/netstandard2.0/TencentCloudCommon.dll": {}}}, "TencentCloudSDK.Sms/3.0.1273": {"dependencies": {"Newtonsoft.Json": "13.0.3", "System.Text.Encodings.Web": "8.0.0", "TencentCloudSDK.Common": "3.0.1273"}, "runtime": {"lib/netstandard2.0/TencentCloudSms.dll": {"assemblyVersion": "3.0.1273.0", "fileVersion": "3.0.1273.0"}}, "compile": {"lib/netstandard2.0/TencentCloudSms.dll": {}}}, "UAParser/3.1.47": {"runtime": {"lib/netcoreapp2.0/UAParser.dll": {"assemblyVersion": "3.1.47.0", "fileVersion": "3.1.47.0"}}, "compile": {"lib/netcoreapp2.0/UAParser.dll": {}}}, "Yitter.IdGenerator/1.0.14": {"runtime": {"lib/netstandard2.0/Yitter.IdGenerator.dll": {"assemblyVersion": "1.0.14.0", "fileVersion": "1.0.14.0"}}, "compile": {"lib/netstandard2.0/Yitter.IdGenerator.dll": {}}}, "ZstdSharp.Port/0.7.3": {"runtime": {"lib/net7.0/ZstdSharp.dll": {"assemblyVersion": "0.7.3.0", "fileVersion": "0.7.3.0"}}, "compile": {"lib/net7.0/ZstdSharp.dll": {}}}, "Microsoft.AspNetCore.Antiforgery/*******": {"compile": {"Microsoft.AspNetCore.Antiforgery.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.BearerToken/*******": {"compile": {"Microsoft.AspNetCore.Authentication.BearerToken.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Cookies.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Core/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication/*******": {"compile": {"Microsoft.AspNetCore.Authentication.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"compile": {"Microsoft.AspNetCore.Authentication.OAuth.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization/*******": {"compile": {"Microsoft.AspNetCore.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"compile": {"Microsoft.AspNetCore.Authorization.Policy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Authorization/*******": {"compile": {"Microsoft.AspNetCore.Components.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components/*******": {"compile": {"Microsoft.AspNetCore.Components.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Endpoints/*******": {"compile": {"Microsoft.AspNetCore.Components.Endpoints.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Forms/*******": {"compile": {"Microsoft.AspNetCore.Components.Forms.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Server/*******": {"compile": {"Microsoft.AspNetCore.Components.Server.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Web/*******": {"compile": {"Microsoft.AspNetCore.Components.Web.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Connections.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Connections.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.CookiePolicy/*******": {"compile": {"Microsoft.AspNetCore.CookiePolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cors/*******": {"compile": {"Microsoft.AspNetCore.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.Internal.Reference/*******": {"compile": {"Microsoft.AspNetCore.Cryptography.Internal.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/*******": {"compile": {"Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Reference/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore/*******": {"compile": {"Microsoft.AspNetCore.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HostFiltering/*******": {"compile": {"Microsoft.AspNetCore.HostFiltering.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting/*******": {"compile": {"Microsoft.AspNetCore.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Html.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Html.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http/*******": {"compile": {"Microsoft.AspNetCore.Http.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Extensions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Features.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Features.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Results/*******": {"compile": {"Microsoft.AspNetCore.Http.Results.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpLogging/*******": {"compile": {"Microsoft.AspNetCore.HttpLogging.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpOverrides/*******": {"compile": {"Microsoft.AspNetCore.HttpOverrides.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"compile": {"Microsoft.AspNetCore.HttpsPolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Identity/*******": {"compile": {"Microsoft.AspNetCore.Identity.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization/*******": {"compile": {"Microsoft.AspNetCore.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization.Routing/*******": {"compile": {"Microsoft.AspNetCore.Localization.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Metadata/*******": {"compile": {"Microsoft.AspNetCore.Metadata.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Core/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"compile": {"Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc/*******": {"compile": {"Microsoft.AspNetCore.Mvc.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"compile": {"Microsoft.AspNetCore.Mvc.RazorPages.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"compile": {"Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.OutputCaching/*******": {"compile": {"Microsoft.AspNetCore.OutputCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.RateLimiting/*******": {"compile": {"Microsoft.AspNetCore.RateLimiting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor.Reference/*******": {"compile": {"Microsoft.AspNetCore.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor.Runtime.Reference/*******": {"compile": {"Microsoft.AspNetCore.Razor.Runtime.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.RequestDecompression/*******": {"compile": {"Microsoft.AspNetCore.RequestDecompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCompression/*******": {"compile": {"Microsoft.AspNetCore.ResponseCompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Rewrite/*******": {"compile": {"Microsoft.AspNetCore.Rewrite.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Routing.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing/*******": {"compile": {"Microsoft.AspNetCore.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"compile": {"Microsoft.AspNetCore.Server.HttpSys.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IIS/*******": {"compile": {"Microsoft.AspNetCore.Server.IIS.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"compile": {"Microsoft.AspNetCore.Server.IISIntegration.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Session/*******": {"compile": {"Microsoft.AspNetCore.Session.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Common.Reference/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Core/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR/*******": {"compile": {"Microsoft.AspNetCore.SignalR.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.StaticFiles.Reference/*******": {"compile": {"Microsoft.AspNetCore.StaticFiles.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebSockets/*******": {"compile": {"Microsoft.AspNetCore.WebSockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebUtilities/*******": {"compile": {"Microsoft.AspNetCore.WebUtilities.dll": {}}, "compileOnly": true}, "Microsoft.CSharp.Reference/*******": {"compile": {"Microsoft.CSharp.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.CommandLine/*******": {"compile": {"Microsoft.Extensions.Configuration.CommandLine.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration/*******": {"compile": {"Microsoft.Extensions.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.EnvironmentVariables/*******": {"compile": {"Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.FileExtensions/*******": {"compile": {"Microsoft.Extensions.Configuration.FileExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Ini/*******": {"compile": {"Microsoft.Extensions.Configuration.Ini.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Json/*******": {"compile": {"Microsoft.Extensions.Configuration.Json.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"compile": {"Microsoft.Extensions.Configuration.KeyPerFile.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.UserSecrets/*******": {"compile": {"Microsoft.Extensions.Configuration.UserSecrets.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Xml/*******": {"compile": {"Microsoft.Extensions.Configuration.Xml.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Reference/*******": {"compile": {"Microsoft.Extensions.DependencyInjection.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics/*******": {"compile": {"Microsoft.Extensions.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Features.Reference/*******": {"compile": {"Microsoft.Extensions.Features.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Composite/*******": {"compile": {"Microsoft.Extensions.FileProviders.Composite.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Embedded/*******": {"compile": {"Microsoft.Extensions.FileProviders.Embedded.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Physical/*******": {"compile": {"Microsoft.Extensions.FileProviders.Physical.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileSystemGlobbing/*******": {"compile": {"Microsoft.Extensions.FileSystemGlobbing.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting/*******": {"compile": {"Microsoft.Extensions.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Http/*******": {"compile": {"Microsoft.Extensions.Http.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Core/*******": {"compile": {"Microsoft.Extensions.Identity.Core.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Stores/*******": {"compile": {"Microsoft.Extensions.Identity.Stores.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization.Abstractions/*******": {"compile": {"Microsoft.Extensions.Localization.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization/*******": {"compile": {"Microsoft.Extensions.Localization.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Configuration/*******": {"compile": {"Microsoft.Extensions.Logging.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Console/*******": {"compile": {"Microsoft.Extensions.Logging.Console.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Debug/*******": {"compile": {"Microsoft.Extensions.Logging.Debug.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging/*******": {"compile": {"Microsoft.Extensions.Logging.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventLog/*******": {"compile": {"Microsoft.Extensions.Logging.EventLog.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventSource/*******": {"compile": {"Microsoft.Extensions.Logging.EventSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.TraceSource/*******": {"compile": {"Microsoft.Extensions.Logging.TraceSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.ObjectPool.Reference/*******": {"compile": {"Microsoft.Extensions.ObjectPool.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"compile": {"Microsoft.Extensions.Options.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.WebEncoders.Reference/*******": {"compile": {"Microsoft.Extensions.WebEncoders.dll": {}}, "compileOnly": true}, "Microsoft.JSInterop/*******": {"compile": {"Microsoft.JSInterop.dll": {}}, "compileOnly": true}, "Microsoft.Net.Http.Headers.Reference/*******": {"compile": {"Microsoft.Net.Http.Headers.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic.Core/********": {"compile": {"Microsoft.VisualBasic.Core.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic/10.0.0.0": {"compile": {"Microsoft.VisualBasic.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Primitives/*******": {"compile": {"Microsoft.Win32.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Registry.Reference/*******": {"compile": {"Microsoft.Win32.Registry.dll": {}}, "compileOnly": true}, "mscorlib/*******": {"compile": {"mscorlib.dll": {}}, "compileOnly": true}, "netstandard/2.1.0.0": {"compile": {"netstandard.dll": {}}, "compileOnly": true}, "System.AppContext/*******": {"compile": {"System.AppContext.dll": {}}, "compileOnly": true}, "System.Buffers.Reference/*******": {"compile": {"System.Buffers.dll": {}}, "compileOnly": true}, "System.Collections.Concurrent.Reference/*******": {"compile": {"System.Collections.Concurrent.dll": {}}, "compileOnly": true}, "System.Collections.Reference/*******": {"compile": {"System.Collections.dll": {}}, "compileOnly": true}, "System.Collections.Immutable.Reference/*******": {"compile": {"System.Collections.Immutable.dll": {}}, "compileOnly": true}, "System.Collections.NonGeneric/*******": {"compile": {"System.Collections.NonGeneric.dll": {}}, "compileOnly": true}, "System.Collections.Specialized/*******": {"compile": {"System.Collections.Specialized.dll": {}}, "compileOnly": true}, "System.ComponentModel.Annotations.Reference/*******": {"compile": {"System.ComponentModel.Annotations.dll": {}}, "compileOnly": true}, "System.ComponentModel.DataAnnotations/*******": {"compile": {"System.ComponentModel.DataAnnotations.dll": {}}, "compileOnly": true}, "System.ComponentModel/*******": {"compile": {"System.ComponentModel.dll": {}}, "compileOnly": true}, "System.ComponentModel.EventBasedAsync/*******": {"compile": {"System.ComponentModel.EventBasedAsync.dll": {}}, "compileOnly": true}, "System.ComponentModel.Primitives/*******": {"compile": {"System.ComponentModel.Primitives.dll": {}}, "compileOnly": true}, "System.ComponentModel.TypeConverter/*******": {"compile": {"System.ComponentModel.TypeConverter.dll": {}}, "compileOnly": true}, "System.Configuration/*******": {"compile": {"System.Configuration.dll": {}}, "compileOnly": true}, "System.Console/*******": {"compile": {"System.Console.dll": {}}, "compileOnly": true}, "System.Core/*******": {"compile": {"System.Core.dll": {}}, "compileOnly": true}, "System.Data.Common.Reference/*******": {"compile": {"System.Data.Common.dll": {}}, "compileOnly": true}, "System.Data.DataSetExtensions/*******": {"compile": {"System.Data.DataSetExtensions.dll": {}}, "compileOnly": true}, "System.Data/*******": {"compile": {"System.Data.dll": {}}, "compileOnly": true}, "System.Diagnostics.Contracts/*******": {"compile": {"System.Diagnostics.Contracts.dll": {}}, "compileOnly": true}, "System.Diagnostics.Debug.Reference/*******": {"compile": {"System.Diagnostics.Debug.dll": {}}, "compileOnly": true}, "System.Diagnostics.EventLog.Reference/*******": {"compile": {"System.Diagnostics.EventLog.dll": {}}, "compileOnly": true}, "System.Diagnostics.FileVersionInfo/*******": {"compile": {"System.Diagnostics.FileVersionInfo.dll": {}}, "compileOnly": true}, "System.Diagnostics.Process/*******": {"compile": {"System.Diagnostics.Process.dll": {}}, "compileOnly": true}, "System.Diagnostics.StackTrace/*******": {"compile": {"System.Diagnostics.StackTrace.dll": {}}, "compileOnly": true}, "System.Diagnostics.TextWriterTraceListener/*******": {"compile": {"System.Diagnostics.TextWriterTraceListener.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tools/*******": {"compile": {"System.Diagnostics.Tools.dll": {}}, "compileOnly": true}, "System.Diagnostics.TraceSource/*******": {"compile": {"System.Diagnostics.TraceSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tracing.Reference/*******": {"compile": {"System.Diagnostics.Tracing.dll": {}}, "compileOnly": true}, "System/*******": {"compile": {"System.dll": {}}, "compileOnly": true}, "System.Drawing/*******": {"compile": {"System.Drawing.dll": {}}, "compileOnly": true}, "System.Drawing.Primitives/*******": {"compile": {"System.Drawing.Primitives.dll": {}}, "compileOnly": true}, "System.Dynamic.Runtime/*******": {"compile": {"System.Dynamic.Runtime.dll": {}}, "compileOnly": true}, "System.Formats.Asn1.Reference/*******": {"compile": {"System.Formats.Asn1.dll": {}}, "compileOnly": true}, "System.Formats.Tar/*******": {"compile": {"System.Formats.Tar.dll": {}}, "compileOnly": true}, "System.Globalization.Calendars.Reference/*******": {"compile": {"System.Globalization.Calendars.dll": {}}, "compileOnly": true}, "System.Globalization.Reference/*******": {"compile": {"System.Globalization.dll": {}}, "compileOnly": true}, "System.Globalization.Extensions.Reference/*******": {"compile": {"System.Globalization.Extensions.dll": {}}, "compileOnly": true}, "System.IO.Compression.Brotli/*******": {"compile": {"System.IO.Compression.Brotli.dll": {}}, "compileOnly": true}, "System.IO.Compression/*******": {"compile": {"System.IO.Compression.dll": {}}, "compileOnly": true}, "System.IO.Compression.FileSystem/*******": {"compile": {"System.IO.Compression.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.Compression.ZipFile/*******": {"compile": {"System.IO.Compression.ZipFile.dll": {}}, "compileOnly": true}, "System.IO.Reference/*******": {"compile": {"System.IO.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.AccessControl/*******": {"compile": {"System.IO.FileSystem.AccessControl.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Reference/*******": {"compile": {"System.IO.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.DriveInfo/*******": {"compile": {"System.IO.FileSystem.DriveInfo.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Primitives.Reference/*******": {"compile": {"System.IO.FileSystem.Primitives.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Watcher/*******": {"compile": {"System.IO.FileSystem.Watcher.dll": {}}, "compileOnly": true}, "System.IO.IsolatedStorage/*******": {"compile": {"System.IO.IsolatedStorage.dll": {}}, "compileOnly": true}, "System.IO.MemoryMappedFiles/*******": {"compile": {"System.IO.MemoryMappedFiles.dll": {}}, "compileOnly": true}, "System.IO.Pipelines.Reference/*******": {"compile": {"System.IO.Pipelines.dll": {}}, "compileOnly": true}, "System.IO.Pipes.AccessControl/*******": {"compile": {"System.IO.Pipes.AccessControl.dll": {}}, "compileOnly": true}, "System.IO.Pipes/*******": {"compile": {"System.IO.Pipes.dll": {}}, "compileOnly": true}, "System.IO.UnmanagedMemoryStream/*******": {"compile": {"System.IO.UnmanagedMemoryStream.dll": {}}, "compileOnly": true}, "System.Linq.Reference/*******": {"compile": {"System.Linq.dll": {}}, "compileOnly": true}, "System.Linq.Expressions/*******": {"compile": {"System.Linq.Expressions.dll": {}}, "compileOnly": true}, "System.Linq.Parallel/*******": {"compile": {"System.Linq.Parallel.dll": {}}, "compileOnly": true}, "System.Linq.Queryable/*******": {"compile": {"System.Linq.Queryable.dll": {}}, "compileOnly": true}, "System.Memory.Reference/*******": {"compile": {"System.Memory.dll": {}}, "compileOnly": true}, "System.Net/*******": {"compile": {"System.Net.dll": {}}, "compileOnly": true}, "System.Net.Http.Reference/*******": {"compile": {"System.Net.Http.dll": {}}, "compileOnly": true}, "System.Net.Http.Json/*******": {"compile": {"System.Net.Http.Json.dll": {}}, "compileOnly": true}, "System.Net.HttpListener/*******": {"compile": {"System.Net.HttpListener.dll": {}}, "compileOnly": true}, "System.Net.Mail/*******": {"compile": {"System.Net.Mail.dll": {}}, "compileOnly": true}, "System.Net.NameResolution/*******": {"compile": {"System.Net.NameResolution.dll": {}}, "compileOnly": true}, "System.Net.NetworkInformation/*******": {"compile": {"System.Net.NetworkInformation.dll": {}}, "compileOnly": true}, "System.Net.Ping/*******": {"compile": {"System.Net.Ping.dll": {}}, "compileOnly": true}, "System.Net.Primitives.Reference/*******": {"compile": {"System.Net.Primitives.dll": {}}, "compileOnly": true}, "System.Net.Quic/*******": {"compile": {"System.Net.Quic.dll": {}}, "compileOnly": true}, "System.Net.Requests/*******": {"compile": {"System.Net.Requests.dll": {}}, "compileOnly": true}, "System.Net.Security/*******": {"compile": {"System.Net.Security.dll": {}}, "compileOnly": true}, "System.Net.ServicePoint/*******": {"compile": {"System.Net.ServicePoint.dll": {}}, "compileOnly": true}, "System.Net.Sockets/*******": {"compile": {"System.Net.Sockets.dll": {}}, "compileOnly": true}, "System.Net.WebClient/*******": {"compile": {"System.Net.WebClient.dll": {}}, "compileOnly": true}, "System.Net.WebHeaderCollection/*******": {"compile": {"System.Net.WebHeaderCollection.dll": {}}, "compileOnly": true}, "System.Net.WebProxy/*******": {"compile": {"System.Net.WebProxy.dll": {}}, "compileOnly": true}, "System.Net.WebSockets.Client/*******": {"compile": {"System.Net.WebSockets.Client.dll": {}}, "compileOnly": true}, "System.Net.WebSockets/*******": {"compile": {"System.Net.WebSockets.dll": {}}, "compileOnly": true}, "System.Numerics/*******": {"compile": {"System.Numerics.dll": {}}, "compileOnly": true}, "System.Numerics.Vectors.Reference/*******": {"compile": {"System.Numerics.Vectors.dll": {}}, "compileOnly": true}, "System.ObjectModel/*******": {"compile": {"System.ObjectModel.dll": {}}, "compileOnly": true}, "System.Reflection.DispatchProxy.Reference/*******": {"compile": {"System.Reflection.DispatchProxy.dll": {}}, "compileOnly": true}, "System.Reflection.Reference/*******": {"compile": {"System.Reflection.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Reference/*******": {"compile": {"System.Reflection.Emit.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.ILGeneration.Reference/*******": {"compile": {"System.Reflection.Emit.ILGeneration.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Lightweight.Reference/*******": {"compile": {"System.Reflection.Emit.Lightweight.dll": {}}, "compileOnly": true}, "System.Reflection.Extensions/*******": {"compile": {"System.Reflection.Extensions.dll": {}}, "compileOnly": true}, "System.Reflection.Metadata.Reference/*******": {"compile": {"System.Reflection.Metadata.dll": {}}, "compileOnly": true}, "System.Reflection.Primitives.Reference/*******": {"compile": {"System.Reflection.Primitives.dll": {}}, "compileOnly": true}, "System.Reflection.TypeExtensions.Reference/*******": {"compile": {"System.Reflection.TypeExtensions.dll": {}}, "compileOnly": true}, "System.Resources.Reader/*******": {"compile": {"System.Resources.Reader.dll": {}}, "compileOnly": true}, "System.Resources.ResourceManager.Reference/*******": {"compile": {"System.Resources.ResourceManager.dll": {}}, "compileOnly": true}, "System.Resources.Writer/*******": {"compile": {"System.Resources.Writer.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.Unsafe.Reference/*******": {"compile": {"System.Runtime.CompilerServices.Unsafe.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.VisualC/*******": {"compile": {"System.Runtime.CompilerServices.VisualC.dll": {}}, "compileOnly": true}, "System.Runtime.Reference/*******": {"compile": {"System.Runtime.dll": {}}, "compileOnly": true}, "System.Runtime.Extensions.Reference/*******": {"compile": {"System.Runtime.Extensions.dll": {}}, "compileOnly": true}, "System.Runtime.Handles.Reference/*******": {"compile": {"System.Runtime.Handles.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.Reference/*******": {"compile": {"System.Runtime.InteropServices.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.JavaScript/*******": {"compile": {"System.Runtime.InteropServices.JavaScript.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.RuntimeInformation/*******": {"compile": {"System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "compileOnly": true}, "System.Runtime.Intrinsics/*******": {"compile": {"System.Runtime.Intrinsics.dll": {}}, "compileOnly": true}, "System.Runtime.Loader/*******": {"compile": {"System.Runtime.Loader.dll": {}}, "compileOnly": true}, "System.Runtime.Numerics.Reference/*******": {"compile": {"System.Runtime.Numerics.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization/*******": {"compile": {"System.Runtime.Serialization.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Formatters/*******": {"compile": {"System.Runtime.Serialization.Formatters.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Json/*******": {"compile": {"System.Runtime.Serialization.Json.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Primitives/*******": {"compile": {"System.Runtime.Serialization.Primitives.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Xml/*******": {"compile": {"System.Runtime.Serialization.Xml.dll": {}}, "compileOnly": true}, "System.Security.AccessControl.Reference/*******": {"compile": {"System.Security.AccessControl.dll": {}}, "compileOnly": true}, "System.Security.Claims/*******": {"compile": {"System.Security.Claims.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Algorithms.Reference/*******": {"compile": {"System.Security.Cryptography.Algorithms.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Cng.Reference/*******": {"compile": {"System.Security.Cryptography.Cng.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Csp.Reference/*******": {"compile": {"System.Security.Cryptography.Csp.dll": {}}, "compileOnly": true}, "System.Security.Cryptography/*******": {"compile": {"System.Security.Cryptography.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Encoding.Reference/*******": {"compile": {"System.Security.Cryptography.Encoding.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.OpenSsl.Reference/*******": {"compile": {"System.Security.Cryptography.OpenSsl.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Primitives.Reference/*******": {"compile": {"System.Security.Cryptography.Primitives.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.X509Certificates.Reference/*******": {"compile": {"System.Security.Cryptography.X509Certificates.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Xml.Reference/*******": {"compile": {"System.Security.Cryptography.Xml.dll": {}}, "compileOnly": true}, "System.Security/*******": {"compile": {"System.Security.dll": {}}, "compileOnly": true}, "System.Security.Principal/*******": {"compile": {"System.Security.Principal.dll": {}}, "compileOnly": true}, "System.Security.Principal.Windows.Reference/*******": {"compile": {"System.Security.Principal.Windows.dll": {}}, "compileOnly": true}, "System.Security.SecureString/*******": {"compile": {"System.Security.SecureString.dll": {}}, "compileOnly": true}, "System.ServiceModel.Web/*******": {"compile": {"System.ServiceModel.Web.dll": {}}, "compileOnly": true}, "System.ServiceProcess/*******": {"compile": {"System.ServiceProcess.dll": {}}, "compileOnly": true}, "System.Text.Encoding.CodePages.Reference/*******": {"compile": {"System.Text.Encoding.CodePages.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Reference/*******": {"compile": {"System.Text.Encoding.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Extensions/*******": {"compile": {"System.Text.Encoding.Extensions.dll": {}}, "compileOnly": true}, "System.Text.Encodings.Web.Reference/*******": {"compile": {"System.Text.Encodings.Web.dll": {}}, "compileOnly": true}, "System.Text.Json.Reference/*******": {"compile": {"System.Text.Json.dll": {}}, "compileOnly": true}, "System.Text.RegularExpressions.Reference/*******": {"compile": {"System.Text.RegularExpressions.dll": {}}, "compileOnly": true}, "System.Threading.Channels/*******": {"compile": {"System.Threading.Channels.dll": {}}, "compileOnly": true}, "System.Threading.Reference/*******": {"compile": {"System.Threading.dll": {}}, "compileOnly": true}, "System.Threading.Overlapped/*******": {"compile": {"System.Threading.Overlapped.dll": {}}, "compileOnly": true}, "System.Threading.RateLimiting.Reference/*******": {"compile": {"System.Threading.RateLimiting.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Dataflow/*******": {"compile": {"System.Threading.Tasks.Dataflow.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Reference/*******": {"compile": {"System.Threading.Tasks.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Extensions.Reference/*******": {"compile": {"System.Threading.Tasks.Extensions.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Parallel/*******": {"compile": {"System.Threading.Tasks.Parallel.dll": {}}, "compileOnly": true}, "System.Threading.Thread.Reference/*******": {"compile": {"System.Threading.Thread.dll": {}}, "compileOnly": true}, "System.Threading.ThreadPool/*******": {"compile": {"System.Threading.ThreadPool.dll": {}}, "compileOnly": true}, "System.Threading.Timer/*******": {"compile": {"System.Threading.Timer.dll": {}}, "compileOnly": true}, "System.Transactions/*******": {"compile": {"System.Transactions.dll": {}}, "compileOnly": true}, "System.Transactions.Local/*******": {"compile": {"System.Transactions.Local.dll": {}}, "compileOnly": true}, "System.ValueTuple/*******": {"compile": {"System.ValueTuple.dll": {}}, "compileOnly": true}, "System.Web/*******": {"compile": {"System.Web.dll": {}}, "compileOnly": true}, "System.Web.HttpUtility/*******": {"compile": {"System.Web.HttpUtility.dll": {}}, "compileOnly": true}, "System.Windows/*******": {"compile": {"System.Windows.dll": {}}, "compileOnly": true}, "System.Xml/*******": {"compile": {"System.Xml.dll": {}}, "compileOnly": true}, "System.Xml.Linq/*******": {"compile": {"System.Xml.Linq.dll": {}}, "compileOnly": true}, "System.Xml.ReaderWriter/*******": {"compile": {"System.Xml.ReaderWriter.dll": {}}, "compileOnly": true}, "System.Xml.Serialization/*******": {"compile": {"System.Xml.Serialization.dll": {}}, "compileOnly": true}, "System.Xml.XDocument/*******": {"compile": {"System.Xml.XDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlDocument/*******": {"compile": {"System.Xml.XmlDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlSerializer/*******": {"compile": {"System.Xml.XmlSerializer.dll": {}}, "compileOnly": true}, "System.Xml.XPath/*******": {"compile": {"System.Xml.XPath.dll": {}}, "compileOnly": true}, "System.Xml.XPath.XDocument/*******": {"compile": {"System.Xml.XPath.XDocument.dll": {}}, "compileOnly": true}, "WindowsBase/*******": {"compile": {"WindowsBase.dll": {}}, "compileOnly": true}}}, "libraries": {"Admin.NET.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AlibabaCloud.EndpointUtil/0.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-p/vsdJoVIfc1QREW4JX1tpzKdZZcFdw6/qfrylfcFXc0e2BDMQ2kPrv3nkyr2u+p4BF0PmOYl4EDqRtqLiBc+g==", "path": "alibabacloud.endpointutil/0.1.1", "hashPath": "alibabacloud.endpointutil.0.1.1.nupkg.sha512"}, "AlibabaCloud.GatewaySpi/0.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-vvmA5BrM8rpOZoXwFl/ZWHYLFnZ8EwPp+07z3Eeg9okLv58QJ8+KGWVovTr8tJpuDgPFG1SVdHXRi04A62ehdA==", "path": "alibabacloud.gatewayspi/0.0.3", "hashPath": "alibabacloud.gatewayspi.0.0.3.nupkg.sha512"}, "AlibabaCloud.OpenApiClient/0.1.14": {"type": "package", "serviceable": true, "sha512": "sha512-mmfl2eiPbfMVJBxLM075nMnDnEvMRP74kTdFJ1ud0RfSgZ6gb2wQJjkiT3Q4YVwVeFoiNZzEolM694zdRXe63Q==", "path": "alibabacloud.openapiclient/0.1.14", "hashPath": "alibabacloud.openapiclient.0.1.14.nupkg.sha512"}, "AlibabaCloud.OpenApiUtil/1.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-0Zd2UHm5gUND+7xNma2SAZEqm2Uy8dvAknLvCx72uDUCbOnfHWh+TsnUnNGMKUvmG3s/ZqxUA1UYIdp5BFCn5Q==", "path": "alibabacloud.openapiutil/1.1.2", "hashPath": "alibabacloud.openapiutil.1.1.2.nupkg.sha512"}, "AlibabaCloud.SDK.Dysmsapi20170525/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1zrpRz6X/9ZohWva2X2o5ddNel2x6ZBPGaBxAGodS2UXrELgqlBHhyv1kPLUkiloRwP51vR+WI4m8/AHSIeb0Q==", "path": "alibabacloud.sdk.dysmsapi20170525/4.0.0", "hashPath": "alibabacloud.sdk.dysmsapi20170525.4.0.0.nupkg.sha512"}, "AlibabaCloud.TeaUtil/0.1.19": {"type": "package", "serviceable": true, "sha512": "sha512-gjPboQEC3rSuS/8Ohk4VAw42W54h9NfIZxn4JIuWfoIF3k3mZxVdMJdKKOgIkNrx8YaLOthPSM3Pfb1zfOyFcw==", "path": "alibabacloud.teautil/0.1.19", "hashPath": "alibabacloud.teautil.0.1.19.nupkg.sha512"}, "AlibabaCloud.TeaXML/0.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-mHxE6H3eq4jaeqn3hryIYTI0k8quvPZfZdEE+PdU8rw+NGRvev68D8Aei6xjwW/pArZaTG6yPawYu5c0EaZkfw==", "path": "alibabacloud.teaxml/0.0.5", "hashPath": "alibabacloud.teaxml.0.0.5.nupkg.sha512"}, "AlipaySDKNet.Standard/4.9.627": {"type": "package", "serviceable": true, "sha512": "sha512-4Myu9cw0QUQeCIA/T08QBQ9gHtzpYTO/3LicET+VRhZ1999n2DmZSNYIclivxjmpEBusf8VNDzPlFx0I8vL61w==", "path": "alipaysdknet.standard/4.9.627", "hashPath": "alipaysdknet.standard.4.9.627.nupkg.sha512"}, "Aliyun.Credentials/1.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-fdWhSvNiXXIWz73eDZGZHesBhd5+St85W8CDaD9aP1ifBK+MZKx9YTL/6e6uLq0TN7j8J0IR5beXUXxohqjjjQ==", "path": "aliyun.credentials/1.5.0", "hashPath": "aliyun.credentials.1.5.0.nupkg.sha512"}, "Aliyun.OSS.SDK.NetCore/2.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-ElvJwTAdBqFmgb7K4PdxDXPFbOBCIUI5OvCOMfCoUoDL21aivtWMFUtU1v4Dxc2wcrN8XQdY1EKeGFhJK/zVyQ==", "path": "aliyun.oss.sdk.netcore/2.13.0", "hashPath": "aliyun.oss.sdk.netcore.2.13.0.nupkg.sha512"}, "AngleSharp/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-iHzfn4cK6CmhuURNdEpmSQCq5/HZFldEpkbnmqT9My8+6l2Sz3F+NxoqRA8z/jTkWB+SAu5boRdp4v/WtyjuIQ==", "path": "anglesharp/1.3.0", "hashPath": "anglesharp.1.3.0.nupkg.sha512"}, "AspectCore.Extensions.Reflection/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZQQ0vwV3OvhI8fipppLK+8PwwgeHSnXW1S/f0e6KGdzTHBJefrnMxdgF8xw08OC76BOK5/xeGJ4FKGJfK30H+g==", "path": "aspectcore.extensions.reflection/2.4.0", "hashPath": "aspectcore.extensions.reflection.2.4.0.nupkg.sha512"}, "AspNet.Security.OAuth.Gitee/8.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+/idY/Fgy1IedVOjFUs/k4yQsHLwRSpJdqQvKwKzn/1xqBYiZSDrxJsM6RT7RmYUqk7/UjcTqGM3f8lD82y+uQ==", "path": "aspnet.security.oauth.gitee/8.3.0", "hashPath": "aspnet.security.oauth.gitee.8.3.0.nupkg.sha512"}, "AspNet.Security.OAuth.Weixin/8.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NNuvdN85ibftxyb+dKEHdcO0Te4lu6NF6lb1bVbDeXqSI9um9B1RNDl8Uj4sXRBOF6xVre1usTUMGbXh8YQE5g==", "path": "aspnet.security.oauth.weixin/8.3.0", "hashPath": "aspnet.security.oauth.weixin.8.3.0.nupkg.sha512"}, "AspNetCoreRateLimit/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6fq9+o1maGADUmpK/PvcF0DtXW2+7bSkIL7MDIo/agbIHKN8XkMQF4oze60DO731WaQmHmK260hB30FwPzCmEg==", "path": "aspnetcoreratelimit/5.0.0", "hashPath": "aspnetcoreratelimit.5.0.0.nupkg.sha512"}, "Azure.Core/1.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "path": "azure.core/1.38.0", "hashPath": "azure.core.1.38.0.nupkg.sha512"}, "Azure.Identity/1.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "path": "azure.identity/1.11.4", "hashPath": "azure.identity.1.11.4.nupkg.sha512"}, "BceSdkDotNetCore/1.0.2.911": {"type": "package", "serviceable": true, "sha512": "sha512-DNa0svdlYlgQABUo2rTSaXuQ6ZMrA/sqMV8dcuiRxEp0mAOkQyJdawwuxise05GmhrTbyysKfpw4l5rPcBMN0Q==", "path": "bcesdkdotnetcore/1.0.2.911", "hashPath": "bcesdkdotnetcore.1.0.2.911.nupkg.sha512"}, "Ben.Demystifier/0.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-axFeEMfmEORy3ipAzOXG/lE+KcNptRbei3F0C4kQCdeiQtW+qJW90K5iIovITGrdLt8AjhNCwk5qLSX9/rFpoA==", "path": "ben.demystifier/0.4.1", "hashPath": "ben.demystifier.0.4.1.nupkg.sha512"}, "BouncyCastle.Cryptography/2.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-vZsG2YILhthgRqO+ZVgRff4ZFKKTl0v7kqaVBLCtRvpREhfBP33pcWrdA3PRYgWuFL1RxiUFvjMUHTdBZlJcoA==", "path": "bouncycastle.cryptography/2.6.1", "hashPath": "bouncycastle.cryptography.2.6.1.nupkg.sha512"}, "CommunityToolkit.HighPerformance/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kgDi65k02hrgnHy3N0xENecsr0spW13RdIA1tipovi9t16gKziI7uZIu3qkxz0GctCHNM4hfeqXYg//6wHJ6Kw==", "path": "communitytoolkit.highperformance/8.1.0", "hashPath": "communitytoolkit.highperformance.8.1.0.nupkg.sha512"}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "DocumentFormat.OpenXml/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-2z9QBzeTLNNKWM9SaOSDMegfQk/7hDuElOsmF77pKZMkFRP/GHA/W/4yOAQD9kn15N/FsFxHn3QVYkatuZghiA==", "path": "documentformat.openxml/3.1.1", "hashPath": "documentformat.openxml.3.1.1.nupkg.sha512"}, "DocumentFormat.OpenXml.Framework/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-6APEp/ElZV58S/4v8mf4Ke3ONEDORs64MqdD64Z7wWpcHANB9oovQsGIwtqjnKihulOj7T0a6IxHIHOfMqKOng==", "path": "documentformat.openxml.framework/3.1.1", "hashPath": "documentformat.openxml.framework.3.1.1.nupkg.sha512"}, "DynamicExpresso.Core/2.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-p6GEP3BphaT9xa59VjpQeozkloXjcDmoL6aPXOInl5S5chWtB82H+GiirV3H1bP39ZeXX2e1UN0w7/pD1wCUlg==", "path": "dynamicexpresso.core/2.3.3", "hashPath": "dynamicexpresso.core.2.3.3.nupkg.sha512"}, "Elastic.Clients.Elasticsearch/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-EdLRCO+/5Q5O2HUQ3fGOnH190/2hin8ykiM/O3gm40jmHs8pRqAbGER+MFZeWU4KoZQklJ1s3Ei/rpeiw4OK1A==", "path": "elastic.clients.elasticsearch/9.0.7", "hashPath": "elastic.clients.elasticsearch.9.0.7.nupkg.sha512"}, "Elastic.Transport/0.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-evL2QL9tsjsWMuV/JAv4EyJmG0S//Np9z35qpWWn70+FiGiWYYsHgZEm7Vs4ad/CQh6YGMZcJ/G3len6+LzjUA==", "path": "elastic.transport/0.9.2", "hashPath": "elastic.transport.0.9.2.nupkg.sha512"}, "Flurl/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rpts69yYgvJqg6PPgqShBQEZ4aNzWQqWpWppcT0oDWxDCIsBqiod4pj6LQZdhk+1OozLFagemldMRACdHF3CsA==", "path": "flurl/4.0.0", "hashPath": "flurl.4.0.0.nupkg.sha512"}, "Flurl.Http/4.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-9vCqFFyceA11yplkFD8AbCFFTvG1Lrw3tpsgOpL5sLUc28p6zcvGszNleuT6nDymRvtt5eS+rqUX+bRztg1fhA==", "path": "flurl.http/4.0.2", "hashPath": "flurl.http.4.0.2.nupkg.sha512"}, "Furion.Extras.Authentication.JwtBearer/*********": {"type": "package", "serviceable": true, "sha512": "sha512-egTNJ5x2GFn0PEltbwKDvXTxvPsNuny2xTetATLn6TZtR9KadF5iHya+dhoUzZ7vWk3YDRuBAfe3qmlr5pMr0Q==", "path": "furion.extras.authentication.jwtbearer/*********", "hashPath": "furion.extras.authentication.jwtbearer.*********.nupkg.sha512"}, "Furion.Extras.ObjectMapper.Mapster/*********": {"type": "package", "serviceable": true, "sha512": "sha512-iYZB4StMBsIcwtmkyWY/ZA8375YkUv9TwGZvBpdhLSue3+XrayK6rgW2M9wrTHpMqzKHGdfGf5U7NO1abvbIkQ==", "path": "furion.extras.objectmapper.mapster/*********", "hashPath": "furion.extras.objectmapper.mapster.*********.nupkg.sha512"}, "Furion.Pure/*********": {"type": "package", "serviceable": true, "sha512": "sha512-mrdvrSEg71Z9z+k8s/gfKp8SlB+b0qN0ffhE1ywj8Ub5zCGGtLKtOx9Et0p0iCu3+JhjNTejfywx1bjym4XXHw==", "path": "furion.pure/*********", "hashPath": "furion.pure.*********.nupkg.sha512"}, "Furion.Pure.Extras.DependencyModel.CodeAnalysis/*********": {"type": "package", "serviceable": true, "sha512": "sha512-N9ol2zndPjWu0yULO4mgoWpX62pkOFSddcYytFwLZI6J+OG1+w7rYVUc67muiMeD+4ta3cfv5SmX2+aABbJ4hg==", "path": "furion.pure.extras.dependencymodel.codeanalysis/*********", "hashPath": "furion.pure.extras.dependencymodel.codeanalysis.*********.nupkg.sha512"}, "Hardware.Info/*********": {"type": "package", "serviceable": true, "sha512": "sha512-ueI3NbMoOL9FVb2Sq0evJihk7Pa9lmJUaxUTaSs2CA0N5LIVzOJf/qHDIEy10GdJKJJl20HJuENKeNfonawuuw==", "path": "hardware.info/*********", "hashPath": "hardware.info.*********.nupkg.sha512"}, "Hashids.net/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-TuQ8ns5c+eeIOHSgJdwCLb0nWzKqn2gCPSLFoz6nWdhw3tS5Wa43qrwRXT4UJT7XVgHqKssacVU1SGlAOWjeIg==", "path": "hashids.net/1.7.0", "hashPath": "hashids.net.1.7.0.nupkg.sha512"}, "Haukcode.WkHtmlToPdfDotNet/1.5.95": {"type": "package", "serviceable": true, "sha512": "sha512-Eiwfe3O+cqqiMxpqYIrt+KbBCkafDc0k9tBBYhIAIBIS1FMJQ5FmlRcf8ElEUhQpIAkn2w5gF4/dYl0+M+cETQ==", "path": "haukcode.wkhtmltopdfdotnet/1.5.95", "hashPath": "haukcode.wkhtmltopdfdotnet.1.5.95.nupkg.sha512"}, "HtmlToOpenXml.dll/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-STr/YomlvHDQky9ilIg6I9dbIwSw7dimDfj1fFo+FFz4n56x9nT18+MB68pBlwBqTOVCKQ2oE2FYBOVJ+rCIrA==", "path": "htmltoopenxml.dll/3.1.0", "hashPath": "htmltoopenxml.dll.3.1.0.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "IP2Region.Ex/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-B8TxhuAw72cPwjgf8pqDf62l1TJL0jXw4J13fXHg4Igq1OwT7SRotQX6gN6puyIgHVYLtKxnmhFf60oUITxmxA==", "path": "ip2region.ex/1.2.0", "hashPath": "ip2region.ex.1.2.0.nupkg.sha512"}, "IPTools.China/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-12VnC92ffiKlLRwr5Ay3uFvZMCB9SDNn77sVlNycQu1OJAunnuCNBOVZTkg9D2UL2cc+iMwra6if9viXhrrt7w==", "path": "iptools.china/1.6.0", "hashPath": "iptools.china.1.6.0.nupkg.sha512"}, "IPTools.Core/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-qO+EY5vEwLKtOkQD1aMweM9pIVSFLwTi/Z2ZnX08qBXI4yPdRuguJJvzT2YVk2Addv999A+bWifIS8ahiwJcTg==", "path": "iptools.core/1.6.0", "hashPath": "iptools.core.1.6.0.nupkg.sha512"}, "IPTools.International/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-3MWjpKGDMDTfAOO5m3+CFvbgIEvSKCPTIP2V96xLlSB1rTkLgf60QIPO0babYic3gU2nf8zKgp2/UGpIgGlKQQ==", "path": "iptools.international/1.6.0", "hashPath": "iptools.international.1.6.0.nupkg.sha512"}, "Json.More.Net/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-izscdjjk8EAHDBCjyz7V7n77SzkrSjh/hUGV6cyR6PlVdjYDh5ohc8yqvwSqJ9+6Uof8W6B24dIHlDKD+I1F8A==", "path": "json.more.net/2.0.2", "hashPath": "json.more.net.2.0.2.nupkg.sha512"}, "JsonPointer.Net/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-H/OtixKadr+ja1j7Fru3WG56V9zP0AKT1Bd0O7RWN/zH1bl8ZIwW9aCa4+xvzuVvt4SPmrvBu3G6NpAkNOwNAA==", "path": "jsonpointer.net/5.0.2", "hashPath": "jsonpointer.net.5.0.2.nupkg.sha512"}, "JsonSchema.Net/7.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-R0Hk2Tr/np4Q1NO8CBjyQsoiD1iFJyEQP20Sw7JnZCNGJoaSBe+g4b+nZqnBXPQhiqY5LGZ8JZwZkRh/eKZhEQ==", "path": "jsonschema.net/7.0.4", "hashPath": "jsonschema.net.7.0.4.nupkg.sha512"}, "Lazy.Captcha.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-fiEH/E2atregzfkrCsYZKS/0yjTzkv8GE+hNtZjyyJeihFY7/ic6Nw31eDOj7AlCC+jGymwBBgTlHnBnQnVvhQ==", "path": "lazy.captcha.core/2.2.0", "hashPath": "lazy.captcha.core.2.2.0.nupkg.sha512"}, "log4net/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-GT7ZYyNcBNbMENUSSQsH8HkjrELe55UOwOkxMvUBfoSyq/K/c1SPi5aXCNHFqpyeCPlrq8nVx40z9pBVwnqkmA==", "path": "log4net/3.1.0", "hashPath": "log4net.3.1.0.nupkg.sha512"}, "Magicodes.IE.Core/2.7.6": {"type": "package", "serviceable": true, "sha512": "sha512-HX6Sm1ifcl/y8rMt29O31AICoI944xp00pcXkc0MWs0WfxXRO845QRjQUfYdF32dybAtTvmbfa8XVAkHyykMjg==", "path": "magicodes.ie.core/2.7.6", "hashPath": "magicodes.ie.core.2.7.6.nupkg.sha512"}, "Magicodes.IE.EPPlus/2.7.6": {"type": "package", "serviceable": true, "sha512": "sha512-38brikUY1ULQj2RDdhqw6/tnxhBFTFizRadfnwzoQLaT29JDhk83x9OjVXExoPSi55zo/3uMtswlY+FQ8tXm7A==", "path": "magicodes.ie.epplus/2.7.6", "hashPath": "magicodes.ie.epplus.2.7.6.nupkg.sha512"}, "Magicodes.IE.Excel/2.7.6": {"type": "package", "serviceable": true, "sha512": "sha512-YekKRHrHpFRIyvDt/amex97fjH8/8jvHttw6plP3zpUEBVXXUkfCE9QsUMoturmgqYVKJQgLiD8gTrBXYwlOkw==", "path": "magicodes.ie.excel/2.7.6", "hashPath": "magicodes.ie.excel.2.7.6.nupkg.sha512"}, "Magicodes.IE.Html/2.7.6": {"type": "package", "serviceable": true, "sha512": "sha512-O7z3/ZjgjCSCqri5zoWh8Rh3TIEnezbTgxOsDOczqusc9Sby1AuzODtnCUzDsNGvTOhyol3LSamSD8koAvSpuQ==", "path": "magicodes.ie.html/2.7.6", "hashPath": "magicodes.ie.html.2.7.6.nupkg.sha512"}, "Magicodes.IE.Pdf/2.7.6": {"type": "package", "serviceable": true, "sha512": "sha512-ZWzsHjR78u8YgJgINHYdFfWTBMclo+mtKCFeIzbbFATSVaCupi/8eC4ejv99R2rtuVXFZcdClAbPfF6Q5q5Fsw==", "path": "magicodes.ie.pdf/2.7.6", "hashPath": "magicodes.ie.pdf.2.7.6.nupkg.sha512"}, "Magicodes.IE.Word/2.7.6": {"type": "package", "serviceable": true, "sha512": "sha512-5sIiw1oZW8FcTIEQuhFyu2+u48WMagyDPp7tJjV6JfB8uBW4KIEX/tdYGGbI1P6mYp96SzJRSRNPcR387pcVeg==", "path": "magicodes.ie.word/2.7.6", "hashPath": "magicodes.ie.word.2.7.6.nupkg.sha512"}, "Magicodes.RazorEngine.NetCore/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Cz7MMFjZKmdeYCJKlmtO1lpsR+3tzyScZhX+SZfXCxglp2OoIUD2zB3IyFm1+FQg2Ed1R1nLinSoVk0Zv9oLgg==", "path": "magicodes.razorengine.netcore/2.2.0", "hashPath": "magicodes.razorengine.netcore.2.2.0.nupkg.sha512"}, "MailKit/4.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-GsepEHKkaQvbAuBizlhz93yc0ihJWzVCfoerfnpCeqiKLeS6gsTKInYy3/U2wqgkGE62TKs5OKS1a90pyc+j4g==", "path": "mailkit/4.13.0", "hashPath": "mailkit.4.13.0.nupkg.sha512"}, "Mapster/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-RYGoDqvS4WTKIq0HDyPBBVIj6N0mluOCXQ1Vk95JKseMHEsbCXSEGKSlP95oL+s42IXAXbqvHj7p0YaRBUcfqg==", "path": "mapster/7.4.0", "hashPath": "mapster.7.4.0.nupkg.sha512"}, "Mapster.Core/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-11lokmfliBEMMmjeqxFGNpqGXq6tN96zFqpBmfYeahr4Ybk63oDmeJmOflsATjobYkX248g5Y62oQ2NNnZaeww==", "path": "mapster.core/1.2.1", "hashPath": "mapster.core.1.2.1.nupkg.sha512"}, "Mapster.DependencyInjection/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-LfjnRIwx6WAo3ssq8PFeaHFaUz00BfSG9BhWgXsiDa3H5lDhG0lpMGDF6w2ZnooS4eHYmAv4f77VxmzpvgorNg==", "path": "mapster.dependencyinjection/1.0.1", "hashPath": "mapster.dependencyinjection.1.0.1.nupkg.sha512"}, "Markdig.Signed/0.33.0": {"type": "package", "serviceable": true, "sha512": "sha512-/BE/XANxmocgEqajbWB/ur4Jei+j1FkXppWH9JFmEuoq8T3xJndkQKZVCW/7lTdc9Ru6kfEAkwSXFOv30EkU2Q==", "path": "markdig.signed/0.33.0", "hashPath": "markdig.signed.0.33.0.nupkg.sha512"}, "MaxMind.Db/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-3x7q8Wir8Jnrl0j4Pw00XeptEH+IpbjNS2FQJqRD/CKDPFhdkUxRNlCKd4pdpUXvYeVRr0Iy0fWkoXORJB0vCw==", "path": "maxmind.db/2.4.0", "hashPath": "maxmind.db.2.4.0.nupkg.sha512"}, "MaxMind.GeoIP2/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-O+4jtK2fA/vLXqsx7RxsaCCQfSUogYfmlFJeh3HocREXz6vs4CFjSg6m0/6CdtaUAO4dGMBjm4utlwmTI1b3jQ==", "path": "maxmind.geoip2/3.0.0", "hashPath": "maxmind.geoip2.3.0.0.nupkg.sha512"}, "MessagePack/2.5.108": {"type": "package", "serviceable": true, "sha512": "sha512-kcVRbdWP3xNWLZmmpm4DFO+kuXf6mUR2mHZ27WoZIEFIv9hazuUd80injXhNrZnlq/FklAdCsLOil5M76I4Ndg==", "path": "messagepack/2.5.108", "hashPath": "messagepack.2.5.108.nupkg.sha512"}, "MessagePack.Annotations/2.5.108": {"type": "package", "serviceable": true, "sha512": "sha512-28aNCvfJClgwaKr26gf2S6LT+C1PNyPxiG+ihYpy8uCJsRLJEDoCt2I0Uk5hqOPQ8P8hI0ESy520oMkZkPmsOQ==", "path": "messagepack.annotations/2.5.108", "hashPath": "messagepack.annotations.2.5.108.nupkg.sha512"}, "Microsoft.ApplicationInsights/2.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-btZEDWAFNo9CoYliMCriSMTX3ruRGZTtYw4mo2XyyfLlowFicYVM2Xszi5evDG95QRYV7MbbH3D2RqVwfZlJHw==", "path": "microsoft.applicationinsights/2.21.0", "hashPath": "microsoft.applicationinsights.2.21.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-Ty49uva5oIFa7nOkeL+6TGRU7DQohBaEGs+QcGoGSXq4d7MZnNueLte0HFa9WHvjZUDfJSQ1PVmWkFeIYS1w4Q==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.18", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-3AdZ+1eTsHgdsLS3n64yBTCDZumuN0zpTKNyIy7VTKJpiYgMVYa3XafOJ521oxdo/B3Ww5C0vUNzPCiY5tX3Qw==", "path": "microsoft.aspnetcore.connections.abstractions/8.0.11", "hashPath": "microsoft.aspnetcore.connections.abstractions.8.0.11.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-8OW1VlxT6m9pitB7fR/YfNbuQ0BxBJhnXde+qlr4NXEoODhE2hRxHm8rgUgxLNywftXJK/OfOJw5w5nLCXXC1w==", "path": "microsoft.aspnetcore.cryptography.internal/8.0.11", "hashPath": "microsoft.aspnetcore.cryptography.internal.8.0.11.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-J4v0esMKc29rVzO9kQPyFBGfWoBxrREe+opmbNiEPOMdG0J9ifEdNV33+06ebEjJk4/yh6c89j4AuFKPrDQ6jA==", "path": "microsoft.aspnetcore.dataprotection/8.0.11", "hashPath": "microsoft.aspnetcore.dataprotection.8.0.11.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-VKfvb4jYPj/RhBljnl3dxBbzTWtrDDM93sweewUSNLicU8BJXfTiczWTMv5feOxsTDO50BKUzBtlSUylC5Lilg==", "path": "microsoft.aspnetcore.dataprotection.abstractions/8.0.11", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.8.0.11.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.StackExchangeRedis/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-OPNZ/wpMmr4s7YWxHT2JTiWCuSV7FW2meDA11sQJ+CfJumfL1cbaSQUkwELv+XkJwx7VXdXigNMn3d4OXLwbhw==", "path": "microsoft.aspnetcore.dataprotection.stackexchangeredis/8.0.11", "hashPath": "microsoft.aspnetcore.dataprotection.stackexchangeredis.8.0.11.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y4rs5aMEXY8G7wJo5S3EEt6ltqyOTr/qOeZzfn+hw/fuQj5GppGckMY5psGLETo1U9hcT5MmAhaT5xtusM1b5g==", "path": "microsoft.aspnetcore.html.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.html.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-1c8SQxSYwhxqndMn5prT2h5gPjPz+zZ5bVE+RSmbmX/1Xub9Rf5r3xxWPw9vxW6Uo7bqCdvfs2sBmad+wva4ug==", "path": "microsoft.aspnetcore.jsonpatch/8.0.18", "hashPath": "microsoft.aspnetcore.jsonpatch.8.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-euTSUwsFr33Daw8g3BZPWJmB/PEwJhVYTYfK5/DqiukPkXCwXZhtRY0QZSuSSSApofv3n2Mdah58T2MVT1JvvA==", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/8.0.18", "hashPath": "microsoft.aspnetcore.mvc.newtonsoftjson.8.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.Razor/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V54PIyDCFl8COnTp9gezNHpUNHk7F9UnerGeZy3UfbnwYvfzbo+ipqQmSgeoESH8e0JvKhRTyQyZquW2EPtCmg==", "path": "microsoft.aspnetcore.razor/2.2.0", "hashPath": "microsoft.aspnetcore.razor.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-n5Mg5D0aRrhHJJ6bJcwKqQydIFcgUq0jTlvuynoJjwA2IvAzh8Aqf9cpYagofQbIlIXILkCP6q6FgbngyVtpYA==", "path": "microsoft.aspnetcore.razor.language/6.0.36", "hashPath": "microsoft.aspnetcore.razor.language.6.0.36.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7YqK+H61lN6yj9RiQUko7oaOhKtRR9Q/kBcoWNRemhJdTIWOh1OmdvJKzZrMWOlff3BAjejkPQm+0V0qXk+B1w==", "path": "microsoft.aspnetcore.razor.runtime/2.2.0", "hashPath": "microsoft.aspnetcore.razor.runtime.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-qUI0lN8Q0loKMF0e+46/yoYE3nRAhAzJyx6Els9aIkZJ8kjhLVvlAW/0E99donhbe5+quyyE4gVqYmx8wAP5qQ==", "path": "microsoft.aspnetcore.signalr.common/8.0.11", "hashPath": "microsoft.aspnetcore.signalr.common.8.0.11.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-nGpp2CFvG3sU2Y0SyUYfaKT6/XExgbUOZJta8jz1jv9js+oULNIQpl15mGFCHBwj7w0FNXhhu48uFF7/1tFiJQ==", "path": "microsoft.aspnetcore.signalr.protocols.newtonsoftjson/8.0.11", "hashPath": "microsoft.aspnetcore.signalr.protocols.newtonsoftjson.8.0.11.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.StackExchangeRedis/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-5EapH/IEO/EfizlCK06MSIpyJor49q0mdvl+Bql227VEOksUlXWHSP8D07NvHloLNpxocHXK2FBive65Wy16QQ==", "path": "microsoft.aspnetcore.signalr.stackexchangeredis/8.0.11", "hashPath": "microsoft.aspnetcore.signalr.stackexchangeredis.8.0.11.nupkg.sha512"}, "Microsoft.AspNetCore.StaticFiles/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-byZDrjir6Co5EoWbraQyG0qbPCUG6XgGYQstipMF9lucOAjq/mqnIyt8B8iMWnin/ghZoOln9Y01af4rUAwOhA==", "path": "microsoft.aspnetcore.staticfiles/2.2.0", "hashPath": "microsoft.aspnetcore.staticfiles.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-M5PThug7b2AdxL7xKmQs50KzAQTl9jENw5jMT3iUt16k+DAFlw1S87juU3UuPs3gvBm8trMBSOEvSFDr31c9Vw==", "path": "microsoft.codeanalysis.common/4.9.2", "hashPath": "microsoft.codeanalysis.common.4.9.2.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-HGIo7E9Mf3exAJbUdYpDFfLoYkSVaHDJXPyusWTYUTBaOPCowGw+Gap5McE1w+K+ryIXre72oiqL88sQHmHBmg==", "path": "microsoft.codeanalysis.csharp/4.9.2", "hashPath": "microsoft.codeanalysis.csharp.4.9.2.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-mtoeRMh7F/OA536c/Cnh8L4H0uLSKB5kSmoi54oN7Fp0hNJDy22IqyMhaMH4PkDCqI7xL//Fvg9ldtuPHG0h5g==", "path": "microsoft.data.sqlclient/5.2.2", "hashPath": "microsoft.data.sqlclient.5.2.2.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-po1jhvFd+8pbfvJR/puh+fkHi0GRanAdvayh/0e47yaM6CXWZ6opUjCMFuYlAnD2LcbyvQE7fPJKvogmaUcN+w==", "path": "microsoft.data.sqlclient.sni.runtime/5.2.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.2.0.nupkg.sha512"}, "Microsoft.Data.Sqlite/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lw6wthgXGx3r/U775k1UkUAWIn0kAT0wj4ZRq0WlhPx4WAOiBsIjgDKgWkXcNTGT0KfHiClkM+tyPVFDvxeObw==", "path": "microsoft.data.sqlite/9.0.0", "hashPath": "microsoft.data.sqlite.9.0.0.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cFfZjFL+tqzGYw9lB31EkV1IWF5xRQNk2k+MQd+Cf86Gl6zTeAoiZIFw5sRB1Z8OxpEC7nu+nTDsLSjieBAPTw==", "path": "microsoft.data.sqlite.core/9.0.0", "hashPath": "microsoft.data.sqlite.core.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jDM3a95WerM8g6IcMiBXq1qRS9dqmEUpgnCk2DeMWpPkYtp1ia+CkXabOnK93JmhVlUmv8l9WMPsCSUm+WqkIA==", "path": "microsoft.extensions.apidescription.server/8.0.0", "hashPath": "microsoft.extensions.apidescription.server.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FPWZAa9c0H4dvOj351iR1jkUIs4u9ykL4Bm592yhjDyO5lCoWd+TMAHx2EMbarzUvCvgjWjJIoC6//Q9kH6YhA==", "path": "microsoft.extensions.caching.abstractions/9.0.0", "hashPath": "microsoft.extensions.caching.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zbnPX/JQ0pETRSUG9fNPBvpIq42Aufvs15gGYyNIMhCun9yhmWihz0WgsI7bSDPjxWTKBf8oX/zv6v2uZ3W9OQ==", "path": "microsoft.extensions.caching.memory/9.0.0", "hashPath": "microsoft.extensions.caching.memory.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lqvd7W3FGKUO1+ZoUEMaZ5XDJeWvjpy2/M/ptCGz3tXLD4HWVaSzjufsAsjemasBEg+2SxXVtYVvGt5r2nKDlg==", "path": "microsoft.extensions.configuration.abstractions/9.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RiScL99DcyngY9zJA2ROrri7Br8tn5N4hP4YNvGdTN/bvg1A3dwvDOxHnNZ3Im7x2SJ5i4LkX1uPiR/MfSFBLQ==", "path": "microsoft.extensions.configuration.binder/9.0.0", "hashPath": "microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+6f2qv2a3dLwd5w6JanPIPs47CxRbnk+ZocMJUhv9NxP88VlOcJYZs9jY+MYSjxvady08bUZn6qgiNh7DadGgg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-mUBDZZRgZrSyFOsJ2qJJ9fXfqd/kXJwf3AiDoqLD9m6TjY5OO/vLNOb9fb4juC0487eq4hcGN/M2Rh/CKS7QYw==", "path": "microsoft.extensions.dependencymodel/8.0.2", "hashPath": "microsoft.extensions.dependencymodel.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-elH2vmwNmsXuKmUeMQ4YW9ldXiF+gSGDgg1vORksob5POnpaI6caj1Hu8zaYbEuibhqCoWg0YRWDazBY3zjBfg==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.1", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Features/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-xL0WcA9bRu49QSUmGZEYY5dslR/x52fl+XY9GWOP2h5ibOJY7hF8Tz/raXVP+GPJ2S2piNPlfL4GkJK794PjFA==", "path": "microsoft.extensions.features/8.0.11", "hashPath": "microsoft.extensions.features.8.0.11.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nHwq9aPBdBPYXPti6wYEEfgXddfBrYC+CQLn+qISiwQq5tpfaqDZSKOJNxoe9rfQxGf1c+2wC/qWFe1QYJPYqw==", "path": "microsoft.extensions.hosting.abstractions/8.0.1", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-g0UfujELzlLbHoVG8kPKVBaW470Ewi+jnptGS9KUi6jcb+k2StujtK3m26DFSGGwQ/+bVgZfsWqNzlP6YOejvw==", "path": "microsoft.extensions.logging.abstractions/9.0.0", "hashPath": "microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/8.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-oJ5DKTHz+tOypwwppLmtNaffUVDg9ouMndOdnERRRv2BeipuoLTW7i9HIbztGDU7fs/th9wFSJWM5ELw1GiGtA==", "path": "microsoft.extensions.objectpool/8.0.17", "hashPath": "microsoft.extensions.objectpool.8.0.17.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-y2146b3jrPI3Q0lokKXdKLpmXqakYbDIPDV6r3M8SqvSf45WwOTzkyfDpxnZXJsJQEpAsAqjUq5Pu8RCJMjubg==", "path": "microsoft.extensions.options/9.0.0", "hashPath": "microsoft.extensions.options.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ob3FXsXkcSMQmGZi7qP07EQ39kZpSBlTcAZLbJLdI4FIf0Jug8biv2HTavWmnTirchctPlq9bl/26CXtQRguzA==", "path": "microsoft.extensions.options.configurationextensions/9.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "path": "microsoft.extensions.primitives/9.0.0", "hashPath": "microsoft.extensions.primitives.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.WebEncoders/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8XcqYcpcdBAxUhLeyYcuKmxu4CtNQA9IphTnARpQGhkop4A93v2XgM3AtaVVJo3H2cDWxWM6aeO8HxkifREqw==", "path": "microsoft.extensions.webencoders/2.2.0", "hashPath": "microsoft.extensions.webencoders.2.2.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "path": "microsoft.identity.client/4.61.3", "hashPath": "microsoft.identity.client.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "path": "microsoft.identity.client.extensions.msal/4.61.3", "hashPath": "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-33eTIA2uO/L9utJjZWbKsMSVsQf7F8vtd6q5mQX7ZJzNvCpci5fleD6AeANGlbbb7WX7XKxq9+Dkb5e3GNDrmQ==", "path": "microsoft.identitymodel.abstractions/7.1.2", "hashPath": "microsoft.identitymodel.abstractions.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-cloLGeZolXbCJhJBc5OC05uhrdhdPL6MWHuVUnkkUvPDeK7HkwThBaLZ1XjBQVk9YhxXE2OvHXnKi0PLleXxDg==", "path": "microsoft.identitymodel.jsonwebtokens/7.1.2", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-YCxBt2EeJP8fcXk9desChkWI+0vFqFLvBwrz5hBMsoh0KJE6BC66DnzkdzkJNqMltLromc52dkdT206jJ38cTw==", "path": "microsoft.identitymodel.logging/7.1.2", "hashPath": "microsoft.identitymodel.logging.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-SydLwMRFx6EHPWJ+N6+MVaoArN1Htt92b935O3RUWPY1yUF63zEjvd3lBu79eWdZUwedP8TN2I5V9T3nackvIQ==", "path": "microsoft.identitymodel.protocols/7.1.2", "hashPath": "microsoft.identitymodel.protocols.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-6lHQoLXhnMQ42mGrfDkzbIOR3rzKM1W1tgTeMPLgLCqwwGw0d96xFi/UiX/fYsu7d6cD5MJiL3+4HuI8VU+sVQ==", "path": "microsoft.identitymodel.protocols.openidconnect/7.1.2", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-oICJMqr3aNEDZOwnH5SK49bR6Z4aX0zEAnOLuhloumOSuqnNq+GWBdQyrgILnlcT5xj09xKCP/7Y7gJYB+ls/g==", "path": "microsoft.identitymodel.tokens/7.1.2", "hashPath": "microsoft.identitymodel.tokens.7.1.2.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s/s20YTVY9r9TPfTrN5g8zPF1YhwxyqO6PxUkrYTGI2B+OGPe9AdajWZrLhFqXIvqIW23fnUE4+ztrUWNU1+9g==", "path": "microsoft.io.recyclablememorystream/3.0.1", "hashPath": "microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512"}, "Microsoft.Management.Infrastructure/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cGZi0q5IujCTVYKo9h22Pw+UwfZDV82HXO8HTxMG2HqntPlT3Ls8jY6punLp4YzCypJNpfCAu2kae3TIyuAiJw==", "path": "microsoft.management.infrastructure/3.0.0", "hashPath": "microsoft.management.infrastructure.3.0.0.nupkg.sha512"}, "Microsoft.Management.Infrastructure.CimCmdlets/7.4.11": {"type": "package", "serviceable": true, "sha512": "sha512-o5DYLdXlQUYjpuDtXUX2KSVMNJX7ewFdHj/gIyGZxdfpJljtSIWFgfKRQMLp43xbeCVVPz/fSzV/W3ic+cDQqw==", "path": "microsoft.management.infrastructure.cimcmdlets/7.4.11", "hashPath": "microsoft.management.infrastructure.cimcmdlets.7.4.11.nupkg.sha512"}, "Microsoft.Management.Infrastructure.Runtime.Unix/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QZE3uEDvZ0m7LabQvcmNOYHp7v1QPBVMpB/ild0WEE8zqUVAP5y9rRI5we37ImI1bQmW5pZ+3HNC70POPm0jBQ==", "path": "microsoft.management.infrastructure.runtime.unix/3.0.0", "hashPath": "microsoft.management.infrastructure.runtime.unix.3.0.0.nupkg.sha512"}, "Microsoft.Management.Infrastructure.Runtime.Win/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwMyWN33+iQ8Wm/n1yoPXgFoiYNd0HzJyoqSVhaQZyJfaQrJR3udgcIHjqa1qbc3lS6kvfuUMN4TrF4U4refCQ==", "path": "microsoft.management.infrastructure.runtime.win/3.0.0", "hashPath": "microsoft.management.infrastructure.runtime.win.3.0.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NET.StringTools/17.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-06T6Hqfs3JDIaBvJaBRFFMIdU7oE0OMab5Xl8LKQjWPxBQr3BgVFKMQPTC+GsSEuYREWmK6g5eOd7Xqd9p1YCA==", "path": "microsoft.net.stringtools/17.4.0", "hashPath": "microsoft.net.stringtools.17.4.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-TMBuzAHpTenGbGgk0SMTwyEkyijY/Eae4ZGsFNYJvAr/LDn1ku3Etp3FPxChmDp5HHF3kzJuoaa08N0xjqAJfQ==", "path": "microsoft.netcore.platforms/1.1.1", "hashPath": "microsoft.netcore.platforms.1.1.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.OpenApi/1.6.23": {"type": "package", "serviceable": true, "sha512": "sha512-tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g==", "path": "microsoft.openapi/1.6.23", "hashPath": "microsoft.openapi.1.6.23.nupkg.sha512"}, "Microsoft.PowerShell.Commands.Diagnostics/7.4.11": {"type": "package", "serviceable": true, "sha512": "sha512-mRav2dNPfMwaq9yqkB16m6crMcQ8foeh8ckkcZRqWvxpG/FLOHyjTG/Fvw9ZR/3bALucB7NhW5IC3hPRnFfUNQ==", "path": "microsoft.powershell.commands.diagnostics/7.4.11", "hashPath": "microsoft.powershell.commands.diagnostics.7.4.11.nupkg.sha512"}, "Microsoft.PowerShell.Commands.Management/7.4.11": {"type": "package", "serviceable": true, "sha512": "sha512-7GBO3AGyx15DxDp/NCbRiFkxbdFmGMWoP/5dL13mqdB8qrsDBxJjhCL4k0Arpjq9zvv2qaE4n5ttqW2fRftkrw==", "path": "microsoft.powershell.commands.management/7.4.11", "hashPath": "microsoft.powershell.commands.management.7.4.11.nupkg.sha512"}, "Microsoft.PowerShell.Commands.Utility/7.4.11": {"type": "package", "serviceable": true, "sha512": "sha512-PW3+D4xhyZXRWVNmyHS6MyllvDMKtGdvB8W6uaWrF/EcLqVwEvHug0D/6sX1dyIViQ8ECIvIk3stYa1T5DqMWw==", "path": "microsoft.powershell.commands.utility/7.4.11", "hashPath": "microsoft.powershell.commands.utility.7.4.11.nupkg.sha512"}, "Microsoft.PowerShell.ConsoleHost/7.4.11": {"type": "package", "serviceable": true, "sha512": "sha512-tu+7+X2899sQ2hjEA/RFJ+reSKV1tUFdQIrkb8GsNkQ5F3AI441gLWJs6JkQLJt7JQif4f1VzBTwKdFYix3oFg==", "path": "microsoft.powershell.consolehost/7.4.11", "hashPath": "microsoft.powershell.consolehost.7.4.11.nupkg.sha512"}, "Microsoft.PowerShell.CoreCLR.Eventing/7.4.11": {"type": "package", "serviceable": true, "sha512": "sha512-V24hbrrpdGUlzFXtlNoMsqSlHXEtGUNGYYXF/g2ux9EkmaIzFxYEbmzP8VjVbfHwD/zFFl1yeBvqrj0lE7BaKg==", "path": "microsoft.powershell.coreclr.eventing/7.4.11", "hashPath": "microsoft.powershell.coreclr.eventing.7.4.11.nupkg.sha512"}, "Microsoft.PowerShell.MarkdownRender/7.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-o5oUwL23R/KnjQPD2Oi49WAG5j4O4VLo1fPRSyM/aq0HuTrY2RnF4B3MCGk13BfcmK51p9kPlHZ1+8a/ZjO4Jg==", "path": "microsoft.powershell.markdownrender/7.2.1", "hashPath": "microsoft.powershell.markdownrender.7.2.1.nupkg.sha512"}, "Microsoft.PowerShell.Native/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-FlaJ3JBWhqFToYT0ycMb/Xxzoof7oTQbNyI4UikgubC7AMWt5ptBNKjIAMPvOcvEHr+ohaO9GvRWp3tiyS3sKw==", "path": "microsoft.powershell.native/7.4.0", "hashPath": "microsoft.powershell.native.7.4.0.nupkg.sha512"}, "Microsoft.PowerShell.SDK/7.4.11": {"type": "package", "serviceable": true, "sha512": "sha512-B8HM+dRPTALtgrC+eLEH4si3YQ+3ysmOXGo4dPwTKebHYUwKP17p7WGx6Jwk15EO3RmegYP7z2QhtrJEXqJCWQ==", "path": "microsoft.powershell.sdk/7.4.11", "hashPath": "microsoft.powershell.sdk.7.4.11.nupkg.sha512"}, "Microsoft.PowerShell.Security/7.4.11": {"type": "package", "serviceable": true, "sha512": "sha512-9mJnk2WnNIkQw6BbrM3Ht1n9P4lFcD/uWWSwEdYCLLd/yir2ZsxVscLRS8bqY0AlAqVon66M4Qa4Hj1RGXVokw==", "path": "microsoft.powershell.security/7.4.11", "hashPath": "microsoft.powershell.security.7.4.11.nupkg.sha512"}, "Microsoft.Security.Extensions/1.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-MnHXttc0jHbRrGdTJ+yJBbGDoa4OXhtnKXHQw70foMyAooFtPScZX/dN+Nib47nuglc9Gt29Gfb5Zl+1lAuTeA==", "path": "microsoft.security.extensions/1.4.0", "hashPath": "microsoft.security.extensions.1.4.0.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry.AccessControl/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-u8PB9/v02C8mBXzl0vJ7bOyC020zOP+T1mRct+KA46DqZkB40XtsNn9pGD0QowTRsT6R4jPCghn+yAODn2UMMw==", "path": "microsoft.win32.registry.accesscontrol/8.0.0", "hashPath": "microsoft.win32.registry.accesscontrol.8.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "path": "microsoft.win32.systemevents/8.0.0", "hashPath": "microsoft.win32.systemevents.8.0.0.nupkg.sha512"}, "Microsoft.Windows.Compatibility/8.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-wOWpuIqntcxwewoSpxtz8YUAfi7zeZVZj76bBrD6UCFtVCb/xCvJYeF5tjsa5u9195WzQantjr9LLT6i7y+bXw==", "path": "microsoft.windows.compatibility/8.0.17", "hashPath": "microsoft.windows.compatibility.8.0.17.nupkg.sha512"}, "Microsoft.WSMan.Management/7.4.11": {"type": "package", "serviceable": true, "sha512": "sha512-qmQ/HKUu7CubbiH2uXYF096E+OTb1w0VZmN7yLqcu2NI/17N51StcQfeEiuZKs3/vpQcHMSL3gDNzRFi0MxkGg==", "path": "microsoft.wsman.management/7.4.11", "hashPath": "microsoft.wsman.management.7.4.11.nupkg.sha512"}, "Microsoft.WSMan.Runtime/7.4.11": {"type": "package", "serviceable": true, "sha512": "sha512-HVN1Zkt/4WIT2YLOFwCNyNfbnGlwybpZ52Qcal8QeDB3WEfdF0FmN8OJ6zyr1Nkl+SzR5dj67Ej8mQrqEX7RfQ==", "path": "microsoft.wsman.runtime/7.4.11", "hashPath": "microsoft.wsman.runtime.7.4.11.nupkg.sha512"}, "MimeKit/4.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-oa4JuhAzJydHnPCc/XWeyBUGd3uiVyWW0NXqOVgkXEHjbHlPVBssklK3mpw9sokjzAaBGdj0bceFsr+NXvAukA==", "path": "mimekit/4.13.0", "hashPath": "mimekit.4.13.0.nupkg.sha512"}, "MiniExcel/1.41.3": {"type": "package", "serviceable": true, "sha512": "sha512-wa6aS2+4Yc9KX0rZRU1M56rjAI3zqIiQWxiL3aNtW/3rq+owh1wwHca7DHfVnNp9XCExMm1tTik3cpUwJvjwqA==", "path": "miniexcel/1.41.3", "hashPath": "miniexcel.1.41.3.nupkg.sha512"}, "Minio/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7tZj90WEuuH60RAP4wBYexjMuJOhCnK7I46hCiX3CtZPackHisLZ8aAJmn3KlwbUX22dBDphwemD+h37vet8Qw==", "path": "minio/5.0.0", "hashPath": "minio.5.0.0.nupkg.sha512"}, "MiniWord/0.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-TDcYSy7bdgRJqMAybx0yxCuQpIdZhPDjImxsZ2kOU+WWeT8FzAcaxYeeKm0JXP9O3FtHeBVbPVuh/fAuQIsjOQ==", "path": "miniword/0.9.2", "hashPath": "miniword.0.9.2.nupkg.sha512"}, "MongoDB.Bson/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6y1uFFmU3O7tIAOcWmOkFImar+VtSeaia3bzaZTDXc30CP1zsPaUNLhM8CzUCzEzGc02rQlEaWQwV/LzA0NlXA==", "path": "mongodb.bson/3.3.0", "hashPath": "mongodb.bson.3.3.0.nupkg.sha512"}, "MongoDB.Driver/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1EVdMMTsUllMJC+4BPQlcxGzn1pB43vAQ03VCx0QpfbhzaYTqnuHKBp3rAPG5aSo2az4JXKXwrQca7GhP/NhUQ==", "path": "mongodb.driver/3.3.0", "hashPath": "mongodb.driver.3.3.0.nupkg.sha512"}, "MySqlConnector/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-6sinY78RvryhHwpup3awdjYO7d5hhWahb5p/1VDODJhSxJggV/sBbYuKK5IQF9TuzXABiddqUbmRfM884tqA3Q==", "path": "mysqlconnector/2.2.5", "hashPath": "mysqlconnector.2.2.5.nupkg.sha512"}, "NewLife.Core/11.5.2025.701": {"type": "package", "serviceable": true, "sha512": "sha512-6n7N/Ef4FcLLiqZo02G3dGeLaxjb57lezKcVWxro6dJrWf3uXL8Nf9XHNP0eBCCdSVTkhbED9f5nGDBCYTdX2Q==", "path": "newlife.core/11.5.2025.701", "hashPath": "newlife.core.11.5.2025.701.nupkg.sha512"}, "NewLife.Redis/6.3.2025.701": {"type": "package", "serviceable": true, "sha512": "sha512-3qcQ7Tp2L0yymLjRvvhpqwMnOtTNgK9kiSx70As2/bFch21sz0m/RKziUlpsESINrvGnxYv+fEHLcUyjhB71Nw==", "path": "newlife.redis/6.3.2025.701", "hashPath": "newlife.redis.6.3.2025.701.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "Novell.Directory.Ldap.NETStandard/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qyaOhuyv5nhp35yK3X0cBa+pnuVyOqec37LKQDzRSmFPECn6d1cnxBCrJdaqgaWuvOQtWa1rqlEaZyp+lpcXZQ==", "path": "novell.directory.ldap.netstandard/4.0.0", "hashPath": "novell.directory.ldap.netstandard.4.0.0.nupkg.sha512"}, "Npgsql/5.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-1u4iCPKL9wtPeSUzChIbgq/BlOhvf44o7xASacdpMY7z0PbqACKpNOF0fjEn9jDV/AJl/HtPY6zk5qasQ1URhw==", "path": "npgsql/5.0.18", "hashPath": "npgsql.5.0.18.nupkg.sha512"}, "OnceMi.AspNetCore.OSS/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ESndB5bQ37n+iTMCCL/HGll0+WhXuHeErfMaUbhnGM/ib0koFMY91cUe1wwLWLTi8EKucQgdAzc/im4+QS5zew==", "path": "oncemi.aspnetcore.oss/1.2.0", "hashPath": "oncemi.aspnetcore.oss.1.2.0.nupkg.sha512"}, "Oracle.ManagedDataAccess.Core/23.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-wgCcndZnwbWySb4Bm0UOAJO8wOagFAs1IG8Aa4ZX38D9N9MrySfryGKJd+yajb2CDGXv/yL3x7yES6mmF4OMWw==", "path": "oracle.manageddataaccess.core/23.8.0", "hashPath": "oracle.manageddataaccess.core.23.8.0.nupkg.sha512"}, "Oscar.Data.SqlClient/4.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-VJ3xVvRjxrPi/mMPT5EqYiMZor0MjFu83mw1qvUveBFWJSudGh9BOKZq7RkhqeNCcL1ud0uK0/TVkw+xTa4q4g==", "path": "oscar.data.sqlclient/4.0.4", "hashPath": "oscar.data.sqlclient.4.0.4.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "path": "pipelines.sockets.unofficial/2.2.8", "hashPath": "pipelines.sockets.unofficial.2.2.8.nupkg.sha512"}, "Portable.BouncyCastle/*******": {"type": "package", "serviceable": true, "sha512": "sha512-iLfdr+T33QrDpsvcKRbMVk0PnjqE4hcrbgc6IH+A1BdPAThZlMB9s5TtFANhVVtdS+0oN3yIivgXRPRET7zuZw==", "path": "portable.bouncycastle/*******", "hashPath": "portable.bouncycastle.*******.nupkg.sha512"}, "Qiniu/8.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-SdmxJ/W/32zWB5/tkUq9Qcn/XA2FW2wdOsBqbJBQ8tN1lWyb/4bD+DdWKy9WGzVrVmxtCxmzWeiJ14p1//ufcA==", "path": "qiniu/8.3.1", "hashPath": "qiniu.8.3.1.nupkg.sha512"}, "QRCoder/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-XmPA81eo+oRxBuyVdswsSkTGTE1d3thfF11Z1PdD7oB56A6HU4G4AAOdySmGRMb/cljwlFTMWUtosGEnwpS6GA==", "path": "qrcoder/1.6.0", "hashPath": "qrcoder.1.6.0.nupkg.sha512"}, "RabbitMQ.Client/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-y3c6ulgULScWthHw5PLM1ShHRLhxg0vCtzX/hh61gRgNecL3ZC3WoBW2HYHoXOVRqTl99Br9E7CZEytGZEsCyQ==", "path": "rabbitmq.client/7.1.2", "hashPath": "rabbitmq.client.7.1.2.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gK720fg6HemDg8sXcfy+xCMZ9+hF78Gc7BmREbmkS4noqlu1BAr9qZtuWGhLzFjBfgecmdtl4+SYVwJ1VneZBQ==", "path": "runtime.linux-arm.runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-KYG6/3ojhEWbb3FwQAKgGWPHrY+HKUXXdVjJlrtyCLn3EMcNTaNcPadb2c0ndQzixZSmAxZKopXJr0nLwhOrpQ==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wnw5vhA4mgGbIFoo6l9Fk3iEcwRSq49a1aKwJgXUCUtEQLCSUDjTGSxqy/oMUuOyyn7uLHsH8KgZzQ1y3lReiQ==", "path": "runtime.linux-x64.runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-A8v6PGmk+UGbfWo5Ixup0lPM4swuSwOiayJExZwKIOjTlFFQIsu3QnDXECosBEyrWSPryxBVrdqtJyhK3BaupQ==", "path": "runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ee7Sz5llLpTgyKIWzKI/GeuRSbFkOABgJRY00SqTY0OkTYtkB+9l5rFZfE7fxPA3c22RfytCBYkUdAkcmwMjQg==", "path": "runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rbUBLAaFW9oVkbsb0+XSrAo2QdhBeAyzLl5KQ6Oci9L/u626uXGKInsVJG6B9Z5EO8bmplC8tsMiaHK8wOBZ+w==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IcfB4jKtM9pkzP9OpYelEcUX1MiDt0IJPBh3XYYdEISFF+6Mc+T8WWi0dr9wVh1gtcdVjubVEIBgB8BHESlGfQ==", "path": "runtime.osx-x64.runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-8eJkROLGh6DBQLR666q2aOpAaean2puZaZ6Ur9YxoyGzjaZhv8OJSxtnDou54+OXMkXtLUdyQC0so47sOsqZjg==", "path": "sixlabors.imagesharp/3.1.8", "hashPath": "sixlabors.imagesharp.3.1.8.nupkg.sha512"}, "SixLabors.ImageSharp.Web/3.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-xC0gpnV14tY1fXtmhOoyhqf0XSy9g48wLzRS89SRgOHkO8enLJDjATv4ghklSsjMtRZuseYHkYXGty80Jp2Eow==", "path": "sixlabors.imagesharp.web/3.1.5", "hashPath": "sixlabors.imagesharp.web.3.1.5.nupkg.sha512"}, "SkiaSharp/3.119.0": {"type": "package", "serviceable": true, "sha512": "sha512-gR9yVoOta2Mc1Rxt15LD65AckfHMfwjIs/3kkD59C9bT2nYYISsE6uz3t4aMPNHA6CgsIL0Ssn+jE5OVilZ1yw==", "path": "skiasharp/3.119.0", "hashPath": "skiasharp.3.119.0.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux.NoDependencies/3.119.0": {"type": "package", "serviceable": true, "sha512": "sha512-e92vdqf1VOETPjy1T67Fs1zPxfGMM1nbrpt69GM5foXSI/iIbq6L9avPz/bl/DbWtb81D0yF/NKjRmXuOZoLcg==", "path": "skiasharp.nativeassets.linux.nodependencies/3.119.0", "hashPath": "skiasharp.nativeassets.linux.nodependencies.3.119.0.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/3.119.0": {"type": "package", "serviceable": true, "sha512": "sha512-YE1vNn0Nyw2PWtv7hw1PYkKJO0itFiQp9vSqGppZUKzQJqwp28a2jgdCMPfYtOiR8KCnDgZqQoynqJRRaE2ZVg==", "path": "skiasharp.nativeassets.macos/3.119.0", "hashPath": "skiasharp.nativeassets.macos.3.119.0.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/3.119.0": {"type": "package", "serviceable": true, "sha512": "sha512-IwC9yx36lOdXVT2DjgmWHl1qkVspfj8ctd4+li8CNnvqdfaTolXCOh6TLznURcPAvzatx9K/tLOB7zT6T8EA9w==", "path": "skiasharp.nativeassets.win32/3.119.0", "hashPath": "skiasharp.nativeassets.win32.3.119.0.nupkg.sha512"}, "SKIT.FlurlHttpClient.Common/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZCcSa2+ZrZKwAXvYBdMmzio/SgPFwqB6xjyRvWjjv+P8V5QRk41tMNfjXreKwWsRhj25/mAdKZLc5YfOsofQUQ==", "path": "skit.flurlhttpclient.common/3.1.1", "hashPath": "skit.flurlhttpclient.common.3.1.1.nupkg.sha512"}, "SKIT.FlurlHttpClient.Wechat.Api/3.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-jqMYrZ3ZSAYQ9s72syw1wzBxd58Uu+zlQgl/MDAcQ+7aD2rnGJGAaiH/zC11LLrxDFgxCJwFYNpeGFTmxyWRVw==", "path": "skit.flurlhttpclient.wechat.api/3.11.0", "hashPath": "skit.flurlhttpclient.wechat.api.3.11.0.nupkg.sha512"}, "SKIT.FlurlHttpClient.Wechat.TenpayV3/3.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-nuKt8FN3HbrwblV2TtfrsVnwnMftQmKh2F0O8+IfmeAe13QKlk910mCtF+abpNki6b2/8/tiqkig2a8B6kEJmA==", "path": "skit.flurlhttpclient.wechat.tenpayv3/3.13.0", "hashPath": "skit.flurlhttpclient.wechat.tenpayv3.3.13.0.nupkg.sha512"}, "Snappier/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "path": "snappier/1.0.0", "hashPath": "snappier.1.0.0.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "path": "sqlitepclraw.core/2.1.10", "hashPath": "sqlitepclraw.core.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512"}, "SqlSugar.MongoDbCore/*********": {"type": "package", "serviceable": true, "sha512": "sha512-KlDvd+wzwt3I2m5txqpezP2UNaNKp6C8QEfnRHsDubVWiaGHEQfTAJjVZyfCMbqXrUl4l5RRw0PKpIN2U0Heow==", "path": "sqlsugar.mongodbcore/*********", "hashPath": "sqlsugar.mongodbcore.*********.nupkg.sha512"}, "SqlSugarCore/*********": {"type": "package", "serviceable": true, "sha512": "sha512-6ZRn5ii/X5JD96KT3uc+A/xUQh2YLRW8k3/N4bKNrrvV0AlIV6EMU9fwXzcqy5cwRJA0rTPlSUnnawpefeWS6g==", "path": "sqlsugarcore/*********", "hashPath": "sqlsugarcore.*********.nupkg.sha512"}, "SqlSugarCore.Dm/8.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-M1Wh/JdK/UgaJe9m76/EKTrV0d18sBDvsgh7FCmRB8U0oJYA0pkFJWLchqi9CMYv1RJjc3f4tB0tFzKjlRV5rQ==", "path": "sqlsugarcore.dm/8.8.0", "hashPath": "sqlsugarcore.dm.8.8.0.nupkg.sha512"}, "SqlSugarCore.Kdbndp/9.3.7.613": {"type": "package", "serviceable": true, "sha512": "sha512-kfG7lNyEI6UI9TtmMww9kYRsVOfeUuuf4rFu/0e7ZtUGi+JIS5m/D1hyTEAjYtTVGZp2CxFoVb/1/c4t7t5Qow==", "path": "sqlsugarcore.kdbndp/9.3.7.613", "hashPath": "sqlsugarcore.kdbndp.9.3.7.613.nupkg.sha512"}, "SSH.NET/2025.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AKYbB+q2zFkNQbBFx5gXdv+Wje0baBtADQ35WnMKi4bg1ka74wTQtWoPd+fOWcydohdfsD0nfT8ErMOAPxtSfA==", "path": "ssh.net/2025.0.0", "hashPath": "ssh.net.2025.0.0.nupkg.sha512"}, "StackExchange.Redis/2.7.27": {"type": "package", "serviceable": true, "sha512": "sha512-Uqc2OQHglqj9/FfGQ6RkKFkZfHySfZlfmbCl+hc+u2I/IqunfelQ7QJi7ZhvAJxUtu80pildVX6NPLdDaUffOw==", "path": "stackexchange.redis/2.7.27", "hashPath": "stackexchange.redis.2.7.27.nupkg.sha512"}, "Swashbuckle.AspNetCore/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-Akk4oFgy0ST8Q8pZTfPbrt045tWNyMMiKhlbYjG3qnjQZLz645IL5vhQm7NLicc2sAAQ+vftArIlsYWFevmb2g==", "path": "swashbuckle.aspnetcore/9.0.3", "hashPath": "swashbuckle.aspnetcore.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-CGpkZDWj1g/yH/0wYkxUtBhiFo5TY/Esq2fS0vlBvLOs1UL2Jzef9tdtYmTdd3zBPtnMyXQcsXjMt9yCxz4VaA==", "path": "swashbuckle.aspnetcore.swagger/9.0.3", "hashPath": "swashbuckle.aspnetcore.swagger.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-STqjhw1TZiEGmIRgE6jcJUOcgU/Fjquc6dP4GqbuwBzqWZAWr/9T7FZOGWYEwKnmkMplzlUNepGHwnUrfTP0fw==", "path": "swashbuckle.aspnetcore.swaggergen/9.0.3", "hashPath": "swashbuckle.aspnetcore.swaggergen.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-DgJKJASz5OAygeKv2+N0FCZVhQylESqLXrtrRAqIT0vKpX7t5ImJ1FL6+6OqxKiamGkL0jchRXR8OgpMSsMh8w==", "path": "swashbuckle.aspnetcore.swaggerui/9.0.3", "hashPath": "swashbuckle.aspnetcore.swaggerui.9.0.3.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.CodeDom/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WTlRjL6KWIMr/pAaq3rYqh0TJlzpouaQ/W1eelssHgtlwHAH25jXTkUphTYx9HaIIf7XA6qs/0+YhtLEQRkJ+Q==", "path": "system.codedom/8.0.0", "hashPath": "system.codedom.8.0.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-0YFqjhp/mYkDGpU0Ye1GjE53HMp9UVfGN7seGpAMttAC0C40v5gw598jCgpbBLMmCo0E5YRLBv5Z2doypO49ZQ==", "path": "system.componentmodel.annotations/4.7.0", "hashPath": "system.componentmodel.annotations.4.7.0.nupkg.sha512"}, "System.ComponentModel.Composition/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bGhUX5BTivJ9Wax0qnJy7uGq7dn/TQkEpJ2Fpu1etg8dbPwyDkUzNPc1d3I2/jUr9y4wDI3a1dkSmi8X21Pzbw==", "path": "system.componentmodel.composition/8.0.0", "hashPath": "system.componentmodel.composition.8.0.0.nupkg.sha512"}, "System.ComponentModel.Composition.Registration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-BVMXYqX7Z0Zdq3tc94UKJL/cOWq4LF3ufexfdPuUDrDl4ekbbfwPVzsusVbx+aq6Yx60CJnmJLyHtM3V2Q7BBQ==", "path": "system.componentmodel.composition.registration/8.0.0", "hashPath": "system.componentmodel.composition.registration.8.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-gPYFPDyohW2gXNhdQRSjtmeS6FymL2crg4Sral1wtvEJ7DUqFCDWDVbbLobASbzxfic8U1hQEdC7hmg9LHncMw==", "path": "system.configuration.configurationmanager/8.0.1", "hashPath": "system.configuration.configurationmanager.8.0.1.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Data.Odbc/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-JQd0QHOaZuH+ki+4Geas88dnLe/lZSaEYYmRdovZaqNVuExVlVFs/of2I1VaasMxzbO5+yrGDAP2rkazx/b8Sg==", "path": "system.data.odbc/8.0.1", "hashPath": "system.data.odbc.8.0.1.nupkg.sha512"}, "System.Data.OleDb/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-RO+/y2ggU5956uQDRXdjA1e2l5yJ4rTWNX76eZ+3sgtYGqGapCe2kQCyiUci+/y6Fyb21Irp4RQEdfrIiuYrxQ==", "path": "system.data.oledb/8.0.1", "hashPath": "system.data.oledb.8.0.1.nupkg.sha512"}, "System.Data.SqlClient/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-j4KJO+vC62NyUtNHz854njEqXbT8OmAa5jb1nrGfYWBOcggyYUQE0w/snXeaCjdvkSKWuUD+hfvlbN8pTrJTXg==", "path": "system.data.sqlclient/4.9.0", "hashPath": "system.data.sqlclient.4.9.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ddppcFpnbohLWdYKr/ZeLZHmmI+DXFgZ3Snq+/E7SwcdW4UnvxmaugkwGywvGVWkHPGCSZjCP+MLzu23AL5SDw==", "path": "system.diagnostics.diagnosticsource/9.0.0", "hashPath": "system.diagnostics.diagnosticsource.9.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-hH83Rt+x3eezRIsyCgZ15vfulTjphrzZmaT/g2y0+AS6hjvcWHGK0y9lBofgtGZQugXxDZz26JD9U8LuU4lI8g==", "path": "system.diagnostics.eventlog/8.0.2", "hashPath": "system.diagnostics.eventlog.8.0.2.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-9RfEDiEjlUADeThs8IPdDVTXSnPRSqjfgTQJALpmGFPKC0k2mbdufOXnb/9JZ4I0TkmxOfy3VTJxrHOJSs8cXg==", "path": "system.diagnostics.performancecounter/8.0.1", "hashPath": "system.diagnostics.performancecounter.8.0.1.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.DirectoryServices/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7nit//efUTy1OsAKco2f02PMrwsR2S234N0dVVp84udC77YcvpOQDz5znAWMtgMWBzY1aRJvUW61jo/7vQRfXg==", "path": "system.directoryservices/8.0.0", "hashPath": "system.directoryservices.8.0.0.nupkg.sha512"}, "System.DirectoryServices.AccountManagement/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qVDWKClyDY+rHVEnf11eU4evW25d5OeidrtMPSJv+fwG213wa2zJ+AuIFCxsuvNSCFyHo+DvQIVfBcoK3CL1pA==", "path": "system.directoryservices.accountmanagement/8.0.1", "hashPath": "system.directoryservices.accountmanagement.8.0.1.nupkg.sha512"}, "System.DirectoryServices.Protocols/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-7pSU+qkmdkwbikAvn4kpvClfyffaR7dYJw7ucBtO5d6m/7wgKZIULOZAGFLMIMOFFmg7xC67TYKptHxcbqQm+Q==", "path": "system.directoryservices.protocols/8.0.2", "hashPath": "system.directoryservices.protocols.8.0.2.nupkg.sha512"}, "System.Drawing.Common/8.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-DZVB52l2vcgE2ROngL+MN6wp6RVy9v0wnQ/VFOjB6Ipu55L2aNsI6tM+lAfo1/Q/zJ3CsNVmsGBEaSMhECyiXw==", "path": "system.drawing.common/8.0.17", "hashPath": "system.drawing.common.8.0.17.nupkg.sha512"}, "System.Formats.Asn1/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-yUsFqNGa7tbwm5QOOnOR3VSoh8a0Yki39mTbhOnErdbg8hVSFtrK0EXerj286PXcegiF1LkE7lL++qqMZW5jIQ==", "path": "system.formats.asn1/8.0.2", "hashPath": "system.formats.asn1.8.0.2.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-Thhbe1peAmtSBFaV/ohtykXiZSOkx59Da44hvtWfIMFofDA3M3LaVyjstACf2rKGn4dEDR2cUpRAZ0Xs/zB+7Q==", "path": "system.identitymodel.tokens.jwt/7.1.2", "hashPath": "system.identitymodel.tokens.jwt.7.1.2.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Hashing/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-sDnWM0N3AMCa86LrKTWeF3BZLD2sgWyYUc7HL6z4+xyDZNQRwzmxbo4qP2rX2MqC+Sy1/gOSRDah5ltxY5jPxw==", "path": "system.io.hashing/7.0.0", "hashPath": "system.io.hashing.7.0.0.nupkg.sha512"}, "System.IO.Packaging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KYkIOAvPexQOLDxPO2g0BVoWInnQhPpkFzRqvNrNrMhVT6kqhVr0zEb6KCHlptLFukxnZrjuMVAnxK7pOGUYrw==", "path": "system.io.packaging/8.0.1", "hashPath": "system.io.packaging.8.0.1.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MaiPbx2/QXZc62gm/DrajRrGPG1lU4m08GWMoWiymPYM+ba4kfACp2PbiYpqJ4QiFGhHD00zX3RoVDTucjWe9g==", "path": "system.io.ports/8.0.0", "hashPath": "system.io.ports.8.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.6.6": {"type": "package", "serviceable": true, "sha512": "sha512-DalpBR6IiZsT61Z9h3ujGxhOALmJUE6hrbeWjRUL24AxDIvhxzhxftSrRrF1G8vNggpadDfRiej6ImrpYS8sJA==", "path": "system.linq.dynamic.core/1.6.6", "hashPath": "system.linq.dynamic.core.1.6.6.nupkg.sha512"}, "System.Management/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jrK22i5LRzxZCfGb+tGmke2VH7oE0DvcDlJ1HAKYU8cPmD8XnpUT0bYn2Gy98GEhGjtfbR/sxKTVb+dE770pfA==", "path": "system.management/8.0.0", "hashPath": "system.management.8.0.0.nupkg.sha512"}, "System.Management.Automation/7.4.11": {"type": "package", "serviceable": true, "sha512": "sha512-jZM3gN5a1BeuTEUAzTfQOBWeiEorLN0WscJtT5QWHbGKTEuu4flkgPLW9av5yFo4j/YQle1LgwwSbMUffdIznQ==", "path": "system.management.automation/7.4.11", "hashPath": "system.management.automation.7.4.11.nupkg.sha512"}, "System.Memory/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-OEkbBQoklHngJ8UD8ez2AERSk2g+/qpAaSWWCBFbpH727HxDq5ydVkuncBaKcKfwRqXGWx64dS6G1SUScMsitg==", "path": "system.memory/4.6.0", "hashPath": "system.memory.4.6.0.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Net.Http/4.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "path": "system.net.http/4.3.4", "hashPath": "system.net.http.4.3.4.nupkg.sha512"}, "System.Net.Http.WinHttpHandler/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-1<PERSON><PERSON>lowez3KdQ4Fy0scIyr+TLkU2+6JBl1pI2Ht/X1qtqq154PcRwzsro0g2KgSwqe1YZANT+BqXOSHtH0RC1Sw==", "path": "system.net.http.winhttphandler/8.0.3", "hashPath": "system.net.http.winhttphandler.8.0.3.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Private.ServiceModel/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-BcUV7OERlLqGxDXZuIyIMMmk1PbqBblLRbAoigmzIUx/M8A+8epvyPyXRpbgoucKH7QmfYdQIev04Phx2Co08A==", "path": "system.private.servicemodel/4.10.3", "hashPath": "system.private.servicemodel.4.10.3.nupkg.sha512"}, "System.Private.Uri/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-o1+7RJnu3Ik3PazR7Z7tJhjPdE000Eq2KGLLWhqJJKXj04wrS8lwb1OFtDF9jzXXADhUuZNJZlPc98uwwqmpFA==", "path": "system.private.uri/4.3.2", "hashPath": "system.private.uri.4.3.2.nupkg.sha512"}, "System.Reactive/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-erBZjkQHWL9jpasCE/0qKAryzVBJFxGHVBAvgRN1bzM0q2s1S4oYREEEL0Vb+1kA/6BKb5FjUZMp5VXmy+gzkQ==", "path": "system.reactive/5.0.0", "hashPath": "system.reactive.5.0.0.nupkg.sha512"}, "System.Reactive.Linq/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IB4/qlV4T1WhZvM11RVoFUSZXPow9VWVeQ1uDkSKgz6bAO+gCf65H/vjrYlwyXmojSSxvfHndF9qdH43P/IuAw==", "path": "system.reactive.linq/5.0.0", "hashPath": "system.reactive.linq.5.0.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Context/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k76ubeIBOeIVg7vkQ4I+LoB8sY1EzFIc3oHEtoiNLhXleb7TBLXUQu0CFZ4sPlXJzWNabRf+gn1T7lyhOBxIMA==", "path": "system.reflection.context/8.0.0", "hashPath": "system.reflection.context.8.0.0.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-C1sMLwIG6ILQ2bmOT4gh62V6oJlyF4BlHcVMrOoor49p0Ji2tA8QAoqyMcIhAdH6OHKJ8m7BU+r4LK2CUEOKqw==", "path": "system.reflection.dispatchproxy/4.7.1", "hashPath": "system.reflection.dispatchproxy.4.7.1.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ptvgrFh7PvWI8bcVqG5rsA/weWM09EnthFHR5SCnS6IN+P4mj6rE1lBDC4U8HL9/57htKAqy4KQ3bBj84cfYyQ==", "path": "system.reflection.metadata/8.0.0", "hashPath": "system.reflection.metadata.8.0.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VybpaOQQhqE6siHppMktjfGBw1GCwvCqiufqmP8F1nj7fTUNtW35LOEt3UZTEsECfo+ELAl/9o9nJx3U91i7vA==", "path": "system.reflection.typeextensions/4.7.0", "hashPath": "system.reflection.typeextensions.4.7.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.Caching/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-tdl7Q47P09UpRu0C/OQsGJU6GacBzzk4vfp5My9rodD+BchrxmajORnTthH8RxPUTPrIoVDJmLyvJcGxB267nQ==", "path": "system.runtime.caching/8.0.1", "hashPath": "system.runtime.caching.8.0.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-IQ4NXP/B3Ayzvw0rDQzVTYsCKyy0Jp9KI6aYcK7UnGVlR9+Awz++TIPCQtPYfLJfOpm8ajowMR09V7quD3sEHw==", "path": "system.security.accesscontrol/6.0.1", "hashPath": "system.security.accesscontrol.6.0.1.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "path": "system.security.cryptography.cng/4.3.0", "hashPath": "system.security.cryptography.cng.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "path": "system.security.cryptography.pkcs/8.0.1", "hashPath": "system.security.cryptography.pkcs.8.0.1.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "path": "system.security.cryptography.protecteddata/8.0.0", "hashPath": "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-aDM/wm0ZGEZ6ZYJLzgqjp2FZdHbDHh6/OmpGfb7AdZ105zYmPn/83JRU2xLIbwgoNz9U1SLUTJN0v5th3qmvjA==", "path": "system.security.cryptography.xml/8.0.2", "hashPath": "system.security.cryptography.xml.8.0.2.nupkg.sha512"}, "System.Security.Permissions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-v/BBylw7XevuAsHXoX9dDUUfmBIcUf7Lkz8K3ZXIKz3YRKpw8YftpSir4n4e/jDTKFoaK37AsC3xnk+GNFI1Ow==", "path": "system.security.permissions/8.0.0", "hashPath": "system.security.permissions.8.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.ServiceModel.Duplex/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-IZ8ZahvTenWML7/jGUXSCm6jHlxpMbcb+Hy+h5p1WP9YVtb+Er7FHRRGizqQMINEdK6HhWpD6rzr5PdxNyusdg==", "path": "system.servicemodel.duplex/4.10.3", "hashPath": "system.servicemodel.duplex.4.10.3.nupkg.sha512"}, "System.ServiceModel.Http/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-hodkn0rPTYmoZ9EIPwcleUrOi1gZBPvU0uFvzmJbyxl1lIpVM5GxTrs/pCETStjOXCiXhBDoZQYajquOEfeW/w==", "path": "system.servicemodel.http/4.10.3", "hashPath": "system.servicemodel.http.4.10.3.nupkg.sha512"}, "System.ServiceModel.NetTcp/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-tP7GN7ehqSIQEz7yOJEtY8ziTpfavf2IQMPKa7r9KGQ75+uEW6/wSlWez7oKQwGYuAHbcGhpJvdG6WoVMKYgkw==", "path": "system.servicemodel.nettcp/4.10.3", "hashPath": "system.servicemodel.nettcp.4.10.3.nupkg.sha512"}, "System.ServiceModel.Primitives/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-aNcdry95wIP1J+/HcLQM/f/AA73LnBQDNc2uCoZ+c1//KpVRp8nMZv5ApMwK+eDNVdCK8G0NLInF+xG3mfQL+g==", "path": "system.servicemodel.primitives/4.10.3", "hashPath": "system.servicemodel.primitives.4.10.3.nupkg.sha512"}, "System.ServiceModel.Security/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-vqelKb7DvP2inb6LDJ5Igi8wpOYdtLXn5luDW5qEaqkV2sYO1pKlVYBpr6g6m5SevzbdZlVNu67dQiD/H6EdGQ==", "path": "system.servicemodel.security/4.10.3", "hashPath": "system.servicemodel.security.4.10.3.nupkg.sha512"}, "System.ServiceModel.Syndication/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CJxIUwpBkMCPmIx46tFVOt0zpRrYurUHLW6tJBcmyj+MyWpKc6MMcS69B7IdlV/bgtgys073wMIHZX9QOQ1OFA==", "path": "system.servicemodel.syndication/8.0.0", "hashPath": "system.servicemodel.syndication.8.0.0.nupkg.sha512"}, "System.ServiceProcess.ServiceController/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-02I0BXo1kmMBgw03E8Hu4K6nTqur4wpQdcDZrndczPzY2fEoGvlinE35AWbyzLZ2h2IksEZ6an4tVt3hi9j1oA==", "path": "system.serviceprocess.servicecontroller/8.0.1", "hashPath": "system.serviceprocess.servicecontroller.8.0.1.nupkg.sha512"}, "System.Speech/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CNuiA6vb95Oe5PRjClZEBiaju31vwB8OIeCgeSBXyZL6+MS4RVVB2X/C11z0xCkooHE3Vy91nM2z76emIzR+sg==", "path": "system.speech/8.0.0", "hashPath": "system.speech.8.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OZIsVplFGaVY90G2SbpgU7EnCoOO5pw1t4ic21dBF3/1omrJFpAGoNAVpPyMVOC90/hvgkGG3VFqR13YgZMQfg==", "path": "system.text.encoding.codepages/8.0.0", "hashPath": "system.text.encoding.codepages.8.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "path": "system.text.regularexpressions/4.3.1", "hashPath": "system.text.regularexpressions.4.3.1.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.AccessControl/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cIed5+HuYz+eV9yu9TH95zPkqmm1J9Qps9wxjB335sU8tsqc2kGdlTEH9FZzZeCS8a7mNSEsN8ZkyhQp1gfdEw==", "path": "system.threading.accesscontrol/8.0.0", "hashPath": "system.threading.accesscontrol.8.0.0.nupkg.sha512"}, "System.Threading.RateLimiting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7mu9v0QDv66ar3DpGSZHg9NuNcxDaaAcnMULuZlaTpP9+hwXhrxNGsF5GmLkSHxFdb5bBc1TzeujsRgTrPWi+Q==", "path": "system.threading.ratelimiting/8.0.0", "hashPath": "system.threading.ratelimiting.8.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.Web.Services.Description/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-ORCkTkUo9f1o4ACG+H6SV+0XSxVZ461w3cHzYxEU41y6aKWp1CeNTMYbtdxMw1we6c6t4Hqq15PdcLVcdqno/g==", "path": "system.web.services.description/4.10.3", "hashPath": "system.web.services.description.4.10.3.nupkg.sha512"}, "System.Windows.Extensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Obg3a90MkOw9mYKxrardLpY2u0axDMrSmy4JCdq2cYbelM2cUwmUir5Bomvd1yxmPL9h5LVHU1tuKBZpUjfASg==", "path": "system.windows.extensions/8.0.0", "hashPath": "system.windows.extensions.8.0.0.nupkg.sha512"}, "Tea/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-vwpYRSIcbNeRDrrRp6eG1rB2rAE5vOZyrQAjMPCxDTisptsIq4ApdmFc9jvthCIgOeBwTyOe4Y/TYTD88Zq+Yw==", "path": "tea/1.1.3", "hashPath": "tea.1.1.3.nupkg.sha512"}, "Tencent.QCloud.Cos.Sdk/5.4.34": {"type": "package", "serviceable": true, "sha512": "sha512-2bBeaa6fGHUI0zKRORNFK/s3SrEdQxEkqCEui6w/DCYnjaF+/XF09BGdxcIOn0h2EHHNjXPf10Iwh28gS0O93w==", "path": "tencent.qcloud.cos.sdk/5.4.34", "hashPath": "tencent.qcloud.cos.sdk.5.4.34.nupkg.sha512"}, "TencentCloudSDK.Common/3.0.1273": {"type": "package", "serviceable": true, "sha512": "sha512-1uX3OCKPa8onJOw2qgH663UsKIWuUZ37yAINnTXr2Gw7+fYkKzAUySksXmumitd2dVAlvH8XUffKzX4gFhqnlQ==", "path": "tencentcloudsdk.common/3.0.1273", "hashPath": "tencentcloudsdk.common.3.0.1273.nupkg.sha512"}, "TencentCloudSDK.Sms/3.0.1273": {"type": "package", "serviceable": true, "sha512": "sha512-FcLRn6Hf65MoopTcrg195HQraBzX/yaLtKpAQy7aAhywMSleBJ8KSGfeimrE/dYD15VPKhwGPjYqaqSwGOejgg==", "path": "tencentcloudsdk.sms/3.0.1273", "hashPath": "tencentcloudsdk.sms.3.0.1273.nupkg.sha512"}, "UAParser/3.1.47": {"type": "package", "serviceable": true, "sha512": "sha512-I68Jl/Vs5RQZdz9BbmYtnXgujg0jVd61LhKbyNZOCm9lBxZFGxLbiQo6yFj21VYi7DzPvEvrVOmeC6v41AoLfw==", "path": "uaparser/3.1.47", "hashPath": "uaparser.3.1.47.nupkg.sha512"}, "Yitter.IdGenerator/1.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-F4nOJ7Geq41vgNWX9E6/vkxRzFInACGpDp4Kad2mA2WIKhEwgPyE9FpulBAuEmDByrfHHz6mOII3IIeLJAh91g==", "path": "yitter.idgenerator/1.0.14", "hashPath": "yitter.idgenerator.1.0.14.nupkg.sha512"}, "ZstdSharp.Port/0.7.3": {"type": "package", "serviceable": true, "sha512": "sha512-U9Ix4l4cl58Kzz1rJzj5hoVTjmbx1qGMwzAcbv1j/d3NzrFaESIurQyg+ow4mivCgkE3S413y+U9k4WdnEIkRA==", "path": "zstdsharp.port/0.7.3", "hashPath": "zstdsharp.port.0.7.3.nupkg.sha512"}, "Microsoft.AspNetCore.Antiforgery/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.BearerToken/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Authorization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Endpoints/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Forms/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Server/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Connections.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.CookiePolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.Internal.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HostFiltering/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Html.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Features.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Results/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpLogging/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpOverrides/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Identity/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Metadata/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.OutputCaching/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.RateLimiting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor.Runtime.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.RequestDecompression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCompression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Rewrite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IIS/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Session/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Common.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticFiles.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebUtilities/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.CSharp.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.CommandLine/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.EnvironmentVariables/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.FileExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Ini/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.UserSecrets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Features.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Composite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Embedded/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Physical/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileSystemGlobbing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Stores/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Console/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Debug/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventLog/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.ObjectPool.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.WebEncoders.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.JSInterop/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Net.Http.Headers.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic.Core/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Registry.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "mscorlib/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "netstandard/2.1.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.AppContext/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Buffers.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Concurrent.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Immutable.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.NonGeneric/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Specialized/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Annotations.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.EventBasedAsync/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.TypeConverter/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Console/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.Common.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.DataSetExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Contracts/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Debug.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.EventLog.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.FileVersionInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Process/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.StackTrace/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TextWriterTraceListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tools/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tracing.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Dynamic.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Asn1.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Tar/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Calendars.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Brotli/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.ZipFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.AccessControl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.DriveInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Watcher/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.IsolatedStorage/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.MemoryMappedFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipelines.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes.AccessControl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.UnmanagedMemoryStream/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Expressions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Queryable/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Memory.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.HttpListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Mail/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NameResolution/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NetworkInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Ping/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Quic/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Requests/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.ServicePoint/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebClient/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebHeaderCollection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets.Client/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics.Vectors.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ObjectModel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.DispatchProxy.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.ILGeneration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Lightweight.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Metadata.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.TypeExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Reader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.ResourceManager.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Writer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.Unsafe.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.VisualC/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Handles.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.JavaScript/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.RuntimeInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Intrinsics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Loader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Numerics.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Formatters/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.AccessControl.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Claims/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Algorithms.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Cng.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Csp.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Encoding.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.OpenSsl.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.X509Certificates.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Xml.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal.Windows.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.SecureString/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceProcess/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.CodePages.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encodings.Web.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Json.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.RegularExpressions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Channels/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Overlapped/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.RateLimiting.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Dataflow/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Thread.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.ThreadPool/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Timer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions.Local/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ValueTuple/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web.HttpUtility/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Linq/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.ReaderWriter/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlSerializer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "WindowsBase/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}}}