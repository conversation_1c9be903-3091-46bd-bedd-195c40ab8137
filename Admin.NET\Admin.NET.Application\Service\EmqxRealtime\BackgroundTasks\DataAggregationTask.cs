using Admin.NET.Application.Service.EmqxRealtime.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Admin.NET.Application.Service.EmqxRealtime.BackgroundTasks;

/// <summary>
/// 数据聚合后台任务
/// </summary>
public class DataAggregationTask : BackgroundService
{
    private readonly ILogger<DataAggregationTask> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly Configuration.RedisOptions _redisOptions;
    private readonly Timer _aggregationTimer;
    private readonly Timer _cleanupTimer;
    private bool _isAggregating = false;
    private bool _isCleaning = false;

    public DataAggregationTask(
        ILogger<DataAggregationTask> logger,
        IServiceProvider serviceProvider,
        IOptions<Configuration.RedisOptions> redisOptions)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _redisOptions = redisOptions.Value;

        // 创建定时器，但不立即启动
        _aggregationTimer = new Timer(OnAggregationTimer, null, Timeout.Infinite, Timeout.Infinite);
        _cleanupTimer = new Timer(OnCleanupTimer, null, Timeout.Infinite, Timeout.Infinite);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("数据聚合后台任务启动");

        try
        {
            // 启动聚合定时器 - 每30秒执行一次
            _aggregationTimer.Change(TimeSpan.Zero, TimeSpan.FromSeconds(30));

            // 启动清理定时器 - 每小时执行一次
            _cleanupTimer.Change(TimeSpan.FromMinutes(5), TimeSpan.FromHours(1));

            // 启动自动聚合服务
            using var scope = _serviceProvider.CreateScope();
            var aggregationService = scope.ServiceProvider.GetRequiredService<IDataAggregationService>();
            await aggregationService.StartAutoAggregationAsync();

            // 保持服务运行
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(10000, stoppingToken);
                
                // 定期检查队列大小
                await CheckQueueSizeAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据聚合后台任务执行失败");
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("数据聚合后台任务停止");

        try
        {
            // 停止定时器
            await _aggregationTimer.DisposeAsync();
            await _cleanupTimer.DisposeAsync();

            // 停止自动聚合服务
            using var scope = _serviceProvider.CreateScope();
            var aggregationService = scope.ServiceProvider.GetRequiredService<IDataAggregationService>();
            await aggregationService.StopAutoAggregationAsync();

            // 执行最后一次聚合，确保数据不丢失
            if (!_isAggregating)
            {
                await PerformAggregationAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止数据聚合后台任务失败");
        }

        await base.StopAsync(cancellationToken);
    }

    private async void OnAggregationTimer(object? state)
    {
        if (_isAggregating)
        {
            _logger.LogDebug("数据聚合正在进行中，跳过本次执行");
            return;
        }

        await PerformAggregationAsync();
    }

    private async void OnCleanupTimer(object? state)
    {
        if (_isCleaning)
        {
            _logger.LogDebug("数据清理正在进行中，跳过本次执行");
            return;
        }

        await PerformCleanupAsync();
    }

    private async Task PerformAggregationAsync()
    {
        if (_isAggregating)
            return;

        _isAggregating = true;

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var aggregationService = scope.ServiceProvider.GetRequiredService<IDataAggregationService>();

            // 获取队列大小
            var queueSize = await aggregationService.GetQueueSizeAsync();
            
            if (queueSize > 0)
            {
                _logger.LogInformation("开始数据聚合，队列大小: {QueueSize}", queueSize);
                
                var startTime = DateTime.Now;
                await aggregationService.TriggerAggregationAsync();
                var duration = DateTime.Now - startTime;
                
                _logger.LogInformation("数据聚合完成，耗时: {Duration}ms", duration.TotalMilliseconds);
            }
            else
            {
                _logger.LogDebug("队列为空，跳过数据聚合");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行数据聚合失败");
        }
        finally
        {
            _isAggregating = false;
        }
    }

    private async Task PerformCleanupAsync()
    {
        if (_isCleaning)
            return;

        _isCleaning = true;

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var aggregationService = scope.ServiceProvider.GetRequiredService<IDataAggregationService>();
            var cacheService = scope.ServiceProvider.GetRequiredService<IRedisCacheService>();

            _logger.LogInformation("开始清理过期数据");
            
            var startTime = DateTime.Now;
            
            // 清理过期的历史消息（保留30天）
            var deletedMessages = await aggregationService.CleanupExpiredMessagesAsync(30);
            
            // 清理过期的缓存数据
            await CleanupExpiredCacheAsync(cacheService);
            
            var duration = DateTime.Now - startTime;
            
            _logger.LogInformation("数据清理完成，删除消息: {DeletedMessages} 条，耗时: {Duration}ms", 
                deletedMessages, duration.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行数据清理失败");
        }
        finally
        {
            _isCleaning = false;
        }
    }

    private async Task CleanupExpiredCacheAsync(IRedisCacheService cacheService)
    {
        try
        {
            // 这里可以实现具体的缓存清理逻辑
            // 例如清理过期的订阅信息、统计信息等
            
            _logger.LogDebug("缓存清理完成");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理缓存失败");
        }
    }

    private async Task CheckQueueSizeAsync()
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var aggregationService = scope.ServiceProvider.GetRequiredService<IDataAggregationService>();

            var queueSize = await aggregationService.GetQueueSizeAsync();
            var maxQueueSize = _redisOptions.MaxCachedMessages;

            // 如果队列大小超过阈值，触发紧急聚合
            if (queueSize > maxQueueSize * 0.8) // 80%阈值
            {
                _logger.LogWarning("队列大小接近上限: {QueueSize}/{MaxSize}，触发紧急聚合", queueSize, maxQueueSize);
                
                if (!_isAggregating)
                {
                    _ = Task.Run(async () => await PerformAggregationAsync());
                }
            }
            else if (queueSize > maxQueueSize * 0.5) // 50%阈值
            {
                _logger.LogInformation("队列大小: {QueueSize}/{MaxSize}", queueSize, maxQueueSize);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查队列大小失败");
        }
    }

    /// <summary>
    /// 获取任务状态
    /// </summary>
    /// <returns></returns>
    public async Task<object> GetTaskStatusAsync()
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var aggregationService = scope.ServiceProvider.GetRequiredService<IDataAggregationService>();

            var queueSize = await aggregationService.GetQueueSizeAsync();
            var statistics = await aggregationService.GetAggregationStatisticsAsync();
            var isAutoAggregationRunning = await aggregationService.IsAutoAggregationRunningAsync();

            return new
            {
                IsRunning = true,
                IsAggregating = _isAggregating,
                IsCleaning = _isCleaning,
                QueueSize = queueSize,
                IsAutoAggregationRunning = isAutoAggregationRunning,
                Statistics = statistics,
                LastAggregationTime = DateTime.Now, // 这里可以从统计信息中获取
                LastCleanupTime = DateTime.Now // 这里可以从统计信息中获取
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取任务状态失败");
            return new
            {
                IsRunning = false,
                Error = ex.Message
            };
        }
    }

    /// <summary>
    /// 手动触发聚合
    /// </summary>
    /// <returns></returns>
    public async Task<bool> TriggerManualAggregationAsync()
    {
        try
        {
            if (_isAggregating)
            {
                _logger.LogWarning("数据聚合正在进行中，无法手动触发");
                return false;
            }

            _logger.LogInformation("手动触发数据聚合");
            _ = Task.Run(async () => await PerformAggregationAsync());
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "手动触发聚合失败");
            return false;
        }
    }

    /// <summary>
    /// 手动触发清理
    /// </summary>
    /// <returns></returns>
    public async Task<bool> TriggerManualCleanupAsync()
    {
        try
        {
            if (_isCleaning)
            {
                _logger.LogWarning("数据清理正在进行中，无法手动触发");
                return false;
            }

            _logger.LogInformation("手动触发数据清理");
            _ = Task.Run(async () => await PerformCleanupAsync());
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "手动触发清理失败");
            return false;
        }
    }
}