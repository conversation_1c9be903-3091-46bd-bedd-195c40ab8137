using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application.Service.EmqxRealtime.DTOs;

/// <summary>
/// 设备分组DTO
/// </summary>
public class DeviceGroupDto
{
    /// <summary>
    /// 分组ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 分组名称
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 分组描述
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// 设备ID模式（用于自动分组）
    /// </summary>
    [StringLength(200)]
    public string? DeviceIdPattern { get; set; }

    /// <summary>
    /// 主题前缀
    /// </summary>
    [StringLength(200)]
    public string? TopicPrefix { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 设备数量
    /// </summary>
    public int DeviceCount { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }
}