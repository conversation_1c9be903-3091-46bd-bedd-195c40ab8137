{"Version": 1, "WorkspaceRootPath": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}|Admin.NET.Application\\Admin.NET.Application.csproj|d:\\project\\欧信mqtt中台\\mqtt-middle-platform\\admin.net\\admin.net.application\\configuration\\database.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}|Admin.NET.Application\\Admin.NET.Application.csproj|solutionrelative:admin.net.application\\configuration\\database.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}|Admin.NET.Application\\Admin.NET.Application.csproj|d:\\project\\欧信mqtt中台\\mqtt-middle-platform\\admin.net\\admin.net.application\\entity\\mqttclientauth.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}|Admin.NET.Application\\Admin.NET.Application.csproj|solutionrelative:admin.net.application\\entity\\mqttclientauth.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}|Admin.NET.Application\\Admin.NET.Application.csproj|d:\\project\\欧信mqtt中台\\mqtt-middle-platform\\admin.net\\admin.net.application\\service\\mqttsubscription\\mqttsubscriptionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}|Admin.NET.Application\\Admin.NET.Application.csproj|solutionrelative:admin.net.application\\service\\mqttsubscription\\mqttsubscriptionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}|Admin.NET.Application\\Admin.NET.Application.csproj|d:\\project\\欧信mqtt中台\\mqtt-middle-platform\\admin.net\\admin.net.application\\service\\mqttdevicegroup\\mqttdevicegroupservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}|Admin.NET.Application\\Admin.NET.Application.csproj|solutionrelative:admin.net.application\\service\\mqttdevicegroup\\mqttdevicegroupservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}|Admin.NET.Application\\Admin.NET.Application.csproj|d:\\project\\欧信mqtt中台\\mqtt-middle-platform\\admin.net\\admin.net.application\\service\\mqttclientauth\\mqttclientauthservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}|Admin.NET.Application\\Admin.NET.Application.csproj|solutionrelative:admin.net.application\\service\\mqttclientauth\\mqttclientauthservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}|Admin.NET.Application\\Admin.NET.Application.csproj|d:\\project\\欧信mqtt中台\\mqtt-middle-platform\\admin.net\\admin.net.application\\entity\\mqttdevicegroup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}|Admin.NET.Application\\Admin.NET.Application.csproj|solutionrelative:admin.net.application\\entity\\mqttdevicegroup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 2496, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Database.json", "DocumentMoniker": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Application\\Configuration\\Database.json", "RelativeDocumentMoniker": "Admin.NET.Application\\Configuration\\Database.json", "ToolTip": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Application\\Configuration\\Database.json", "RelativeToolTip": "Admin.NET.Application\\Configuration\\Database.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAACEAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-04T14:43:09.947Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "MqttClientAuth.cs", "DocumentMoniker": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Application\\Entity\\MqttClientAuth.cs", "RelativeDocumentMoniker": "Admin.NET.Application\\Entity\\MqttClientAuth.cs", "ToolTip": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Application\\Entity\\MqttClientAuth.cs", "RelativeToolTip": "Admin.NET.Application\\Entity\\MqttClientAuth.cs", "ViewState": "AgIAAPMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T14:26:54.548Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "MqttSubscriptionService.cs", "DocumentMoniker": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Application\\Service\\MqttSubscription\\MqttSubscriptionService.cs", "RelativeDocumentMoniker": "Admin.NET.Application\\Service\\MqttSubscription\\MqttSubscriptionService.cs", "ToolTip": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Application\\Service\\MqttSubscription\\MqttSubscriptionService.cs", "RelativeToolTip": "Admin.NET.Application\\Service\\MqttSubscription\\MqttSubscriptionService.cs", "ViewState": "AgIAAJoCAAAAAAAAAIA0wLICAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T14:01:27.583Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "MqttDeviceGroupService.cs", "DocumentMoniker": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Application\\Service\\MqttDeviceGroup\\MqttDeviceGroupService.cs", "RelativeDocumentMoniker": "Admin.NET.Application\\Service\\MqttDeviceGroup\\MqttDeviceGroupService.cs", "ToolTip": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Application\\Service\\MqttDeviceGroup\\MqttDeviceGroupService.cs", "RelativeToolTip": "Admin.NET.Application\\Service\\MqttDeviceGroup\\MqttDeviceGroupService.cs", "ViewState": "AgIAAJsAAAAAAAAAAAAEwLUAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T09:34:22.056Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "MqttClientAuthService.cs", "DocumentMoniker": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Application\\Service\\MqttClientAuth\\MqttClientAuthService.cs", "RelativeDocumentMoniker": "Admin.NET.Application\\Service\\MqttClientAuth\\MqttClientAuthService.cs", "ToolTip": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Application\\Service\\MqttClientAuth\\MqttClientAuthService.cs", "RelativeToolTip": "Admin.NET.Application\\Service\\MqttClientAuth\\MqttClientAuthService.cs", "ViewState": "AgIAACEAAAAAAAAAAAAEwDsAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T09:34:19.32Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "MqttDeviceGroup.cs", "DocumentMoniker": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Application\\Entity\\MqttDeviceGroup.cs", "RelativeDocumentMoniker": "Admin.NET.Application\\Entity\\MqttDeviceGroup.cs", "ToolTip": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Application\\Entity\\MqttDeviceGroup.cs", "RelativeToolTip": "Admin.NET.Application\\Entity\\MqttDeviceGroup.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAAA8AAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T08:29:37.189Z"}]}]}]}