{"version": 2, "dgSpecHash": "9fIagMi140g=", "success": true, "projectFilePath": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Plugins\\Admin.NET.Plugin.WorkWeixin\\Admin.NET.Plugin.WorkWeixin.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.endpointutil\\0.1.1\\alibabacloud.endpointutil.0.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.gatewayspi\\0.0.3\\alibabacloud.gatewayspi.0.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.openapiclient\\0.1.14\\alibabacloud.openapiclient.0.1.14.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.openapiutil\\1.1.2\\alibabacloud.openapiutil.1.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.sdk.dysmsapi20170525\\4.0.0\\alibabacloud.sdk.dysmsapi20170525.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.teautil\\0.1.19\\alibabacloud.teautil.0.1.19.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.teaxml\\0.0.5\\alibabacloud.teaxml.0.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\alipaysdknet.standard\\4.9.627\\alipaysdknet.standard.4.9.627.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aliyun.credentials\\1.5.0\\aliyun.credentials.1.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aliyun.oss.sdk.netcore\\2.13.0\\aliyun.oss.sdk.netcore.2.13.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\anglesharp\\1.3.0\\anglesharp.1.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspectcore.extensions.reflection\\2.4.0\\aspectcore.extensions.reflection.2.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnet.security.oauth.gitee\\8.3.0\\aspnet.security.oauth.gitee.8.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnet.security.oauth.gitee\\9.4.0\\aspnet.security.oauth.gitee.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnet.security.oauth.weixin\\8.3.0\\aspnet.security.oauth.weixin.8.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnet.security.oauth.weixin\\9.4.0\\aspnet.security.oauth.weixin.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnetcoreratelimit\\5.0.0\\aspnetcoreratelimit.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.core\\1.38.0\\azure.core.1.38.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.identity\\1.11.4\\azure.identity.1.11.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bcesdkdotnetcore\\1.0.2.911\\bcesdkdotnetcore.1.0.2.911.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\ben.demystifier\\0.4.1\\ben.demystifier.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bouncycastle.cryptography\\2.6.1\\bouncycastle.cryptography.2.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.highperformance\\8.1.0\\communitytoolkit.highperformance.8.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dnsclient\\1.6.1\\dnsclient.1.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\documentformat.openxml\\3.1.1\\documentformat.openxml.3.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\documentformat.openxml.framework\\3.1.1\\documentformat.openxml.framework.3.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dynamicexpresso.core\\2.3.3\\dynamicexpresso.core.2.3.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elastic.clients.elasticsearch\\9.0.7\\elastic.clients.elasticsearch.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elastic.transport\\0.9.2\\elastic.transport.0.9.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\flurl\\4.0.0\\flurl.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\flurl.http\\4.0.2\\flurl.http.4.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\furion.extras.authentication.jwtbearer\\4.9.7.105\\furion.extras.authentication.jwtbearer.4.9.7.105.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\furion.extras.objectmapper.mapster\\4.9.7.105\\furion.extras.objectmapper.mapster.4.9.7.105.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\furion.pure\\4.9.7.105\\furion.pure.4.9.7.105.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\furion.pure.extras.dependencymodel.codeanalysis\\4.9.7.105\\furion.pure.extras.dependencymodel.codeanalysis.4.9.7.105.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\hardware.info\\*********\\hardware.info.*********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\hashids.net\\1.7.0\\hashids.net.1.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\haukcode.wkhtmltopdfdotnet\\1.5.95\\haukcode.wkhtmltopdfdotnet.1.5.95.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\htmltoopenxml.dll\\3.1.0\\htmltoopenxml.dll.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\humanizer.core\\2.14.1\\humanizer.core.2.14.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\ip2region.ex\\1.2.0\\ip2region.ex.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iptools.china\\1.6.0\\iptools.china.1.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iptools.core\\1.6.0\\iptools.core.1.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iptools.international\\1.6.0\\iptools.international.1.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\json.more.net\\2.0.2\\json.more.net.2.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jsonpointer.net\\5.0.2\\jsonpointer.net.5.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jsonschema.net\\7.0.4\\jsonschema.net.7.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jsonschema.net\\7.2.3\\jsonschema.net.7.2.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\lazy.captcha.core\\2.1.0\\lazy.captcha.core.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\lazy.captcha.core\\2.2.0\\lazy.captcha.core.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\log4net\\3.1.0\\log4net.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\magicodes.ie.core\\2.7.6\\magicodes.ie.core.2.7.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\magicodes.ie.epplus\\2.7.6\\magicodes.ie.epplus.2.7.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\magicodes.ie.excel\\2.7.6\\magicodes.ie.excel.2.7.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\magicodes.ie.html\\2.7.6\\magicodes.ie.html.2.7.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\magicodes.ie.pdf\\2.7.6\\magicodes.ie.pdf.2.7.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\magicodes.ie.word\\2.7.6\\magicodes.ie.word.2.7.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\magicodes.razorengine.netcore\\2.2.0\\magicodes.razorengine.netcore.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mailkit\\4.13.0\\mailkit.4.13.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mapster\\7.4.0\\mapster.7.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mapster.core\\1.2.1\\mapster.core.1.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mapster.dependencyinjection\\1.0.1\\mapster.dependencyinjection.1.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\markdig.signed\\0.33.0\\markdig.signed.0.33.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\markdig.signed\\0.38.0\\markdig.signed.0.38.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\maxmind.db\\2.4.0\\maxmind.db.2.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\maxmind.geoip2\\3.0.0\\maxmind.geoip2.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\messagepack\\2.5.108\\messagepack.2.5.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\messagepack\\2.5.187\\messagepack.2.5.187.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\messagepack.annotations\\2.5.108\\messagepack.annotations.2.5.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\messagepack.annotations\\2.5.187\\messagepack.annotations.2.5.187.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.applicationinsights\\2.21.0\\microsoft.applicationinsights.2.21.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.applicationinsights\\2.22.0\\microsoft.applicationinsights.2.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.jwtbearer\\8.0.18\\microsoft.aspnetcore.authentication.jwtbearer.8.0.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.jwtbearer\\9.0.0\\microsoft.aspnetcore.authentication.jwtbearer.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.connections.abstractions\\8.0.11\\microsoft.aspnetcore.connections.abstractions.8.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.connections.abstractions\\9.0.7\\microsoft.aspnetcore.connections.abstractions.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.internal\\8.0.11\\microsoft.aspnetcore.cryptography.internal.8.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.internal\\9.0.7\\microsoft.aspnetcore.cryptography.internal.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection\\8.0.11\\microsoft.aspnetcore.dataprotection.8.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection\\9.0.7\\microsoft.aspnetcore.dataprotection.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection.abstractions\\8.0.11\\microsoft.aspnetcore.dataprotection.abstractions.8.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection.abstractions\\9.0.7\\microsoft.aspnetcore.dataprotection.abstractions.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection.stackexchangeredis\\8.0.11\\microsoft.aspnetcore.dataprotection.stackexchangeredis.8.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection.stackexchangeredis\\9.0.7\\microsoft.aspnetcore.dataprotection.stackexchangeredis.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.server.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.html.abstractions\\2.2.0\\microsoft.aspnetcore.html.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.abstractions\\2.2.0\\microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.extensions\\2.2.0\\microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.features\\2.2.0\\microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.jsonpatch\\8.0.18\\microsoft.aspnetcore.jsonpatch.8.0.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.jsonpatch\\9.0.0\\microsoft.aspnetcore.jsonpatch.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.newtonsoftjson\\8.0.18\\microsoft.aspnetcore.mvc.newtonsoftjson.8.0.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.newtonsoftjson\\9.0.0\\microsoft.aspnetcore.mvc.newtonsoftjson.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.razor\\2.2.0\\microsoft.aspnetcore.razor.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.razor.language\\6.0.36\\microsoft.aspnetcore.razor.language.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.razor.runtime\\2.2.0\\microsoft.aspnetcore.razor.runtime.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.signalr.common\\8.0.11\\microsoft.aspnetcore.signalr.common.8.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.signalr.common\\9.0.7\\microsoft.aspnetcore.signalr.common.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.signalr.protocols.newtonsoftjson\\8.0.11\\microsoft.aspnetcore.signalr.protocols.newtonsoftjson.8.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.signalr.protocols.newtonsoftjson\\9.0.7\\microsoft.aspnetcore.signalr.protocols.newtonsoftjson.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.signalr.stackexchangeredis\\8.0.11\\microsoft.aspnetcore.signalr.stackexchangeredis.8.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.signalr.stackexchangeredis\\9.0.7\\microsoft.aspnetcore.signalr.stackexchangeredis.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.staticfiles\\2.2.0\\microsoft.aspnetcore.staticfiles.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\8.0.0\\microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.analyzers\\3.3.4\\microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.analyzers\\3.11.0\\microsoft.codeanalysis.analyzers.3.11.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\4.9.2\\microsoft.codeanalysis.common.4.9.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\4.11.0\\microsoft.codeanalysis.common.4.11.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\4.9.2\\microsoft.codeanalysis.csharp.4.9.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\4.11.0\\microsoft.codeanalysis.csharp.4.11.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient\\5.2.2\\microsoft.data.sqlclient.5.2.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient.sni.runtime\\5.2.0\\microsoft.data.sqlclient.sni.runtime.5.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlite\\9.0.0\\microsoft.data.sqlite.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlite.core\\9.0.0\\microsoft.data.sqlite.core.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.apidescription.server\\8.0.0\\microsoft.extensions.apidescription.server.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.apidescription.server\\9.0.0\\microsoft.extensions.apidescription.server.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\9.0.0\\microsoft.extensions.caching.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\9.0.0\\microsoft.extensions.caching.memory.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.0\\microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.7\\microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\9.0.0\\microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\8.0.0\\microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.0\\microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.7\\microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\8.0.2\\microsoft.extensions.dependencymodel.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\9.0.0\\microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\8.0.1\\microsoft.extensions.diagnostics.abstractions.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\9.0.7\\microsoft.extensions.diagnostics.abstractions.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.features\\8.0.11\\microsoft.extensions.features.8.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.features\\9.0.7\\microsoft.extensions.features.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\8.0.0\\microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\9.0.7\\microsoft.extensions.fileproviders.abstractions.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\8.0.1\\microsoft.extensions.hosting.abstractions.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\9.0.7\\microsoft.extensions.hosting.abstractions.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.0\\microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.7\\microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\8.0.17\\microsoft.extensions.objectpool.8.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.0\\microsoft.extensions.options.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.7\\microsoft.extensions.options.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\9.0.0\\microsoft.extensions.options.configurationextensions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.0\\microsoft.extensions.primitives.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.7\\microsoft.extensions.primitives.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.webencoders\\2.2.0\\microsoft.extensions.webencoders.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client\\4.61.3\\microsoft.identity.client.4.61.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client.extensions.msal\\4.61.3\\microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\7.1.2\\microsoft.identitymodel.abstractions.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\8.0.1\\microsoft.identitymodel.abstractions.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\7.1.2\\microsoft.identitymodel.jsonwebtokens.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\8.0.1\\microsoft.identitymodel.jsonwebtokens.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\7.1.2\\microsoft.identitymodel.logging.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\8.0.1\\microsoft.identitymodel.logging.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\7.1.2\\microsoft.identitymodel.protocols.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\8.0.1\\microsoft.identitymodel.protocols.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\7.1.2\\microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\8.0.1\\microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\7.1.2\\microsoft.identitymodel.tokens.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\8.0.1\\microsoft.identitymodel.tokens.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.io.recyclablememorystream\\3.0.1\\microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.management.infrastructure\\3.0.0\\microsoft.management.infrastructure.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.management.infrastructure.cimcmdlets\\7.4.11\\microsoft.management.infrastructure.cimcmdlets.7.4.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.management.infrastructure.cimcmdlets\\7.5.2\\microsoft.management.infrastructure.cimcmdlets.7.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.management.infrastructure.runtime.unix\\3.0.0\\microsoft.management.infrastructure.runtime.unix.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.management.infrastructure.runtime.win\\3.0.0\\microsoft.management.infrastructure.runtime.win.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.http.headers\\2.2.0\\microsoft.net.http.headers.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.stringtools\\17.4.0\\microsoft.net.stringtools.17.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.stringtools\\17.6.3\\microsoft.net.stringtools.17.6.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.1\\microsoft.netcore.platforms.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.3\\microsoft.netcore.targets.1.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.openapi\\1.6.23\\microsoft.openapi.1.6.23.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.powershell.commands.diagnostics\\7.4.11\\microsoft.powershell.commands.diagnostics.7.4.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.powershell.commands.diagnostics\\7.5.2\\microsoft.powershell.commands.diagnostics.7.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.powershell.commands.management\\7.4.11\\microsoft.powershell.commands.management.7.4.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.powershell.commands.management\\7.5.2\\microsoft.powershell.commands.management.7.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.powershell.commands.utility\\7.4.11\\microsoft.powershell.commands.utility.7.4.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.powershell.commands.utility\\7.5.2\\microsoft.powershell.commands.utility.7.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.powershell.consolehost\\7.4.11\\microsoft.powershell.consolehost.7.4.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.powershell.consolehost\\7.5.2\\microsoft.powershell.consolehost.7.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.powershell.coreclr.eventing\\7.4.11\\microsoft.powershell.coreclr.eventing.7.4.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.powershell.coreclr.eventing\\7.5.2\\microsoft.powershell.coreclr.eventing.7.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.powershell.markdownrender\\7.2.1\\microsoft.powershell.markdownrender.7.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.powershell.native\\7.4.0\\microsoft.powershell.native.7.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.powershell.sdk\\7.4.11\\microsoft.powershell.sdk.7.4.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.powershell.sdk\\7.5.2\\microsoft.powershell.sdk.7.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.powershell.security\\7.4.11\\microsoft.powershell.security.7.4.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.powershell.security\\7.5.2\\microsoft.powershell.security.7.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.security.extensions\\1.4.0\\microsoft.security.extensions.1.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.sqlserver.server\\1.0.0\\microsoft.sqlserver.server.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry.accesscontrol\\8.0.0\\microsoft.win32.registry.accesscontrol.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry.accesscontrol\\9.0.6\\microsoft.win32.registry.accesscontrol.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\8.0.0\\microsoft.win32.systemevents.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\9.0.6\\microsoft.win32.systemevents.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.compatibility\\8.0.17\\microsoft.windows.compatibility.8.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.compatibility\\9.0.6\\microsoft.windows.compatibility.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.wsman.management\\7.4.11\\microsoft.wsman.management.7.4.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.wsman.management\\7.5.2\\microsoft.wsman.management.7.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.wsman.runtime\\7.4.11\\microsoft.wsman.runtime.7.4.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.wsman.runtime\\7.5.2\\microsoft.wsman.runtime.7.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mimekit\\4.13.0\\mimekit.4.13.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\miniexcel\\1.41.3\\miniexcel.1.41.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\minio\\5.0.0\\minio.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\miniword\\0.9.2\\miniword.0.9.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.bson\\3.3.0\\mongodb.bson.3.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.driver\\3.3.0\\mongodb.driver.3.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mysqlconnector\\2.2.5\\mysqlconnector.2.2.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newlife.core\\11.5.2025.701\\newlife.core.11.5.2025.701.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newlife.redis\\6.3.2025.701\\newlife.redis.6.3.2025.701.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json.bson\\1.0.2\\newtonsoft.json.bson.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\novell.directory.ldap.netstandard\\4.0.0\\novell.directory.ldap.netstandard.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npgsql\\5.0.18\\npgsql.5.0.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\oncemi.aspnetcore.oss\\1.2.0\\oncemi.aspnetcore.oss.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\oracle.manageddataaccess.core\\23.8.0\\oracle.manageddataaccess.core.23.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\oscar.data.sqlclient\\4.0.4\\oscar.data.sqlclient.4.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\pipelines.sockets.unofficial\\2.2.8\\pipelines.sockets.unofficial.2.2.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\portable.bouncycastle\\*******\\portable.bouncycastle.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\qiniu\\8.3.1\\qiniu.8.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\qrcoder\\1.6.0\\qrcoder.1.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\rabbitmq.client\\7.1.2\\rabbitmq.client.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.android-arm.runtime.native.system.io.ports\\9.0.6\\runtime.android-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.android-arm64.runtime.native.system.io.ports\\9.0.6\\runtime.android-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.android-x64.runtime.native.system.io.ports\\9.0.6\\runtime.android-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.android-x86.runtime.native.system.io.ports\\9.0.6\\runtime.android-x86.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-arm.runtime.native.system.io.ports\\8.0.0\\runtime.linux-arm.runtime.native.system.io.ports.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-arm.runtime.native.system.io.ports\\9.0.6\\runtime.linux-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-arm64.runtime.native.system.io.ports\\8.0.0\\runtime.linux-arm64.runtime.native.system.io.ports.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-arm64.runtime.native.system.io.ports\\9.0.6\\runtime.linux-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-bionic-arm64.runtime.native.system.io.ports\\9.0.6\\runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-bionic-x64.runtime.native.system.io.ports\\9.0.6\\runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-musl-arm.runtime.native.system.io.ports\\9.0.6\\runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-musl-arm64.runtime.native.system.io.ports\\9.0.6\\runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-musl-x64.runtime.native.system.io.ports\\9.0.6\\runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-x64.runtime.native.system.io.ports\\8.0.0\\runtime.linux-x64.runtime.native.system.io.ports.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-x64.runtime.native.system.io.ports\\9.0.6\\runtime.linux-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.maccatalyst-arm64.runtime.native.system.io.ports\\9.0.6\\runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.maccatalyst-x64.runtime.native.system.io.ports\\9.0.6\\runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.io.ports\\8.0.0\\runtime.native.system.io.ports.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.io.ports\\9.0.6\\runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx-arm64.runtime.native.system.io.ports\\8.0.0\\runtime.osx-arm64.runtime.native.system.io.ports.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx-arm64.runtime.native.system.io.ports\\9.0.6\\runtime.osx-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx-x64.runtime.native.system.io.ports\\8.0.0\\runtime.osx-x64.runtime.native.system.io.ports.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx-x64.runtime.native.system.io.ports\\9.0.6\\runtime.osx-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x86.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpcompress\\0.30.1\\sharpcompress.0.30.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sixlabors.imagesharp\\3.1.8\\sixlabors.imagesharp.3.1.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sixlabors.imagesharp.web\\3.1.5\\sixlabors.imagesharp.web.3.1.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp\\3.116.1\\skiasharp.3.116.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp\\3.119.0\\skiasharp.3.119.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.linux.nodependencies\\3.116.1\\skiasharp.nativeassets.linux.nodependencies.3.116.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.linux.nodependencies\\3.119.0\\skiasharp.nativeassets.linux.nodependencies.3.119.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.macos\\3.116.1\\skiasharp.nativeassets.macos.3.116.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.macos\\3.119.0\\skiasharp.nativeassets.macos.3.119.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.win32\\3.116.1\\skiasharp.nativeassets.win32.3.116.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.win32\\3.119.0\\skiasharp.nativeassets.win32.3.119.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skit.flurlhttpclient.common\\3.1.1\\skit.flurlhttpclient.common.3.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skit.flurlhttpclient.wechat.api\\3.11.0\\skit.flurlhttpclient.wechat.api.3.11.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skit.flurlhttpclient.wechat.tenpayv3\\3.13.0\\skit.flurlhttpclient.wechat.tenpayv3.3.13.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\snappier\\1.0.0\\snappier.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.bundle_e_sqlite3\\2.1.10\\sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.core\\2.1.10\\sqlitepclraw.core.2.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.lib.e_sqlite3\\2.1.10\\sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.provider.e_sqlite3\\2.1.10\\sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlsugar.mongodbcore\\5.1.4.232\\sqlsugar.mongodbcore.5.1.4.232.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlsugarcore\\5.1.4.198\\sqlsugarcore.5.1.4.198.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlsugarcore.dm\\8.8.0\\sqlsugarcore.dm.8.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlsugarcore.kdbndp\\9.3.7.613\\sqlsugarcore.kdbndp.9.3.7.613.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\ssh.net\\2025.0.0\\ssh.net.2025.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stackexchange.redis\\2.7.27\\stackexchange.redis.2.7.27.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore\\9.0.3\\swashbuckle.aspnetcore.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swagger\\9.0.3\\swashbuckle.aspnetcore.swagger.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggergen\\9.0.3\\swashbuckle.aspnetcore.swaggergen.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggerui\\9.0.3\\swashbuckle.aspnetcore.swaggerui.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.clientmodel\\1.0.0\\system.clientmodel.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\8.0.0\\system.codedom.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\9.0.6\\system.codedom.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\8.0.0\\system.collections.immutable.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.annotations\\4.7.0\\system.componentmodel.annotations.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.composition\\8.0.0\\system.componentmodel.composition.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.composition\\9.0.6\\system.componentmodel.composition.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.composition.registration\\8.0.0\\system.componentmodel.composition.registration.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.composition.registration\\9.0.6\\system.componentmodel.composition.registration.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\8.0.1\\system.configuration.configurationmanager.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\9.0.6\\system.configuration.configurationmanager.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.common\\4.3.0\\system.data.common.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.odbc\\8.0.1\\system.data.odbc.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.odbc\\9.0.6\\system.data.odbc.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.oledb\\8.0.1\\system.data.oledb.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.oledb\\9.0.6\\system.data.oledb.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlclient\\4.9.0\\system.data.sqlclient.4.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\9.0.0\\system.diagnostics.diagnosticsource.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\9.0.6\\system.diagnostics.diagnosticsource.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\8.0.2\\system.diagnostics.eventlog.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\9.0.6\\system.diagnostics.eventlog.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.performancecounter\\8.0.1\\system.diagnostics.performancecounter.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.performancecounter\\9.0.6\\system.diagnostics.performancecounter.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices\\8.0.0\\system.directoryservices.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices\\9.0.6\\system.directoryservices.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices.accountmanagement\\8.0.1\\system.directoryservices.accountmanagement.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices.accountmanagement\\9.0.6\\system.directoryservices.accountmanagement.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices.protocols\\8.0.2\\system.directoryservices.protocols.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices.protocols\\9.0.6\\system.directoryservices.protocols.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\8.0.17\\system.drawing.common.8.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\9.0.6\\system.drawing.common.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.formats.asn1\\8.0.1\\system.formats.asn1.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.formats.asn1\\8.0.2\\system.formats.asn1.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\7.1.2\\system.identitymodel.tokens.jwt.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\8.0.1\\system.identitymodel.tokens.jwt.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.hashing\\7.0.0\\system.io.hashing.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.packaging\\8.0.1\\system.io.packaging.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.packaging\\9.0.6\\system.io.packaging.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\8.0.0\\system.io.pipelines.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.ports\\8.0.0\\system.io.ports.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.ports\\9.0.6\\system.io.ports.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.dynamic.core\\1.6.6\\system.linq.dynamic.core.1.6.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\8.0.0\\system.management.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\9.0.6\\system.management.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management.automation\\7.4.11\\system.management.automation.7.4.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management.automation\\7.5.2\\system.management.automation.7.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.6.0\\system.memory.4.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory.data\\1.0.2\\system.memory.data.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.http\\4.3.4\\system.net.http.4.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.http.winhttphandler\\8.0.3\\system.net.http.winhttphandler.8.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.http.winhttphandler\\9.0.6\\system.net.http.winhttphandler.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.private.servicemodel\\4.10.3\\system.private.servicemodel.4.10.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.private.uri\\4.3.2\\system.private.uri.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reactive\\5.0.0\\system.reactive.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reactive.linq\\5.0.0\\system.reactive.linq.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.context\\8.0.0\\system.reflection.context.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.context\\9.0.6\\system.reflection.context.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.dispatchproxy\\4.7.1\\system.reflection.dispatchproxy.4.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\8.0.0\\system.reflection.metadata.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\8.0.1\\system.reflection.metadata.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.typeextensions\\4.7.0\\system.reflection.typeextensions.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.1\\system.runtime.4.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.caching\\8.0.1\\system.runtime.caching.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.caching\\9.0.6\\system.runtime.caching.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\5.0.0\\system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\6.0.1\\system.security.accesscontrol.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\4.3.0\\system.security.cryptography.cng.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\8.0.1\\system.security.cryptography.pkcs.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\9.0.7\\system.security.cryptography.pkcs.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\8.0.0\\system.security.cryptography.protecteddata.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\9.0.6\\system.security.cryptography.protecteddata.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.x509certificates\\4.3.0\\system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\8.0.2\\system.security.cryptography.xml.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\9.0.7\\system.security.cryptography.xml.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\8.0.0\\system.security.permissions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\9.0.6\\system.security.permissions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.duplex\\4.10.3\\system.servicemodel.duplex.4.10.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.http\\4.10.3\\system.servicemodel.http.4.10.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.nettcp\\4.10.3\\system.servicemodel.nettcp.4.10.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.primitives\\4.10.3\\system.servicemodel.primitives.4.10.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.security\\4.10.3\\system.servicemodel.security.4.10.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.syndication\\8.0.0\\system.servicemodel.syndication.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.syndication\\9.0.6\\system.servicemodel.syndication.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.serviceprocess.servicecontroller\\8.0.1\\system.serviceprocess.servicecontroller.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.serviceprocess.servicecontroller\\9.0.6\\system.serviceprocess.servicecontroller.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.speech\\8.0.0\\system.speech.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.speech\\9.0.6\\system.speech.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\8.0.0\\system.text.encoding.codepages.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\9.0.6\\system.text.encoding.codepages.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\8.0.0\\system.text.encodings.web.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\9.0.6\\system.text.encodings.web.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\8.0.5\\system.text.json.8.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.regularexpressions\\4.3.1\\system.text.regularexpressions.4.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.accesscontrol\\8.0.0\\system.threading.accesscontrol.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.accesscontrol\\9.0.6\\system.threading.accesscontrol.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.ratelimiting\\8.0.0\\system.threading.ratelimiting.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.thread\\4.3.0\\system.threading.thread.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.web.services.description\\4.10.3\\system.web.services.description.4.10.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.web.services.description\\8.0.0\\system.web.services.description.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.windows.extensions\\8.0.0\\system.windows.extensions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.windows.extensions\\9.0.6\\system.windows.extensions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\tea\\1.1.3\\tea.1.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\tencent.qcloud.cos.sdk\\5.4.34\\tencent.qcloud.cos.sdk.5.4.34.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\tencentcloudsdk.common\\3.0.1273\\tencentcloudsdk.common.3.0.1273.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\tencentcloudsdk.sms\\3.0.1273\\tencentcloudsdk.sms.3.0.1273.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\uaparser\\3.1.47\\uaparser.3.1.47.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xihan.framework\\0.11.6\\xihan.framework.0.11.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xihan.framework.utils\\0.11.6\\xihan.framework.utils.0.11.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\yitter.idgenerator\\1.0.14\\yitter.idgenerator.1.0.14.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\zstdsharp.port\\0.7.3\\zstdsharp.port.0.7.3.nupkg.sha512"], "logs": []}