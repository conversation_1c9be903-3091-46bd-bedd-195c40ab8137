using Admin.NET.Application.Service.EmqxRealtime.DTOs;

namespace Admin.NET.Application.Service.EmqxRealtime.Interfaces;

/// <summary>
/// Redis缓存服务接口
/// </summary>
public interface IRedisCacheService
{
    /// <summary>
    /// 缓存消息
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="expireSeconds">过期时间（秒）</param>
    /// <returns></returns>
    Task<bool> CacheMessageAsync(MessageDto message, int? expireSeconds = null);

    /// <summary>
    /// 获取缓存的消息
    /// </summary>
    /// <param name="instanceId">实例ID</param>
    /// <param name="topics">主题列表</param>
    /// <param name="maxCount">最大数量</param>
    /// <returns></returns>
    Task<List<MessageDto>> GetCachedMessagesAsync(int instanceId, List<string> topics, int maxCount = 50);

    /// <summary>
    /// 缓存订阅信息
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="subscriptionInfo">订阅信息</param>
    /// <returns></returns>
    Task<bool> CacheSubscriptionAsync(string connectionId, SubscriptionInfo subscriptionInfo);

    /// <summary>
    /// 获取订阅信息
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns></returns>
    Task<SubscriptionInfo?> GetSubscriptionAsync(string connectionId);

    /// <summary>
    /// 移除订阅信息
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns></returns>
    Task<bool> RemoveSubscriptionAsync(string connectionId);

    /// <summary>
    /// 获取所有活跃订阅
    /// </summary>
    /// <returns></returns>
    Task<List<SubscriptionInfo>> GetAllSubscriptionsAsync();

    /// <summary>
    /// 缓存统计信息
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="value">值</param>
    /// <param name="expireSeconds">过期时间（秒）</param>
    /// <returns></returns>
    Task<bool> CacheStatisticsAsync(string key, object value, int? expireSeconds = null);

    /// <summary>
    /// 获取统计信息
    /// </summary>
    /// <param name="key">键</param>
    /// <returns></returns>
    Task<T?> GetStatisticsAsync<T>(string key);

    /// <summary>
    /// 增加计数器
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="increment">增量</param>
    /// <returns></returns>
    Task<long> IncrementCounterAsync(string key, long increment = 1);

    /// <summary>
    /// 获取计数器值
    /// </summary>
    /// <param name="key">键</param>
    /// <returns></returns>
    Task<long> GetCounterAsync(string key);

    /// <summary>
    /// 设置过期时间
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="expireSeconds">过期时间（秒）</param>
    /// <returns></returns>
    Task<bool> SetExpireAsync(string key, int expireSeconds);

    /// <summary>
    /// 删除键
    /// </summary>
    /// <param name="key">键</param>
    /// <returns></returns>
    Task<bool> DeleteAsync(string key);

    /// <summary>
    /// 批量删除键
    /// </summary>
    /// <param name="keys">键列表</param>
    /// <returns></returns>
    Task<long> DeleteAsync(List<string> keys);
}