namespace Admin.NET.Application.Service.EmqxRealtime.Configuration;

/// <summary>
/// Redis配置选项
/// </summary>
public class RedisOptions
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "RedisOptions";

    /// <summary>
    /// 连接字符串
    /// </summary>
    public string ConnectionString { get; set; } = "localhost:6379";

    /// <summary>
    /// 数据库索引
    /// </summary>
    public int Database { get; set; } = 0;

    /// <summary>
    /// 键前缀
    /// </summary>
    public string KeyPrefix { get; set; } = "mqtt:realtime:";

    /// <summary>
    /// 连接超时时间（毫秒）
    /// </summary>
    public int ConnectTimeout { get; set; } = 5000;

    /// <summary>
    /// 同步超时时间（毫秒）
    /// </summary>
    public int SyncTimeout { get; set; } = 5000;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryTimes { get; set; } = 3;

    /// <summary>
    /// 默认过期时间（秒）
    /// </summary>
    public int DefaultExpireSeconds { get; set; } = 3600;

    /// <summary>
    /// 消息缓存过期时间（秒）
    /// </summary>
    public int MessageCacheExpireSeconds { get; set; } = 1800;

    /// <summary>
    /// 最大缓存消息数量
    /// </summary>
    public int MaxCachedMessages { get; set; } = 1000;
}