using Admin.NET.Application.Service.EmqxRealtime.DTOs;
using Admin.NET.Application.Service.EmqxRealtime.Interfaces;
using Admin.NET.Application.Entity;
using Admin.NET.Application.Service.EmqxRealtime.Configuration;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Text.RegularExpressions;

namespace Admin.NET.Application.Service.EmqxRealtime.Services;

/// <summary>
/// 实时订阅服务实现
/// </summary>
public class RealtimeSubscriptionService : IRealtimeSubscriptionService
{
    private readonly ILogger<RealtimeSubscriptionService> _logger;
    private readonly IRedisCacheService _redisCacheService;
    private readonly IDataAggregationService _dataAggregationService;
    private readonly IDeviceGroupService _deviceGroupService;
    private readonly MqttOptions _mqttOptions;
    private readonly RedisOptions _redisOptions;
    
    // 内存中的连接信息缓存
    private readonly ConcurrentDictionary<string, SubscriptionInfo> _connections = new();
    
    // 统计信息
    private long _totalMessagesReceived = 0;
    private long _totalConnectionsCreated = 0;
    private readonly DateTime _serviceStartTime = DateTime.Now;

    public RealtimeSubscriptionService(
        ILogger<RealtimeSubscriptionService> logger,
        IRedisCacheService redisCacheService,
        IDataAggregationService dataAggregationService,
        IDeviceGroupService deviceGroupService,
        IOptions<MqttOptions> mqttOptions,
        IOptions<RedisOptions> redisOptions)
    {
        _logger = logger;
        _redisCacheService = redisCacheService;
        _dataAggregationService = dataAggregationService;
        _deviceGroupService = deviceGroupService;
        _mqttOptions = mqttOptions.Value;
        _redisOptions = redisOptions.Value;
    }

    public async Task<bool> SubscribeAsync(string connectionId, SubscriptionRequest request)
    {
        try
        {
            var subscriptionInfo = new SubscriptionInfo
            {
                ConnectionId = connectionId,
                InstanceId = request.InstanceId,
                Topics = request.Topics,
                SubscribeToAllTopics = request.SubscribeToAllTopics,
                DeviceGroup = request.DeviceGroup,
                SubscribeTime = DateTime.Now,
                LastActiveTime = DateTime.Now
            };

            // 更新内存缓存
            _connections.AddOrUpdate(connectionId, subscriptionInfo, (key, oldValue) => subscriptionInfo);
            
            // 更新Redis缓存
            await _redisCacheService.CacheSubscriptionAsync(connectionId, subscriptionInfo);

            _logger.LogInformation("连接 {ConnectionId} 成功订阅实例 {InstanceId}, 主题: {Topics}", 
                connectionId, request.InstanceId, string.Join(", ", request.Topics));

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "订阅失败: {ConnectionId}", connectionId);
            return false;
        }
    }

    public async Task<bool> UnsubscribeAsync(string connectionId)
    {
        try
        {
            // 从内存缓存移除
            _connections.TryRemove(connectionId, out _);
            
            // 从Redis缓存移除
            await _redisCacheService.RemoveSubscriptionAsync(connectionId);

            _logger.LogInformation("连接 {ConnectionId} 已取消订阅", connectionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消订阅失败: {ConnectionId}", connectionId);
            return false;
        }
    }

    public async Task<SubscriptionInfo?> GetSubscriptionInfoAsync(string connectionId)
    {
        try
        {
            // 先从内存缓存获取
            if (_connections.TryGetValue(connectionId, out var info))
            {
                return info;
            }

            // 从Redis缓存获取
            return await _redisCacheService.GetSubscriptionAsync(connectionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取订阅信息失败: {ConnectionId}", connectionId);
            return null;
        }
    }

    public async Task<List<MessageDto>> GetHistoryMessagesAsync(int instanceId, List<string> topics, int maxCount = 50)
    {
        try
        {
            // 先从Redis缓存获取
            var cachedMessages = await _redisCacheService.GetCachedMessagesAsync(instanceId, topics, maxCount);
            if (cachedMessages.Any())
            {
                return cachedMessages.Take(maxCount).ToList();
            }

            // 从数据库获取
            return await _dataAggregationService.GetHistoryMessagesAsync(instanceId, topics, maxCount: maxCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取历史消息失败: InstanceId={InstanceId}, Topics={Topics}", 
                instanceId, string.Join(", ", topics));
            return new List<MessageDto>();
        }
    }

    public async Task<object> GetStatisticsAsync()
    {
        try
        {
            var activeConnections = _connections.Count;
            var totalConnections = await _redisCacheService.GetCounterAsync("total_connections");
            var totalMessages = await _redisCacheService.GetCounterAsync("total_messages");
            var queueSize = await _dataAggregationService.GetQueueSizeAsync();

            return new
            {
                ActiveConnections = activeConnections,
                TotalConnections = totalConnections,
                TotalMessages = totalMessages,
                QueueSize = queueSize,
                ServiceUptime = DateTime.Now - _serviceStartTime,
                ServiceStartTime = _serviceStartTime,
                LastUpdateTime = DateTime.Now
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取统计信息失败");
            return new { Error = "获取统计信息失败" };
        }
    }

    public async Task AddConnectionAsync(string connectionId, long? userId = null, string? userName = null, string? clientIp = null, string? userAgent = null)
    {
        try
        {
            var subscriptionInfo = new SubscriptionInfo
            {
                ConnectionId = connectionId,
                UserId = userId,
                UserName = userName,
                ClientIp = clientIp,
                UserAgent = userAgent,
                SubscribeTime = DateTime.Now,
                LastActiveTime = DateTime.Now
            };

            _connections.TryAdd(connectionId, subscriptionInfo);
            await _redisCacheService.IncrementCounterAsync("total_connections");
            
            Interlocked.Increment(ref _totalConnectionsCreated);
            
            _logger.LogInformation("新连接已添加: {ConnectionId}, 用户: {UserName}", connectionId, userName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加连接失败: {ConnectionId}", connectionId);
        }
    }

    public async Task RemoveConnectionAsync(string connectionId)
    {
        try
        {
            await UnsubscribeAsync(connectionId);
            _logger.LogInformation("连接已移除: {ConnectionId}", connectionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除连接失败: {ConnectionId}", connectionId);
        }
    }

    public async Task ProcessMqttMessageAsync(MessageDto message)
    {
        try
        {
            // 增加消息计数
            Interlocked.Increment(ref _totalMessagesReceived);
            await _redisCacheService.IncrementCounterAsync("total_messages");

            // 自动匹配设备分组
            if (!string.IsNullOrEmpty(message.DeviceId))
            {
                var deviceGroup = await _deviceGroupService.MatchGroupByDeviceIdAsync(message.DeviceId);
                if (deviceGroup != null)
                {
                    message.DeviceGroup = deviceGroup.Name;
                }
            }
            else
            {
                var topicGroup = await _deviceGroupService.MatchGroupByTopicAsync(message.Topic);
                if (topicGroup != null)
                {
                    message.DeviceGroup = topicGroup.Name;
                }
            }

            // 缓存到Redis
            await _redisCacheService.CacheMessageAsync(message);

            // 添加到聚合队列
            await _dataAggregationService.AddMessageToQueueAsync(message);

            _logger.LogDebug("处理MQTT消息: Topic={Topic}, DeviceId={DeviceId}, Group={Group}", 
                message.Topic, message.DeviceId, message.DeviceGroup);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理MQTT消息失败: {Message}", message.Topic);
        }
    }

    public async Task<List<SubscriptionInfo>> GetActiveConnectionsAsync()
    {
        try
        {
            var connections = new List<SubscriptionInfo>();
            
            // 从内存获取
            connections.AddRange(_connections.Values);
            
            // 从Redis获取（防止内存丢失）
            var redisConnections = await _redisCacheService.GetAllSubscriptionsAsync();
            foreach (var redisConn in redisConnections)
            {
                if (!connections.Any(c => c.ConnectionId == redisConn.ConnectionId))
                {
                    connections.Add(redisConn);
                }
            }

            return connections;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取活跃连接失败");
            return new List<SubscriptionInfo>();
        }
    }
}