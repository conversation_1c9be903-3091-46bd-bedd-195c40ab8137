﻿{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",

  // 缓存配置
  "Cache": {
    "Prefix": "adminnet_", // 全局缓存前缀
    "CacheType": "Memory", // Memory、Redis
    "Redis": {
      "Configuration": "server=127.0.0.1:6379;password=;db=5;", // Redis连接字符串
      "Prefix": "adminnet_", // Redis前缀（目前没用）
      "MaxMessageSize": "1048576", // 最大消息大小 默认1024 * 1024
      "AutoDetect": false // 自动检测集群节点 阿里云的Redis分布式集群使用代理模式，需要设置为false关闭自动检测；如果不用代理地址，就配置多个节点地址并打开自动检测
    }
  },
  // 集群配置
  "Cluster": {
    "Enabled": false, // 启用集群：前提开启Redis缓存模式
    "ServerId": "adminnet", // 服务器标识
    "ServerIp": "", // 服务器IP
    "SignalR": {
      "RedisConfiguration": "127.0.0.1:6379,ssl=false,password=,defaultDatabase=5",
      "ChannelPrefix": "signalrPrefix_"
    },
    "DataProtecteKey": "AdminNet:DataProtection-Keys",
    "IsSentinel": false, // 是否哨兵模式
    "SentinelConfig": {
      "DefaultDb": "4",
      "EndPoints": [ // 哨兵端口
        // "***********:26380"
      ],
      "MainPrefix": "adminNet:",
      "Password": "123456",
      "SentinelPassword": "adminNet",
      "ServiceName": "adminNet",
      "SignalRChannelPrefix": "signalR:"
    }
  }
}