[{"ContainingType": "Admin.NET.Core.Service.APIJSONService", "Method": "Add", "RelativePath": "api/aPIJSON/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "tables", "Type": "Newtonsoft.Json.Linq.JObject", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Newtonsoft.Json.Linq.JObject, Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.APIJSONService", "Method": "Delete", "RelativePath": "api/aPIJSON/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "tables", "Type": "Newtonsoft.Json.Linq.JObject", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Newtonsoft.Json.Linq.JObject, Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.APIJSONService", "Method": "Query", "RelativePath": "api/aPIJSON/get", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "jobject", "Type": "Newtonsoft.Json.Linq.JObject", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Newtonsoft.Json.Linq.JObject, Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.APIJSONService", "Method": "QueryByTable", "RelativePath": "api/aPIJSON/get/{table}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "table", "Type": "System.String", "IsRequired": true}, {"Name": "jobject", "Type": "Newtonsoft.Json.Linq.JObject", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Newtonsoft.Json.Linq.JObject, Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.APIJSONService", "Method": "Edit", "RelativePath": "api/aPIJSON/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "tables", "Type": "Newtonsoft.Json.Linq.JObject", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Newtonsoft.Json.Linq.JObject, Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.ApprovalFlow.Service.ApprovalFlowService", "Method": "Add", "RelativePath": "api/approvalFlow/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Plugin.ApprovalFlow.Service.AddApprovalFlowInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.ApprovalFlow.Service.ApprovalFlowService", "Method": "Delete", "RelativePath": "api/approvalFlow/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Plugin.ApprovalFlow.Service.DeleteApprovalFlowInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.ApprovalFlow.Service.ApprovalFlowService", "Method": "GetDetail", "RelativePath": "api/approvalFlow/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Plugin.ApprovalFlow.ApprovalFlow, Admin.NET.Plugin.ApprovalFlow, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.ApprovalFlow.Service.ApprovalFlowService", "Method": "FlowList", "RelativePath": "api/approvalFlow/flowList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.ApprovalFlow.Service.ApprovalFlowService", "Method": "FormRoutes", "RelativePath": "api/approvalFlow/formRoutes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.ApprovalFlow.Service.ApprovalFlowService", "Method": "GetInfo", "RelativePath": "api/approvalFlow/info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Plugin.ApprovalFlow.ApprovalFlow, Admin.NET.Plugin.ApprovalFlow, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.ApprovalFlow.Service.ApprovalFlowService", "Method": "GetList", "RelativePath": "api/approvalFlow/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Code", "Type": "System.String", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "Field", "Type": "System.String", "IsRequired": false}, {"Name": "Order", "Type": "System.String", "IsRequired": false}, {"Name": "DescStr", "Type": "System.String", "IsRequired": false}, {"Name": "Search.Fields", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Search.Keyword", "Type": "System.String", "IsRequired": false}, {"Name": "Keyword", "Type": "System.String", "IsRequired": false}, {"Name": "Filter.Logic", "Type": "System.String", "IsRequired": false}, {"Name": "Filter.Filters", "Type": "System.Collections.Generic.IEnumerable`1[[Admin.NET.Core.Filter, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "Filter.Field", "Type": "System.String", "IsRequired": false}, {"Name": "Filter.Operator", "Type": "System.String", "IsRequired": false}, {"Name": "Filter.Value", "Type": "System.Object", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Plugin.ApprovalFlow.Service.ApprovalFlowOutput, Admin.NET.Plugin.ApprovalFlow, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.ApprovalFlow.Service.ApprovalFlowService", "Method": "Page", "RelativePath": "api/approvalFlow/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Plugin.ApprovalFlow.Service.ApprovalFlowInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Plugin.ApprovalFlow.Service.ApprovalFlowOutput, Admin.NET.Plugin.ApprovalFlow, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.ApprovalFlow.Service.ApprovalFlowService", "Method": "Update", "RelativePath": "api/approvalFlow/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Plugin.ApprovalFlow.Service.UpdateApprovalFlowInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.DemoOpenApi", "Method": "HelloWord", "RelativePath": "api/demo/helloWord", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.DingTalk.Service.DingTalkService", "Method": "DingTalkCreateAndDeliver", "RelativePath": "api/dingTalk/dingTalkCreateAndDeliver/{token}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "token", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "Admin.NET.Plugin.DingTalk.DingTalkCreateAndDeliverInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Plugin.DingTalk.DingTalkCreateAndDeliverOutput, Admin.NET.Plugin.DingTalk, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.DingTalk.Service.DingTalkService", "Method": "GetDingTalkCurrentEmployeesList", "RelativePath": "api/dingTalk/dingTalkCurrentEmployeesList/{access_token}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "access_token", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "Admin.NET.Plugin.DingTalk.GetDingTalkCurrentEmployeesListInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Plugin.DingTalk.DingTalkBaseResponse`1[[Admin.NET.Plugin.DingTalk.GetDingTalkCurrentEmployeesListOutput, Admin.NET.Plugin.DingTalk, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Plugin.DingTalk, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.DingTalk.Service.DingTalkService", "Method": "GetDingTalkCurrentEmployeesRosterList", "RelativePath": "api/dingTalk/dingTalkCurrentEmployeesRosterList/{access_token}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "access_token", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "Admin.NET.Plugin.DingTalk.GetDingTalkCurrentEmployeesRosterListInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Plugin.DingTalk.DingTalkBaseResponse`1[[System.Collections.Generic.List`1[[Admin.NET.Plugin.DingTalk.DingTalkEmpRosterFieldVo, Admin.NET.Plugin.DingTalk, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], Admin.NET.Plugin.DingTalk, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.DingTalk.Service.DingTalkService", "Method": "DingTalkSendInteractiveCards", "RelativePath": "api/dingTalk/dingTalkSendInteractiveCards/{token}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "token", "Type": "System.String", "IsRequired": true}, {"Name": "input", "Type": "Admin.NET.Plugin.DingTalk.DingTalkSendInteractiveCardsInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Plugin.DingTalk.DingTalkSendInteractiveCardsOutput, Admin.NET.Plugin.DingTalk, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.DingTalk.Service.DingTalkService", "Method": "GetDingTalkToken", "RelativePath": "api/dingTalk/dingTalkToken", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Plugin.DingTalk.GetDingTalkTokenOutput, Admin.NET.Plugin.DingTalk, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.EmqxRealtimeController", "Method": "AutoGroupDevice", "RelativePath": "api/emqxRealtime/autoGroupDevice", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.AutoGroupDeviceInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.AutoGroupDeviceOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.EmqxRealtimeController", "Method": "BatchConnect", "RelativePath": "api/emqxRealtime/batchConnect", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.EmqxBatchConnectInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.EmqxBatchOperationOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.EmqxRealtimeController", "Method": "BatchDisconnect", "RelativePath": "api/emqxRealtime/batchDisconnect", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.EmqxBatchDisconnectInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.EmqxBatchOperationOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.EmqxRealtimeController", "Method": "Connect", "RelativePath": "api/emqxRealtime/connect", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.EmqxConnectInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.EmqxRealtimeController", "Method": "Disconnect", "RelativePath": "api/emqxRealtime/disconnect", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.EmqxDisconnectInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.EmqxRealtimeController", "Method": "GenerateClientId", "RelativePath": "api/emqxRealtime/generateClientId", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.GenerateClientIdInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.GenerateClientIdOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.EmqxRealtimeController", "Method": "GetActiveConnections", "RelativePath": "api/emqxRealtime/getActiveConnections", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.EmqxActiveConnectionsOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.EmqxRealtimeController", "Method": "GetClientIdSuggestions", "RelativePath": "api/emqxRealtime/getClientIdSuggestions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DeviceType", "Type": "System.String", "IsRequired": false}, {"Name": "Location", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.ClientIdSuggestionsOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.EmqxRealtimeController", "Method": "GetConnectionStatus", "RelativePath": "api/emqxRealtime/getConnectionStatus", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "InstanceId", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.EmqxConnectionStatusOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.EmqxRealtimeController", "Method": "GetDeviceGroupRealtimeStats", "RelativePath": "api/emqxRealtime/getDeviceGroupRealtimeStats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "InstanceId", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Application.DeviceGroupRealtimeStats, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.EmqxRealtimeController", "Method": "GetInstanceDeviceOverview", "RelativePath": "api/emqxRealtime/getInstanceDeviceOverview", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "InstanceId", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.InstanceDeviceOverviewOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.EmqxRealtimeController", "Method": "Publish", "RelativePath": "api/emqxRealtime/publish", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.EmqxPublishInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.EmqxRealtimeController", "Method": "Subscribe", "RelativePath": "api/emqxRealtime/subscribe", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.EmqxSubscribeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.EmqxRealtimeController", "Method": "Unsubscribe", "RelativePath": "api/emqxRealtime/unsubscribe", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.EmqxUnsubscribeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.EmqxRealtimeController", "Method": "ValidateClientId", "RelativePath": "api/emqxRealtime/validateClientId", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.ValidateClientIdInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.ValidateClientIdOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.BaiDuTranslationService", "Method": "GenerateMenuI18nFile", "RelativePath": "api/Extend/baiDuTranslation/generateMenuI18nFile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.BaiDuTranslationService", "Method": "GeneratePageI18nFile", "RelativePath": "api/Extend/baiDuTranslation/generatePageI18nFile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.BaiDuTranslationService", "Method": "Translation", "RelativePath": "api/Extend/baiDuTranslation/translation", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "from", "Type": "System.String", "IsRequired": false}, {"Name": "to", "Type": "System.String", "IsRequired": false}, {"Name": "content", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.BaiDuTranslationResult, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.GoView.Service.GoViewProService", "Method": "Create", "RelativePath": "api/goview/project/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Plugin.GoView.Service.GoViewProCreateInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Plugin.GoView.GoViewResult`1[[Admin.NET.Plugin.GoView.Service.GoViewProCreateOutput, Admin.NET.Plugin.GoView, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.GoView.Service.GoViewProService", "Method": "Delete", "RelativePath": "api/goview/project/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.GoView.Service.GoViewProService", "Method": "Edit", "RelativePath": "api/goview/project/edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Plugin.GoView.Service.GoViewProEditInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.GoView.Service.GoViewProService", "Method": "GetBackGroundImage", "RelativePath": "api/goview/project/getBackGroundImage/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Admin.NET.Plugin.GoView.Service.GoViewProService", "Method": "GetData", "RelativePath": "api/goview/project/getData", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectId", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Plugin.GoView.GoViewResult`1[[Admin.NET.Plugin.GoView.Service.GoViewProDetailOutput, Admin.NET.Plugin.GoView, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.GoView.Service.GoViewProService", "Method": "GetIndexImage", "RelativePath": "api/goview/project/getIndexImage/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Admin.NET.Plugin.GoView.Service.GoViewProService", "Method": "GetList", "RelativePath": "api/goview/project/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Plugin.GoView.GoViewResult`1[[System.Collections.Generic.List`1[[Admin.NET.Plugin.GoView.Service.GoViewProItemOutput, Admin.NET.Plugin.GoView, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.GoView.Service.GoViewProService", "Method": "Publish", "RelativePath": "api/goview/project/publish", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Plugin.GoView.Service.GoViewProPublishInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.GoView.Service.GoViewProService", "Method": "SaveData", "RelativePath": "api/goview/project/save/data", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ProjectId", "Type": "System.Int64", "IsRequired": false}, {"Name": "Content", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.GoView.Service.GoViewProService", "Method": "Upload", "RelativePath": "api/goview/project/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "object", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Plugin.GoView.GoViewResult`1[[Admin.NET.Plugin.GoView.Service.GoViewProUploadOutput, Admin.NET.Plugin.GoView, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.GoView.Service.GoViewProService", "Method": "UploadBackGround", "RelativePath": "api/goview/project/uploadBackGround", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "object", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Plugin.GoView.GoViewResult`1[[Admin.NET.Plugin.GoView.Service.GoViewProUploadOutput, Admin.NET.Plugin.GoView, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.GoView.Service.GoViewSysService", "Method": "GetOssInfo", "RelativePath": "api/goview/sys/getOssInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Plugin.GoView.GoViewResult`1[[Admin.NET.Plugin.GoView.Service.GoViewOssUrlOutput, Admin.NET.Plugin.GoView, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.GoView.Service.GoViewSysService", "Method": "<PERSON><PERSON>", "RelativePath": "api/goview/sys/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Plugin.GoView.Service.GoViewLoginInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Plugin.GoView.GoViewResult`1[[Admin.NET.Plugin.GoView.Service.GoViewLoginOutput, Admin.NET.Plugin.GoView, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Plugin.GoView.Service.GoViewSysService", "Method": "GetLogout", "RelativePath": "api/goview/sys/logout", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttClientService", "Method": "Add", "RelativePath": "api/mqttClient/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.AddMqttClientInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttClientService", "Method": "BatchDelete", "RelativePath": "api/mqttClient/batchDelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "System.Collections.Generic.List`1[[Admin.NET.Application.DeleteMqttClientInput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttClientService", "Method": "Delete", "RelativePath": "api/mqttClient/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.DeleteMqttClientInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttClientService", "Method": "Detail", "RelativePath": "api/mqttClient/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.Entity.MqttClient, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttClientService", "Method": "Page", "RelativePath": "api/mqttClient/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.PageMqttClientInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Application.MqttClientOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttClientService", "Method": "Update", "RelativePath": "api/mqttClient/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.UpdateMqttClientInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttClientAuthService", "Method": "Add", "RelativePath": "api/mqttClientAuth/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.AddMqttClientAuthInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttClientAuthService", "Method": "BatchDelete", "RelativePath": "api/mqttClientAuth/batchDelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "System.Collections.Generic.List`1[[Admin.NET.Application.DeleteMqttClientAuthInput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttClientAuthService", "Method": "Delete", "RelativePath": "api/mqttClientAuth/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.DeleteMqttClientAuthInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttClientAuthService", "Method": "Detail", "RelativePath": "api/mqttClientAuth/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.Entity.MqttClientAuth, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttClientAuthService", "Method": "Page", "RelativePath": "api/mqttClientAuth/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.PageMqttClientAuthInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Application.MqttClientAuthOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttClientAuthService", "Method": "Update", "RelativePath": "api/mqttClientAuth/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.UpdateMqttClientAuthInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttDeviceGroupService", "Method": "Add", "RelativePath": "api/mqttDeviceGroup/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.AddMqttDeviceGroupInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttDeviceGroupService", "Method": "BatchDelete", "RelativePath": "api/mqttDeviceGroup/batchDelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "System.Collections.Generic.List`1[[Admin.NET.Application.DeleteMqttDeviceGroupInput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttDeviceGroupService", "Method": "Delete", "RelativePath": "api/mqttDeviceGroup/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.DeleteMqttDeviceGroupInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttDeviceGroupService", "Method": "Detail", "RelativePath": "api/mqttDeviceGroup/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.Entity.MqttDeviceGroup, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttDeviceGroupService", "Method": "Export", "RelativePath": "api/mqttDeviceGroup/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.PageMqttDeviceGroupInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Admin.NET.Application.MqttDeviceGroupService", "Method": "DownloadTemplate", "RelativePath": "api/mqttDeviceGroup/import", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Admin.NET.Application.MqttDeviceGroupService", "Method": "ImportData", "RelativePath": "api/mqttDeviceGroup/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Admin.NET.Application.MqttDeviceGroupService", "Method": "Page", "RelativePath": "api/mqttDeviceGroup/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.PageMqttDeviceGroupInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Application.MqttDeviceGroupOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttDeviceGroupService", "Method": "Update", "RelativePath": "api/mqttDeviceGroup/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.UpdateMqttDeviceGroupInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttInstanceService", "Method": "Add", "RelativePath": "api/mqttInstance/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.AddMqttInstanceInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttInstanceService", "Method": "BatchDelete", "RelativePath": "api/mqttInstance/batchDelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "System.Collections.Generic.List`1[[Admin.NET.Application.DeleteMqttInstanceInput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttInstanceService", "Method": "Delete", "RelativePath": "api/mqttInstance/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.DeleteMqttInstanceInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttInstanceService", "Method": "Detail", "RelativePath": "api/mqttInstance/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.Entity.MqttInstance, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttInstanceService", "Method": "Page", "RelativePath": "api/mqttInstance/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.PageMqttInstanceInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Application.MqttInstanceOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttInstanceService", "Method": "Update", "RelativePath": "api/mqttInstance/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.UpdateMqttInstanceInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttMessageHistoryService", "Method": "BatchDelete", "RelativePath": "api/mqttMessageHistory/batchDelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.BatchDeleteMqttMessageHistoryInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttMessageHistoryService", "Method": "BatchRecord", "RelativePath": "api/mqttMessageHistory/batchRecord", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.BatchRecordMqttMessageInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.BatchRecordMqttMessageOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttMessageHistoryService", "Method": "CleanHistory", "RelativePath": "api/mqttMessageHistory/cleanHistory", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.CleanHistoryInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.CleanHistoryOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttMessageHistoryService", "Method": "Delete", "RelativePath": "api/mqttMessageHistory/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.DeleteMqttMessageHistoryInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttMessageHistoryService", "Method": "Detail", "RelativePath": "api/mqttMessageHistory/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.Entity.MqttMessageHistory, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttMessageHistoryService", "Method": "GetStatistics", "RelativePath": "api/mqttMessageHistory/getStatistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "InstanceId", "Type": "System.Int64", "IsRequired": false}, {"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.MqttMessageStatisticsOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttMessageHistoryService", "Method": "Page", "RelativePath": "api/mqttMessageHistory/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.PageMqttMessageHistoryInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Application.MqttMessageHistoryOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttMessageHistoryService", "Method": "Record", "RelativePath": "api/mqttMessageHistory/record", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.RecordMqttMessageInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttMessageHistoryService", "Method": "UpdateProcessStatus", "RelativePath": "api/mqttMessageHistory/updateProcessStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.UpdateMessageProcessStatusInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttSubscriptionService", "Method": "Add", "RelativePath": "api/mqttSubscription/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.AddMqttSubscriptionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttSubscriptionService", "Method": "BatchDelete", "RelativePath": "api/mqttSubscription/batchDelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.BatchDeleteMqttSubscriptionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.BatchSubscriptionOperationOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttSubscriptionService", "Method": "BatchSubscribe", "RelativePath": "api/mqttSubscription/batchSubscribe", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.BatchSubscribeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.BatchSubscriptionOperationOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttSubscriptionService", "Method": "BatchUnsubscribe", "RelativePath": "api/mqttSubscription/batchUnsubscribe", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.BatchUnsubscribeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.BatchSubscriptionOperationOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttSubscriptionService", "Method": "Delete", "RelativePath": "api/mqttSubscription/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.DeleteMqttSubscriptionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttSubscriptionService", "Method": "Detail", "RelativePath": "api/mqttSubscription/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.MqttSubscriptionOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttSubscriptionService", "Method": "Export", "RelativePath": "api/mqttSubscription/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.ExportMqttSubscriptionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Microsoft.AspNetCore.Mvc.FileResult, Microsoft.AspNetCore.Mvc.Core, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttSubscriptionService", "Method": "GetClientSubscriptions", "RelativePath": "api/mqttSubscription/getClientSubscriptions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "InstanceId", "Type": "System.Int64", "IsRequired": false}, {"Name": "ClientId", "Type": "System.String", "IsRequired": false}, {"Name": "IncludeStatistics", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.ClientSubscriptionsOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttSubscriptionService", "Method": "GetStatistics", "RelativePath": "api/mqttSubscription/getStatistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "InstanceId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ClientId", "Type": "System.String", "IsRequired": false}, {"Name": "TopicName", "Type": "System.String", "IsRequired": false}, {"Name": "TimeRangeHours", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.SubscriptionStatisticsOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttSubscriptionService", "Method": "GetTopicSubscribers", "RelativePath": "api/mqttSubscription/getTopicSubscribers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "InstanceId", "Type": "System.Int64", "IsRequired": false}, {"Name": "TopicName", "Type": "System.String", "IsRequired": false}, {"Name": "IncludeStatistics", "Type": "System.Boolean", "IsRequired": false}, {"Name": "ActiveOnly", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.TopicSubscribersOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttSubscriptionService", "Method": "List", "RelativePath": "api/mqttSubscription/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "InstanceId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ClientId", "Type": "System.String", "IsRequired": false}, {"Name": "TopicName", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "IsEnabled", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Application.MqttSubscriptionOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttSubscriptionService", "Method": "Page", "RelativePath": "api/mqttSubscription/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.PageMqttSubscriptionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Application.MqttSubscriptionOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttSubscriptionService", "Method": "ResetStatistics", "RelativePath": "api/mqttSubscription/resetStatistics", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.ResetSubscriptionStatisticsInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttSubscriptionService", "Method": "Subscribe", "RelativePath": "api/mqttSubscription/subscribe", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.SubscribeTopicInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.SubscriptionOperationOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttSubscriptionService", "Method": "Unsubscribe", "RelativePath": "api/mqttSubscription/unsubscribe", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.UnsubscribeTopicInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.SubscriptionOperationOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttSubscriptionService", "Method": "Update", "RelativePath": "api/mqttSubscription/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.UpdateMqttSubscriptionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttTopicService", "Method": "Add", "RelativePath": "api/mqttTopic/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.AddMqttTopicInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttTopicService", "Method": "BatchDelete", "RelativePath": "api/mqttTopic/batchDelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "System.Collections.Generic.List`1[[Admin.NET.Application.DeleteMqttTopicInput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttTopicService", "Method": "Delete", "RelativePath": "api/mqttTopic/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.DeleteMqttTopicInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttTopicService", "Method": "Detail", "RelativePath": "api/mqttTopic/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Application.Entity.MqttTopic, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttTopicService", "Method": "Page", "RelativePath": "api/mqttTopic/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.PageMqttTopicInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Application.MqttTopicOutput, Admin.NET.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Application.MqttTopicService", "Method": "Update", "RelativePath": "api/mqttTopic/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Application.UpdateMqttTopicInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysAuthService", "Method": "SwaggerCheckUrl", "RelativePath": "api/swagger/checkUrl", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysAuthService", "Method": "SwaggerSubmitUrl", "RelativePath": "api/swagger/submitUrl", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserName", "Type": "System.String", "IsRequired": false}, {"Name": "Password", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysAlipayService", "Method": "AlipayPreCreate", "RelativePath": "api/sysAlipay/alipayPreCreate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AlipayPreCreateInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysAlipayService", "Method": "AlipayTradePagePay", "RelativePath": "api/sysAlipay/alipayTradePagePay", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AlipayTradePagePayInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysAlipayService", "Method": "GetAuthInfo", "RelativePath": "api/sysAlipay/authInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "user_id", "Type": "System.String", "IsRequired": false}, {"Name": "auth_code", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Admin.NET.Core.Service.SysAlipayService", "Method": "Notify", "RelativePath": "api/sysAlipay/notify", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysAuthService", "Method": "GetCaptcha", "RelativePath": "api/sysAuth/captcha", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysAuthService", "Method": "<PERSON><PERSON>", "RelativePath": "api/sysAuth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.LoginInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.Service.LoginOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysAuthService", "Method": "LoginPhone", "RelativePath": "api/sysAuth/loginPhone", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.LoginPhoneInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.Service.LoginOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysAuthService", "Method": "Logout", "RelativePath": "api/sysAuth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysAuthService", "Method": "GetRefreshToken", "RelativePath": "api/sysAuth/refreshToken", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "accessToken", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.Service.LoginOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysAuthService", "Method": "UnLockScreen", "RelativePath": "api/sysAuth/unLockScreen", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "password", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysAuthService", "Method": "GetUserInfo", "RelativePath": "api/sysAuth/userInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.Service.LoginUserOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysAuthService", "Method": "UserRegistration", "RelativePath": "api/sysAuth/userRegistration", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UserRegistrationInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCacheService", "Method": "BeginCacheLock", "RelativePath": "api/sysCache/beginCacheLock/{key}/{msTimeout}/{msExpire}/{throwOnFailure}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": true}, {"Name": "msTimeout", "Type": "System.Int32", "IsRequired": true}, {"Name": "msExpire", "Type": "System.Int32", "IsRequired": true}, {"Name": "throwOnFailure", "Type": "System.Boolean", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.IDisposable, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCacheService", "Method": "Clear", "RelativePath": "api/sysCache/clear", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCacheService", "Method": "Remove", "RelativePath": "api/sysCache/delete/{key}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCacheService", "Method": "RemoveByPrefixKey", "RelativePath": "api/sysCache/deleteByPreKey/{prefixKey}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "prefixKey", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCacheService", "Method": "GetKeyList", "RelativePath": "api/sysCache/keyList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCacheService", "Method": "GetKeysByPrefixKey", "RelativePath": "api/sysCache/keysByPrefixKey/{prefixKey}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "prefixKey", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCacheService", "Method": "GetValue", "RelativePath": "api/sysCache/value/{key}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCodeGenService", "Method": "AddCodeGen", "RelativePath": "api/sysCodeGen/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddCodeGenInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCodeGenService", "Method": "GetApplicationNamespaces", "RelativePath": "api/sysCodeGen/applicationNamespaces", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCodeGenService", "Method": "GetColumnListByTableName", "RelativePath": "api/sysCodeGen/columnListByTableName/{tableName}/{configId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tableName", "Type": "System.String", "IsRequired": true}, {"Name": "configId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.Service.ColumnOuput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCodeGenService", "Method": "GetDatabaseList", "RelativePath": "api/sysCodeGen/databaseList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.Service.DatabaseOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCodeGenService", "Method": "DeleteCodeGen", "RelativePath": "api/sysCodeGen/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "inputs", "Type": "System.Collections.Generic.List`1[[Admin.NET.Core.Service.DeleteCodeGenInput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCodeGenService", "Method": "GetDetail", "RelativePath": "api/sysCodeGen/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysCodeGen, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCodeGenService", "Method": "Page", "RelativePath": "api/sysCodeGen/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.CodeGenInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysCodeGen, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCodeGenService", "Method": "Preview", "RelativePath": "api/sysCodeGen/preview", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.SysCodeGen", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCodeGenService", "Method": "RunLocal", "RelativePath": "api/sysCodeGen/runLocal", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.SysCodeGen", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCodeGenService", "Method": "GetTableList", "RelativePath": "api/sysCodeGen/tableList/{configId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "configId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.Service.TableOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCodeGenService", "Method": "UpdateCodeGen", "RelativePath": "api/sysCodeGen/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateCodeGenInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCodeGenConfigService", "Method": "GetDetail", "RelativePath": "api/sysCodeGenConfig/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int64", "IsRequired": false}, {"Name": "CodeGenId", "Type": "System.Int64", "IsRequired": false}, {"Name": "ColumnName", "Type": "System.String", "IsRequired": false}, {"Name": "Column<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "PropertyName", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Int32", "IsRequired": false}, {"Name": "LowerPropertyName", "Type": "System.String", "IsRequired": false}, {"Name": "ColumnComment", "Type": "System.String", "IsRequired": false}, {"Name": "NetType", "Type": "System.String", "IsRequired": false}, {"Name": "DataType", "Type": "System.String", "IsRequired": false}, {"Name": "DefaultValue", "Type": "System.String", "IsRequired": false}, {"Name": "NullableNetType", "Type": "System.String", "IsRequired": false}, {"Name": "EffectType", "Type": "System.String", "IsRequired": false}, {"Name": "FkConfigId", "Type": "System.String", "IsRequired": false}, {"Name": "FkEntityName", "Type": "System.String", "IsRequired": false}, {"Name": "FkTableName", "Type": "System.String", "IsRequired": false}, {"Name": "LowerFkEntityName", "Type": "System.String", "IsRequired": false}, {"Name": "FkLinkColumnName", "Type": "System.String", "IsRequired": false}, {"Name": "FkDisplayColumns", "Type": "System.String", "IsRequired": false}, {"Name": "FkDisplayColumnList", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LowerFkDisplayColumnsList", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "FkColumnNetType", "Type": "System.String", "IsRequired": false}, {"Name": "PidColumn", "Type": "System.String", "IsRequired": false}, {"Name": "DictTypeCode", "Type": "System.String", "IsRequired": false}, {"Name": "QueryType", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "WhetherRetract", "Type": "System.String", "IsRequired": false}, {"Name": "WhetherRequired", "Type": "System.String", "IsRequired": false}, {"Name": "WhetherSortable", "Type": "System.String", "IsRequired": false}, {"Name": "WhetherTable", "Type": "System.String", "IsRequired": false}, {"Name": "WhetherAddUpdate", "Type": "System.String", "IsRequired": false}, {"Name": "WhetherImport", "Type": "System.String", "IsRequired": false}, {"Name": "Whether<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "OrderNo", "Type": "System.Int32", "IsRequired": false}, {"Name": "IsSelectorEffectType", "Type": "System.Boolean", "IsRequired": false}, {"Name": "PropertyNameTrimEndId", "Type": "System.String", "IsRequired": false}, {"Name": "LowerPropertyNameTrimEndId", "Type": "System.String", "IsRequired": false}, {"Name": "ExtendedPropertyName", "Type": "System.String", "IsRequired": false}, {"Name": "LowerExtendedPropertyName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysCodeGenConfig, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCodeGenConfigService", "Method": "GetList", "RelativePath": "api/sysCodeGenConfig/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int64", "IsRequired": false}, {"Name": "CodeGenId", "Type": "System.Int64", "IsRequired": false}, {"Name": "ColumnName", "Type": "System.String", "IsRequired": false}, {"Name": "Column<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "PropertyName", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Int32", "IsRequired": false}, {"Name": "LowerPropertyName", "Type": "System.String", "IsRequired": false}, {"Name": "ColumnComment", "Type": "System.String", "IsRequired": false}, {"Name": "NetType", "Type": "System.String", "IsRequired": false}, {"Name": "DataType", "Type": "System.String", "IsRequired": false}, {"Name": "DefaultValue", "Type": "System.String", "IsRequired": false}, {"Name": "NullableNetType", "Type": "System.String", "IsRequired": false}, {"Name": "EffectType", "Type": "System.String", "IsRequired": false}, {"Name": "FkConfigId", "Type": "System.String", "IsRequired": false}, {"Name": "FkEntityName", "Type": "System.String", "IsRequired": false}, {"Name": "FkTableName", "Type": "System.String", "IsRequired": false}, {"Name": "LowerFkEntityName", "Type": "System.String", "IsRequired": false}, {"Name": "FkLinkColumnName", "Type": "System.String", "IsRequired": false}, {"Name": "FkDisplayColumns", "Type": "System.String", "IsRequired": false}, {"Name": "FkDisplayColumnList", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LowerFkDisplayColumnsList", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "FkColumnNetType", "Type": "System.String", "IsRequired": false}, {"Name": "PidColumn", "Type": "System.String", "IsRequired": false}, {"Name": "DictTypeCode", "Type": "System.String", "IsRequired": false}, {"Name": "QueryType", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "WhetherRetract", "Type": "System.String", "IsRequired": false}, {"Name": "WhetherRequired", "Type": "System.String", "IsRequired": false}, {"Name": "WhetherSortable", "Type": "System.String", "IsRequired": false}, {"Name": "WhetherTable", "Type": "System.String", "IsRequired": false}, {"Name": "WhetherAddUpdate", "Type": "System.String", "IsRequired": false}, {"Name": "WhetherImport", "Type": "System.String", "IsRequired": false}, {"Name": "Whether<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "OrderNo", "Type": "System.Int32", "IsRequired": false}, {"Name": "IsSelectorEffectType", "Type": "System.Boolean", "IsRequired": false}, {"Name": "PropertyNameTrimEndId", "Type": "System.String", "IsRequired": false}, {"Name": "LowerPropertyNameTrimEndId", "Type": "System.String", "IsRequired": false}, {"Name": "ExtendedPropertyName", "Type": "System.String", "IsRequired": false}, {"Name": "LowerExtendedPropertyName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.Service.CodeGenConfig, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCodeGenConfigService", "Method": "UpdateCodeGenConfig", "RelativePath": "api/sysCodeGenConfig/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "inputList", "Type": "System.Collections.Generic.List`1[[Admin.NET.Core.Service.CodeGenConfig, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCommonService", "Method": "GetApiList", "RelativePath": "api/sysCommon/apiList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.Service.ApiOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCommonService", "Method": "DownloadErrorExcelTemp", "RelativePath": "api/sysCommon/downloadErrorExcelTemp", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Microsoft.AspNetCore.Mvc.IActionResult, Microsoft.AspNetCore.Mvc.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCommonService", "Method": "EncryptPlainText", "RelativePath": "api/sysCommon/encryptPlainText/{plainText}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "plainText", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCommonService", "Method": "GetSmKeyPair", "RelativePath": "api/sysCommon/smKeyPair", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.Service.SmKeyPairOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysCommonService", "Method": "StressTest", "RelativePath": "api/sysCommon/stressTest", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.StressTestInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.Service.StressTestOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysConfigService", "Method": "AddConfig", "RelativePath": "api/sysConfig/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysConfigService", "Method": "BatchDeleteConfig", "RelativePath": "api/sysConfig/batchDelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysConfigService", "Method": "BatchUpdateConfig", "RelativePath": "api/sysConfig/batchUpdate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "System.Collections.Generic.List`1[[Admin.NET.Core.Service.BatchConfigInput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysConfigService", "Method": "DeleteConfig", "RelativePath": "api/sysConfig/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysConfigService", "Method": "GetDetail", "RelativePath": "api/sysConfig/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysConfig, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysConfigService", "Method": "GetGroupList", "RelativePath": "api/sysConfig/groupList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysConfigService", "Method": "List", "RelativePath": "api/sysConfig/list", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysConfig, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysConfigService", "Method": "Page", "RelativePath": "api/sysConfig/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysConfig, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysConfigService", "Method": "SaveSysInfo", "RelativePath": "api/sysConfig/saveSysInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.InfoSaveInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysConfigService", "Method": "GetSysInfo", "RelativePath": "api/sysConfig/sysInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysConfigService", "Method": "UpdateConfig", "RelativePath": "api/sysConfig/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysConstService", "Method": "GetData", "RelativePath": "api/sysConst/data/{typeName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "typeName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.Service.ConstOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysConstService", "Method": "GetList", "RelativePath": "api/sysConst/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.Service.ConstOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDatabaseService", "Method": "AddColumn", "RelativePath": "api/sysDatabase/addColumn", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DbColumnInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDatabaseService", "Method": "AddTable", "RelativePath": "api/sysDatabase/addTable", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DbTableInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDatabaseService", "Method": "BackupDatabase", "RelativePath": "api/sysDatabase/backupDatabase", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Admin.NET.Core.Service.SysDatabaseService", "Method": "GetColumnList", "RelativePath": "api/sysDatabase/columnList/{tableName}/{configId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tableName", "Type": "System.String", "IsRequired": true}, {"Name": "configId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.Service.DbColumnOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDatabaseService", "Method": "CreateEntity", "RelativePath": "api/sysDatabase/createEntity", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.CreateEntityInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDatabaseService", "Method": "CreateSeedData", "RelativePath": "api/sysDatabase/createSeedData", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.CreateSeedDataInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDatabaseService", "Method": "GetDbTypeList", "RelativePath": "api/sysDatabase/dbTypeList/{configId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "configId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDatabaseService", "Method": "DeleteColumn", "RelativePath": "api/sysDatabase/deleteColumn", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteDbColumnInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDatabaseService", "Method": "DeleteTable", "RelativePath": "api/sysDatabase/deleteTable", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteDbTableInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDatabaseService", "Method": "GetList", "RelativePath": "api/sysDatabase/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.Service.VisualDb, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDatabaseService", "Method": "MoveColumn", "RelativePath": "api/sysDatabase/moveColumn", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.MoveDbColumnInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDatabaseService", "Method": "GetTableList", "RelativePath": "api/sysDatabase/tableList/{configId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "configId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[SqlSugar.DbTableInfo, SqlSugar, Version=5.1.4.198, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDatabaseService", "Method": "UpdateColumn", "RelativePath": "api/sysDatabase/updateColumn", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateDbColumnInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDatabaseService", "Method": "UpdateTable", "RelativePath": "api/sysDatabase/updateTable", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateDbTableInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDatabaseService", "Method": "GetVisualDbTable", "RelativePath": "api/sysDatabase/visualDbTable", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.Service.VisualDbTable, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictDataService", "Method": "AddDictData", "RelativePath": "api/sysDictData/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddDictDataInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictDataService", "Method": "GetDataList", "RelativePath": "api/sysDictData/dataList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Value", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysDictData, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictDataService", "Method": "GetDataList", "RelativePath": "api/sysDictData/dataList/{code}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysDictData, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictDataService", "Method": "DeleteDictData", "RelativePath": "api/sysDictData/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteDictDataInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictDataService", "Method": "GetDetail", "RelativePath": "api/sysDictData/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "Id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysDictData, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictDataService", "Method": "GetList", "RelativePath": "api/sysDictData/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DictTypeId", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysDictData, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictDataService", "Method": "Page", "RelativePath": "api/sysDictData/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageDictDataInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysDictData, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictDataService", "Method": "SetStatus", "RelativePath": "api/sysDictData/setStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DictDataInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictDataService", "Method": "UpdateDictData", "RelativePath": "api/sysDictData/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateDictDataInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictTypeService", "Method": "AddDictType", "RelativePath": "api/sysDictType/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddDictTypeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictTypeService", "Method": "GetAllDictList", "RelativePath": "api/sysDictType/allDictList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictTypeService", "Method": "GetDataList", "RelativePath": "api/sysDictType/dataList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Code", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysDictData, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictTypeService", "Method": "DeleteDictType", "RelativePath": "api/sysDictType/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteDictTypeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictTypeService", "Method": "GetDetail", "RelativePath": "api/sysDictType/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "Id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysDictType, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictTypeService", "Method": "GetList", "RelativePath": "api/sysDictType/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysDictType, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictTypeService", "Method": "Page", "RelativePath": "api/sysDictType/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageDictTypeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysDictType, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictTypeService", "Method": "SetStatus", "RelativePath": "api/sysDictType/setStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DictTypeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysDictTypeService", "Method": "UpdateDictType", "RelativePath": "api/sysDictType/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateDictTypeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysEmailService", "Method": "SendEmail", "RelativePath": "api/sysEmail/sendEmail/{content}/{title}/{toEmail}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "content", "Type": "System.String", "IsRequired": true}, {"Name": "title", "Type": "System.String", "IsRequired": true}, {"Name": "toEmail", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysEnumService", "Method": "GetEnumDataList", "RelativePath": "api/sysEnum/enumDataList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "EnumName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.EnumEntity, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysEnumService", "Method": "GetEnumTypeList", "RelativePath": "api/sysEnum/enumTypeList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.Service.EnumTypeOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysFileService", "Method": "DeleteFile", "RelativePath": "api/sysFile/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.BaseIdInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysFileService", "Method": "DownloadFile", "RelativePath": "api/sysFile/downloadFile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.SysFile", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Microsoft.AspNetCore.Mvc.IActionResult, Microsoft.AspNetCore.Mvc.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysFileService", "Method": "DownloadFileBase64", "RelativePath": "api/sysFile/downloadFileBase64", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "url", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysFileService", "Method": "GetFile", "RelativePath": "api/sysFile/file", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysFile, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysFileService", "Method": "GetFileByIds", "RelativePath": "api/sysFile/fileByIds", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysFile, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysFileService", "Method": "GetFolder", "RelativePath": "api/sysFile/folder", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.TreeNode, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysFileService", "Method": "Page", "RelativePath": "api/sysFile/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageFileInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysFile, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysFileService", "Method": "GetPreview", "RelativePath": "api/sysFile/preview/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Microsoft.AspNetCore.Mvc.IActionResult, Microsoft.AspNetCore.Mvc.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysFileService", "Method": "UpdateFile", "RelativePath": "api/sysFile/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.SysFile", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysFileService", "Method": "UploadAvatar", "RelativePath": "api/sysFile/uploadAvatar", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysFile, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysFileService", "Method": "UploadFile", "RelativePath": "api/sysFile/uploadFile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "File", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "FileType", "Type": "System.String", "IsRequired": false}, {"Name": "IsPublic", "Type": "System.Boolean", "IsRequired": false}, {"Name": "AllowSuffix", "Type": "System.String", "IsRequired": false}, {"Name": "targetPath", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysFile, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysFileService", "Method": "UploadFileFromBase64", "RelativePath": "api/sysFile/uploadFileFromBase64", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UploadFileFromBase64Input", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysFile, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysFileService", "Method": "UploadFiles", "RelativePath": "api/sysFile/uploadFiles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "files", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysFile, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysFileService", "Method": "UploadSignature", "RelativePath": "api/sysFile/uploadSignature", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysFile, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "AddJobDetail", "RelativePath": "api/sysJob/addJobDetail", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddJobDetailInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "AddJobTrigger", "RelativePath": "api/sysJob/addJobTrigger", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddJobTriggerInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/sysJob/cancelJob", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.JobDetailInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "CancelSleep", "RelativePath": "api/sysJob/cancelSleep", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "ClearJobTriggerRecord", "RelativePath": "api/sysJob/clearJobTriggerRecord", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "DeleteJobDetail", "RelativePath": "api/sysJob/deleteJobDetail", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteJobDetailInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "DeleteJobTrigger", "RelativePath": "api/sysJob/deleteJobTrigger", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteJobTriggerInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "GetJobClusterList", "RelativePath": "api/sysJob/jobClusterList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysJobCluster, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "GetJobTriggerList", "RelativePath": "api/sysJob/jobTriggerList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "JobId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysJobTrigger, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "ListJobGroup", "RelativePath": "api/sysJob/listJobGroup", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "PageJobDetail", "RelativePath": "api/sysJob/pageJobDetail", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageJobDetailInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.Service.JobDetailOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "PageJobTriggerRecord", "RelativePath": "api/sysJob/pageJobTriggerRecord", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageJobTriggerRecordInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysJobTriggerRecord, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/sysJob/pauseAllJob", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/sysJob/pauseJob", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.JobDetailInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/sysJob/pauseTrigger", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.JobTriggerInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "PersistAll", "RelativePath": "api/sysJob/persistAll", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "api/sysJob/runJob", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.JobDetailInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "StartAllJob", "RelativePath": "api/sysJob/startAllJob", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "api/sysJob/startJob", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.JobDetailInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "StartTrigger", "RelativePath": "api/sysJob/startTrigger", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.JobTriggerInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "UpdateJobDetail", "RelativePath": "api/sysJob/updateJobDetail", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateJobDetailInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysJobService", "Method": "Update<PERSON><PERSON><PERSON><PERSON>ger", "RelativePath": "api/sysJob/updateJob<PERSON><PERSON>ger", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateJobTriggerInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.SysLdapService", "Method": "Add", "RelativePath": "api/sysLdap/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddSysLdapInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.SysLdapService", "Method": "Delete", "RelativePath": "api/sysLdap/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteSysLdapInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.SysLdapService", "Method": "GetDetail", "RelativePath": "api/sysLdap/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysLdap, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.SysLdapService", "Method": "GetList", "RelativePath": "api/sysLdap/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysLdap, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.SysLdapService", "Method": "Page", "RelativePath": "api/sysLdap/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.SysLdapInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysLdap, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.SysLdapService", "Method": "SyncDept", "RelativePath": "api/sysLdap/syncDept", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.SyncSysLdapInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.SysLdapService", "Method": "SyncUser", "RelativePath": "api/sysLdap/syncUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.SyncSysLdapInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysUserLdap, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.SysLdapService", "Method": "Update", "RelativePath": "api/sysLdap/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateSysLdapInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysLogDiffService", "Method": "GetDetail", "RelativePath": "api/sysLogDiff/detail/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysLogDiff, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysLogDiffService", "Method": "Page", "RelativePath": "api/sysLogDiff/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageLogInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysLogDiff, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysLogExService", "Method": "Clear", "RelativePath": "api/sysLogEx/clear", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysLogExService", "Method": "GetDetail", "RelativePath": "api/sysLogEx/detail/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysLogEx, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysLogExService", "Method": "ExportLogEx", "RelativePath": "api/sysLogEx/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.LogInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Admin.NET.Core.Service.SysLogExService", "Method": "Page", "RelativePath": "api/sysLogEx/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageExLogInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysLogEx, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysLogOpService", "Method": "Clear", "RelativePath": "api/sysLogOp/clear", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysLogOpService", "Method": "GetDetail", "RelativePath": "api/sysLogOp/detail/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysLogOp, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysLogOpService", "Method": "ExportLogOp", "RelativePath": "api/sysLogOp/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.LogInput", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Admin.NET.Core.Service.SysLogOpService", "Method": "Page", "RelativePath": "api/sysLogOp/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageOpLogInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysLogOp, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysLogVisService", "Method": "Clear", "RelativePath": "api/sysLogVis/clear", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysLogVisService", "Method": "GetList", "RelativePath": "api/sysLogVis/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.Service.LogVisOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysLogVisService", "Method": "Page", "RelativePath": "api/sysLogVis/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageVisLogInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysLogVis, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysMenuService", "Method": "AddMenu", "RelativePath": "api/sysMenu/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddMenuInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysMenuService", "Method": "DeleteMenu", "RelativePath": "api/sysMenu/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteMenuInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysMenuService", "Method": "GetList", "RelativePath": "api/sysMenu/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Title", "Type": "System.String", "IsRequired": false}, {"Name": "Type", "Type": "System.String", "IsRequired": false}, {"Name": "TenantId", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysMenu, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysMenuService", "Method": "GetLoginMenuTree", "RelativePath": "api/sysMenu/loginMenuTree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.Service.MenuOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysMenuService", "Method": "GetOwnBtnPermList", "RelativePath": "api/sysMenu/ownBtnPermList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysMenuService", "Method": "UpdateMenu", "RelativePath": "api/sysMenu/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateMenuInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysMessageService", "Method": "SendAllUser", "RelativePath": "api/sysMessage/sendAllUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.MessageInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysMessageService", "Method": "SendOtherUser", "RelativePath": "api/sysMessage/sendOtherUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.MessageInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysMessageService", "Method": "SendUser", "RelativePath": "api/sysMessage/sendUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.MessageInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysMessageService", "Method": "SendUsers", "RelativePath": "api/sysMessage/sendUsers", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.MessageInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysNoticeService", "Method": "AddNotice", "RelativePath": "api/sysNotice/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddNoticeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysNoticeService", "Method": "DeleteNotice", "RelativePath": "api/sysNotice/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteNoticeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysNoticeService", "Method": "Page", "RelativePath": "api/sysNotice/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageNoticeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysNotice, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysNoticeService", "Method": "PageReceived", "RelativePath": "api/sysNotice/pageReceived", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageNoticeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysNoticeUser, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysNoticeService", "Method": "Public", "RelativePath": "api/sysNotice/public", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.NoticeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysNoticeService", "Method": "SetRead", "RelativePath": "api/sysNotice/setRead", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.NoticeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysNoticeService", "Method": "GetUnReadList", "RelativePath": "api/sysNotice/unReadList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysNotice, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysNoticeService", "Method": "UpdateNotice", "RelativePath": "api/sysNotice/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateNoticeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysOAuthService", "Method": "SignIn", "RelativePath": "api/sysOAuth/signIn", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "provider", "Type": "System.String", "IsRequired": false}, {"Name": "redirectUrl", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Microsoft.AspNetCore.Mvc.IActionResult, Microsoft.AspNetCore.Mvc.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysOAuthService", "Method": "SignInCallback", "RelativePath": "api/sysOAuth/signInCallback", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "provider", "Type": "System.String", "IsRequired": false}, {"Name": "redirectUrl", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Microsoft.AspNetCore.Mvc.IActionResult, Microsoft.AspNetCore.Mvc.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysOnlineUserService", "Method": "ForceOffline", "RelativePath": "api/sysOnlineUser/forceOffline", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "Admin.NET.Core.SysOnlineUser", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysOnlineUserService", "Method": "Page", "RelativePath": "api/sysOnlineUser/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageOnlineUserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysOnlineUser, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysOpenAccessService", "Method": "AddOpenAccess", "RelativePath": "api/sysOpenAccess/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddOpenAccessInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysOpenAccessService", "Method": "DeleteOpenAccess", "RelativePath": "api/sysOpenAccess/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteOpenAccessInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysOpenAccessService", "Method": "GenerateSignature", "RelativePath": "api/sysOpenAccess/generateSignature", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.GenerateSignatureInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysOpenAccessService", "Method": "Page", "RelativePath": "api/sysOpenAccess/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.OpenAccessInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.Service.OpenAccessOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysOpenAccessService", "Method": "CreateSecret", "RelativePath": "api/sysOpenAccess/secret", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysOpenAccessService", "Method": "UpdateOpenAccess", "RelativePath": "api/sysOpenAccess/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateOpenAccessInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysOrgService", "Method": "AddOrg", "RelativePath": "api/sysOrg/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddOrgInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysOrgService", "Method": "DeleteOrg", "RelativePath": "api/sysOrg/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteOrgInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysOrgService", "Method": "GetList", "RelativePath": "api/sysOrg/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Code", "Type": "System.String", "IsRequired": false}, {"Name": "Type", "Type": "System.String", "IsRequired": false}, {"Name": "TenantId", "Type": "System.Int64", "IsRequired": false}, {"Name": "Id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysOrg, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysOrgService", "Method": "UpdateOrg", "RelativePath": "api/sysOrg/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateOrgInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysPluginService", "Method": "AddPlugin", "RelativePath": "api/sysPlugin/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddPluginInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysPluginService", "Method": "CompileAssembly", "RelativePath": "api/sysPlugin/compileAssembly", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "csharpCode", "Type": "System.String", "IsRequired": true}, {"Name": "assemblyName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysPluginService", "Method": "DeletePlugin", "RelativePath": "api/sysPlugin/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeletePluginInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysPluginService", "Method": "Page", "RelativePath": "api/sysPlugin/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PagePluginInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysPlugin, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysPluginService", "Method": "RemoveAssembly", "RelativePath": "api/sysPlugin/removeAssembly/{assemblyName}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "assemblyName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysPluginService", "Method": "UpdatePlugin", "RelativePath": "api/sysPlugin/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdatePluginInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysPosService", "Method": "AddPos", "RelativePath": "api/sysPos/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddPosInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysPosService", "Method": "DeletePos", "RelativePath": "api/sysPos/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeletePosInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysPosService", "Method": "GetList", "RelativePath": "api/sysPos/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Code", "Type": "System.String", "IsRequired": false}, {"Name": "TenantId", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysPos, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysPosService", "Method": "UpdatePos", "RelativePath": "api/sysPos/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdatePosInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysPrintService", "Method": "AddPrint", "RelativePath": "api/sysPrint/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddPrintInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysPrintService", "Method": "DeletePrint", "RelativePath": "api/sysPrint/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeletePrintInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysPrintService", "Method": "Page", "RelativePath": "api/sysPrint/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PagePrintInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysPrint, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysPrintService", "Method": "GetPrint", "RelativePath": "api/sysPrint/print/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysPrint, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysPrintService", "Method": "UpdatePrint", "RelativePath": "api/sysPrint/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdatePrintInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysProcService", "Method": "CommonDataSet", "RelativePath": "api/sysProc/commonDataSet", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.BaseProcInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Data.DataSet, System.Data.Common, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysProcService", "Method": "PocExport", "RelativePath": "api/sysProc/pocExport", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.ExportProcByTMPInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Microsoft.AspNetCore.Mvc.IActionResult, Microsoft.AspNetCore.Mvc.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysProcService", "Method": "PocExport2", "RelativePath": "api/sysProc/pocExport2", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.ExportProcInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Microsoft.AspNetCore.Mvc.IActionResult, Microsoft.AspNetCore.Mvc.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysProcService", "Method": "ProcTable", "RelativePath": "api/sysProc/procTable", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.BaseProcInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Data.DataTable, System.Data.Common, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRegionService", "Method": "AddRegion", "RelativePath": "api/sysRegion/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddRegionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRegionService", "Method": "DeleteRegion", "RelativePath": "api/sysRegion/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteRegionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRegionService", "Method": "GetList", "RelativePath": "api/sysRegion/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysRegion, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRegionService", "Method": "Page", "RelativePath": "api/sysRegion/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageRegionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysRegion, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRegionService", "Method": "Sync", "RelativePath": "api/sysRegion/sync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRegionService", "Method": "GetTree", "RelativePath": "api/sysRegion/tree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysRegion, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRegionService", "Method": "UpdateRegion", "RelativePath": "api/sysRegion/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateRegionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRoleService", "Method": "AddRole", "RelativePath": "api/sysRole/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddRoleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRoleService", "Method": "DeleteRole", "RelativePath": "api/sysRole/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteRoleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRoleService", "Method": "GrantDataScope", "RelativePath": "api/sysRole/grantDataScope", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.RoleOrgInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRoleService", "Method": "GrantMenu", "RelativePath": "api/sysRole/grantMenu", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.RoleMenuInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRoleService", "Method": "GetList", "RelativePath": "api/sysRole/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.Service.RoleOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRoleService", "Method": "GetOwnMenuList", "RelativePath": "api/sysRole/ownMenuList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "Id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRoleService", "Method": "GetOwnOrgList", "RelativePath": "api/sysRole/ownOrgList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "Id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRoleService", "Method": "Page", "RelativePath": "api/sysRole/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageRoleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysRole, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRoleService", "Method": "SetStatus", "RelativePath": "api/sysRole/setStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.RoleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysRoleService", "Method": "UpdateRole", "RelativePath": "api/sysRole/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateRoleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysScheduleService", "Method": "AddUserSchedule", "RelativePath": "api/sysSchedule/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddScheduleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysScheduleService", "Method": "DeleteUserSchedule", "RelativePath": "api/sysSchedule/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteScheduleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysScheduleService", "Method": "GetDetail", "RelativePath": "api/sysSchedule/detail/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysSchedule, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysScheduleService", "Method": "Page", "RelativePath": "api/sysSchedule/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.ListScheduleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysSchedule, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysScheduleService", "Method": "SetStatus", "RelativePath": "api/sysSchedule/setStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.ScheduleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysScheduleService", "Method": "UpdateUserSchedule", "RelativePath": "api/sysSchedule/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateScheduleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysServerService", "Method": "GetAssemblyList", "RelativePath": "api/sysServer/assemblyList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysServerService", "Method": "GetServerBase", "RelativePath": "api/sysServer/serverBase", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysServerService", "Method": "GetServerDisk", "RelativePath": "api/sysServer/serverDisk", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysServerService", "Method": "GetServerUsed", "RelativePath": "api/sysServer/serverUsed", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysSmsService", "Method": "AliyunSendSms", "RelativePath": "api/sysSms/aliyunSendSms/{phoneNumber}/{templateId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "phoneNumber", "Type": "System.String", "IsRequired": true}, {"Name": "templateId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysSmsService", "Method": "AliyunSendSmsTemplate", "RelativePath": "api/sysSms/aliyunSendSmsTemplate/{phoneNumber}/{templateId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "phoneNumber", "Type": "System.String", "IsRequired": true}, {"Name": "templateParam", "Type": "System.Object", "IsRequired": true}, {"Name": "templateId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysSmsService", "Method": "SendSms", "RelativePath": "api/sysSms/sendSms/{phoneNumber}/{templateId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "phoneNumber", "Type": "System.String", "IsRequired": true}, {"Name": "templateId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysSmsService", "Method": "TencentSendSms", "RelativePath": "api/sysSms/tencentSendSms/{phoneNumber}/{templateId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "phoneNumber", "Type": "System.String", "IsRequired": true}, {"Name": "templateId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysSmsService", "Method": "VerifyCode", "RelativePath": "api/sysSms/verifyCode", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.SmsVerifyCodeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTemplateService", "Method": "AddTemplate", "RelativePath": "api/sysTemplate/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddTemplateInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTemplateService", "Method": "DeleteTemplate", "RelativePath": "api/sysTemplate/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.BaseIdInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTemplateService", "Method": "GetGroupList", "RelativePath": "api/sysTemplate/groupList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTemplateService", "Method": "Page", "RelativePath": "api/sysTemplate/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageTemplateInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysTemplate, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTemplateService", "Method": "ProView", "RelativePath": "api/sysTemplate/proView", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.ProViewTemplateInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTemplateService", "Method": "Render", "RelativePath": "api/sysTemplate/render", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.RenderTemplateInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTemplateService", "Method": "GetTemplate", "RelativePath": "api/sysTemplate/template/{code}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysTemplate, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTemplateService", "Method": "UpdateTemplate", "RelativePath": "api/sysTemplate/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateTemplateInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantService", "Method": "AddTenant", "RelativePath": "api/sysTenant/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddTenantInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantService", "Method": "ChangeTenant", "RelativePath": "api/sysTenant/changeTenant", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.BaseIdInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.Service.LoginOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantService", "Method": "CreateDb", "RelativePath": "api/sysTenant/createDb", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.TenantInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantService", "Method": "DeleteTenant", "RelativePath": "api/sysTenant/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteTenantInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantService", "Method": "GoTenant", "RelativePath": "api/sysTenant/goTenant", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.BaseIdInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.Service.LoginOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantService", "Method": "GrantMenu", "RelativePath": "api/sysTenant/grantMenu", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.TenantMenuInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantService", "Method": "GetList", "RelativePath": "api/sysTenant/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantService", "Method": "Page", "RelativePath": "api/sysTenant/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageTenantInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.Service.TenantOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantService", "Method": "ResetPwd", "RelativePath": "api/sysTenant/resetPwd", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.TenantUserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantService", "Method": "SetStatus", "RelativePath": "api/sysTenant/setStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.TenantInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantService", "Method": "SyncGrantMenu", "RelativePath": "api/sysTenant/syncGrantMenu", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.BaseIdInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantService", "Method": "GetTenantMenuList", "RelativePath": "api/sysTenant/tenantMenuList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantService", "Method": "UpdateTenant", "RelativePath": "api/sysTenant/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateTenantInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantService", "Method": "UserList", "RelativePath": "api/sysTenant/userList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.TenantIdInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysUser, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantConfigService", "Method": "AddConfig", "RelativePath": "api/sysTenantConfig/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantConfigService", "Method": "BatchDeleteConfig", "RelativePath": "api/sysTenantConfig/batchDelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantConfigService", "Method": "BatchUpdateConfig", "RelativePath": "api/sysTenantConfig/batchUpdate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "System.Collections.Generic.List`1[[Admin.NET.Core.Service.BatchConfigInput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantConfigService", "Method": "GetConfigValueByCode", "RelativePath": "api/sysTenantConfig/configValueByCode/{code}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantConfigService", "Method": "DeleteConfig", "RelativePath": "api/sysTenantConfig/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantConfigService", "Method": "GetDetail", "RelativePath": "api/sysTenantConfig/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysConfig, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantConfigService", "Method": "GetGroupList", "RelativePath": "api/sysTenantConfig/groupList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantConfigService", "Method": "List", "RelativePath": "api/sysTenantConfig/list", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysConfig, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantConfigService", "Method": "Page", "RelativePath": "api/sysTenantConfig/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysConfig, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysTenantConfigService", "Method": "UpdateConfig", "RelativePath": "api/sysTenantConfig/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUpdateService", "Method": "ClearLog", "RelativePath": "api/sysUpdate/clear", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUpdateService", "Method": "List", "RelativePath": "api/sysUpdate/list", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.Service.BackupOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUpdateService", "Method": "LogList", "RelativePath": "api/sysUpdate/logs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUpdateService", "Method": "Rest<PERSON>", "RelativePath": "api/sysUpdate/restore", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.RestoreInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUpdateService", "Method": "Update", "RelativePath": "api/sysUpdate/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUpdateService", "Method": "WebHook", "RelativePath": "api/sysUpdate/webHook", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUpdateService", "Method": "GetWebHookKey", "RelativePath": "api/sysUpdate/webHookKey", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserService", "Method": "AddUser", "RelativePath": "api/sysUser/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddUserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserService", "Method": "GetBaseInfo", "RelativePath": "api/sysUser/baseInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysUser, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserService", "Method": "UpdateBaseInfo", "RelativePath": "api/sysUser/baseInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "Admin.NET.Core.SysUser", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserService", "Method": "ChangePwd", "RelativePath": "api/sysUser/changePwd", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.ChangePwdInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserService", "Method": "DeleteUser", "RelativePath": "api/sysUser/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteUserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserService", "Method": "GrantRole", "RelativePath": "api/sysUser/grantRole", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UserRoleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserService", "Method": "GetOrgInfo", "RelativePath": "api/sysUser/orgInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysOrg, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserService", "Method": "GetOwnExtOrgList", "RelativePath": "api/sysUser/ownExtOrgList/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysUserExtOrg, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserService", "Method": "GetOwnRoleList", "RelativePath": "api/sysUser/ownRoleList/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserService", "Method": "Page", "RelativePath": "api/sysUser/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageUserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.Service.UserOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserService", "Method": "ResetPwd", "RelativePath": "api/sysUser/resetPwd", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.ResetPwdUserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserService", "Method": "SetStatus", "RelativePath": "api/sysUser/setStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserService", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/sysUser/unlockLogin", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UnlockLoginInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserService", "Method": "UpdateUser", "RelativePath": "api/sysUser/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateUserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserConfigService", "Method": "AddConfig", "RelativePath": "api/sysUserConfig/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserConfigService", "Method": "BatchDeleteConfig", "RelativePath": "api/sysUserConfig/batchDelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserConfigService", "Method": "BatchUpdateConfig", "RelativePath": "api/sysUserConfig/batchUpdate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "System.Collections.Generic.List`1[[Admin.NET.Core.Service.BatchConfigInput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserConfigService", "Method": "GetConfigValueByCode", "RelativePath": "api/sysUserConfig/configValueByCode/{code}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserConfigService", "Method": "DeleteConfig", "RelativePath": "api/sysUserConfig/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserConfigService", "Method": "GetDetail", "RelativePath": "api/sysUserConfig/detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysConfig, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserConfigService", "Method": "GetGroupList", "RelativePath": "api/sysUserConfig/groupList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserConfigService", "Method": "List", "RelativePath": "api/sysUserConfig/list", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysConfig, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserConfigService", "Method": "Page", "RelativePath": "api/sysUserConfig/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysConfig, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserConfigService", "Method": "UpdateConfig", "RelativePath": "api/sysUserConfig/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserMenuService", "Method": "AddUserMenu", "RelativePath": "api/sysUserMenu/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UserMenuInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserMenuService", "Method": "DeleteUserMenu", "RelativePath": "api/sysUserMenu/deleteUserMenu", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UserMenuInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserMenuService", "Method": "GetUserMenuIdList", "RelativePath": "api/sysUserMenu/userMenuIdList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserMenuService", "Method": "GetUserMenuList", "RelativePath": "api/sysUserMenu/userMenuList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.Service.MenuOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserRegWayService", "Method": "Add", "RelativePath": "api/sysUserRegWay/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddUserRegWayInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserRegWayService", "Method": "Delete", "RelativePath": "api/sysUserRegWay/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.BaseIdInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserRegWayService", "Method": "List", "RelativePath": "api/sysUserRegWay/list", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.PageUserRegWayInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.Service.UserRegWayOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysUserRegWayService", "Method": "Update", "RelativePath": "api/sysUserRegWay/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.UpdateUserRegWayInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatService", "Method": "DeleteMessageTemplate", "RelativePath": "api/sysWechat/deleteMessageTemplate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteMessageTemplateInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatService", "Method": "GenAuthUrl", "RelativePath": "api/sysWechat/genAuthUrl", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.GenAuthUrlInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatService", "Method": "GenConfigPara", "RelativePath": "api/sysWechat/genConfigPara", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.SignatureInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatService", "Method": "GetMessageTemplateList", "RelativePath": "api/sysWechat/messageTemplateList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatService", "Method": "OpenIdLogin", "RelativePath": "api/sysWechat/openIdLogin", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.WechatUserLogin", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatService", "Method": "SendTemplateMessage", "RelativePath": "api/sysWechat/sendTemplateMessage", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.MessageTemplateSendInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatService", "Method": "SnsOAuth2", "RelativePath": "api/sysWechat/snsOAuth2", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Code", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatPayService", "Method": "GenerateParametersForJsapiPay", "RelativePath": "api/sysWechatPay/generateParametersForJsapiPay", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.WechatPayParaInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.Service.WechatPayParaOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatPayService", "Method": "ListRefund", "RelativePath": "api/sysWechatPay/listRefund", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Collections.Generic.List`1[[Admin.NET.Core.SysWechatRefund, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatPayService", "Method": "Page", "RelativePath": "api/sysWechatPay/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.WechatPayPageInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysWechatPay, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatPayService", "Method": "PayCallBack", "RelativePath": "api/sysWechatPay/payCallBack", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.Service.WechatPayOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatPayService", "Method": "GetPayInfo", "RelativePath": "api/sysWechatPay/payInfo/{tradeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tradeId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysWechatPay, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatPayService", "Method": "GetPayInfoFromWechat", "RelativePath": "api/sysWechatPay/payInfoFromWechat/{tradeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tradeId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysWechatPay, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatPayService", "Method": "PayPartnerCallBack", "RelativePath": "api/sysWechatPay/payPartnerCallBack", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatPayService", "Method": "CreatePayPartnerTransaction", "RelativePath": "api/sysWechatPay/payPartnerTransaction", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.WechatPayTransactionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatPayService", "Method": "CreatePayTransaction", "RelativePath": "api/sysWechatPay/payTransaction", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.WechatPayTransactionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.Service.WechatPayTransactionOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatPayService", "Method": "CreatePayTransactionNative", "RelativePath": "api/sysWechatPay/payTransactionNative", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.WechatPayTransactionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatPayService", "Method": "CreateRefundDomestic", "RelativePath": "api/sysWechatPay/refundDomestic", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.WechatPayRefundDomesticInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatPayService", "Method": "GetRefundInfoFromWechat", "RelativePath": "api/sysWechatPay/refundInfoFromWechat/{refundId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "refundId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysWechatRefund, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatUserService", "Method": "AddWechatUser", "RelativePath": "api/sysWechatUser/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.SysWechatUser", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatUserService", "Method": "DeleteWechatUser", "RelativePath": "api/sysWechatUser/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.DeleteWechatUserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatUserService", "Method": "Page", "RelativePath": "api/sysWechatUser/page", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.WechatUserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SqlSugarPagedList`1[[Admin.NET.Core.SysWechatUser, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWechatUserService", "Method": "UpdateWechatUser", "RelativePath": "api/sysWechatUser/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.SysWechatUser", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWxOpenService", "Method": "AddSubscribeMessageTemplate", "RelativePath": "api/sysWxOpen/addSubscribeMessageTemplate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.AddSubscribeMessageTemplateInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWxOpenService", "Method": "GenerateQRImageAsync", "RelativePath": "api/sysWxOpen/generateQRImage", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.GenerateQRImageInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.Service.GenerateQRImageOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWxOpenService", "Method": "GenerateQRImageUnlimitAsync", "RelativePath": "api/sysWxOpen/generateQRImageUnlimit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.GenerateQRImageUnLimitInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.Service.GenerateQRImageOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWxOpenService", "Method": "GetMessageTemplateList", "RelativePath": "api/sysWxOpen/messageTemplateList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWxOpenService", "Method": "SendSubscribeMessage", "RelativePath": "api/sysWxOpen/sendSubscribeMessage", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.SendSubscribeMessageInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWxOpenService", "Method": "Set<PERSON>ickName", "RelativePath": "api/sysWxOpen/setNickName", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.SetNickNameInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWxOpenService", "Method": "UploadAvatar", "RelativePath": "api/sysWxOpen/uploadAvatar", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "OpenId", "Type": "System.String", "IsRequired": false}, {"Name": "File", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "FileType", "Type": "System.String", "IsRequired": false}, {"Name": "Path", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.SysFile, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWxOpenService", "Method": "GetUserInfo", "RelativePath": "api/sysWxOpen/userInfo/{openid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "openid", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWxOpenService", "Method": "GetWxOpenId", "RelativePath": "api/sysWxOpen/wxOpenId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "JsCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.Service.WxOpenIdOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWxOpenService", "Method": "WxOpenIdLogin", "RelativePath": "api/sysWxOpen/wxOpenIdLogin", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.NET.Core.Service.WxOpenIdLoginInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.NET.Core.Service.SysWxOpenService", "Method": "GetWxPhone", "RelativePath": "api/sysWxOpen/wxPhone", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Code", "Type": "System.String", "IsRequired": false}, {"Name": "OpenId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.NET.Core.AdminResult`1[[Admin.NET.Core.Service.WxPhoneOutput, Admin.NET.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]