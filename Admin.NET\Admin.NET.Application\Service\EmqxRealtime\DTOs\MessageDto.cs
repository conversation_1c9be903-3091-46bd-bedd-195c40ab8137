namespace Admin.NET.Application.Service.EmqxRealtime.DTOs;

/// <summary>
/// 消息DTO
/// </summary>
public class MessageDto
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 主题
    /// </summary>
    public string Topic { get; set; } = string.Empty;

    /// <summary>
    /// 消息内容
    /// </summary>
    public string Payload { get; set; } = string.Empty;

    /// <summary>
    /// 消息质量等级
    /// </summary>
    public int QoS { get; set; }

    /// <summary>
    /// 是否保留消息
    /// </summary>
    public bool Retain { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// MQTT实例ID
    /// </summary>
    public int InstanceId { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public string? DeviceId { get; set; }

    /// <summary>
    /// 设备分组
    /// </summary>
    public string? DeviceGroup { get; set; }

    /// <summary>
    /// 消息大小（字节）
    /// </summary>
    public int MessageSize { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string? ClientId { get; set; }
}