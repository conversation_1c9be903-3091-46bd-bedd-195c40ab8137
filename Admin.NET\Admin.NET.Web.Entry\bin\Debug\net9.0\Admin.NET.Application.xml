<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Admin.NET.Application</name>
    </assembly>
    <members>
        <member name="T:Admin.NET.Application.ApplicationConst">
            <summary>
            业务应用相关常量
            </summary>
        </member>
        <member name="F:Admin.NET.Application.ApplicationConst.GroupName">
            <summary>
            API分组名称
            </summary>
        </member>
        <member name="T:Admin.NET.Application.Entity.MqttClient">
            <summary>
            MQTT 客户端连接信息表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.GroupId">
            <summary>
            设备组ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.ProductKey">
            <summary>
            产品Key
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.DeviceSecret">
            <summary>
            设备密钥（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.IpAddress">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.Port">
            <summary>
            端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.Status">
            <summary>
            连接状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.ProtocolVersion">
            <summary>
            协议版本
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.KeepAlive">
            <summary>
            心跳间隔（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.CleanSession">
            <summary>
            清除会话（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.ConnectedAt">
            <summary>
            连接时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.DisconnectedAt">
            <summary>
            断开时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.MessagesSent">
            <summary>
            发送消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.MessagesReceived">
            <summary>
            接收消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.BytesSent">
            <summary>
            发送字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.BytesReceived">
            <summary>
            接收字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.SubscriptionCount">
            <summary>
            订阅数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.MaxSubscriptions">
            <summary>
            最大订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.DeviceType">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.DeviceVersion">
            <summary>
            设备版本
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.DeviceTags">
            <summary>
            设备标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.LocationInfo">
            <summary>
            位置信息（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.IsOnline">
            <summary>
            是否在线（0:离线，1:在线）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.AuthMode">
            <summary>
            认证模式
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.ClientType">
            <summary>
            客户端类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.LastError">
            <summary>
            最后错误信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClient.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.Entity.MqttClientAuth">
            <summary>
            MQTT 客户端认证信息表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.ClientRefId">
            <summary>
            客户端引用ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.AuthMode">
            <summary>
            认证模式（Username、AliyunSignature、JWT等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.PasswordHash">
            <summary>
            密码哈希
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.PasswordSalt">
            <summary>
            密码盐值
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.AccessKeyId">
            <summary>
            AccessKey ID（阿里云认证）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.AccessKeySecret">
            <summary>
            AccessKey Secret（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.SignMethod">
            <summary>
            签名算法（hmacmd5、hmacsha1、hmacsha256）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.JwtSecret">
            <summary>
            JWT密钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.JwtExpiry">
            <summary>
            JWT过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.JwtToken">
            <summary>
            JWT令牌
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.Certificate">
            <summary>
            客户端证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.PrivateKey">
            <summary>
            私钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.CaCertificate">
            <summary>
            CA证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.AllowedIPs">
            <summary>
            允许的IP地址列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.DeniedIPs">
            <summary>
            拒绝的IP地址列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.RateLimit">
            <summary>
            速率限制（消息/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.LoginAttempts">
            <summary>
            登录尝试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.ExpireTime">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.LastAuthTime">
            <summary>
            最后认证时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.LastLoginTime">
            <summary>
            最后登录时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.LastLoginIP">
            <summary>
            最后登录IP
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.SuccessCount">
            <summary>
            认证成功次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.AuthSuccessCount">
            <summary>
            认证成功总数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.FailedCount">
            <summary>
            认证失败次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.IsLocked">
            <summary>
            是否锁定（0:未锁定，1:已锁定）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.LockTime">
            <summary>
            锁定时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.UnlockTime">
            <summary>
            解锁时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.LockReason">
            <summary>
            锁定原因
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.MaxFailedCount">
            <summary>
            最大失败次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.LockDuration">
            <summary>
            锁定持续时间（分钟）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.IpWhitelist">
            <summary>
            IP白名单
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.IpBlacklist">
            <summary>
            IP黑名单
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.AllowedTopics">
            <summary>
            允许的主题列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.DeniedTopics">
            <summary>
            拒绝的主题列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.CurrentConnections">
            <summary>
            当前连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.MaxSubscriptions">
            <summary>
            最大订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.MessageRateLimit">
            <summary>
            消息速率限制
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.ByteRateLimit">
            <summary>
            字节速率限制
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttClientAuth.Key">
            <summary>
            键值（用于缓存等场景）
            </summary>
        </member>
        <member name="T:Admin.NET.Application.Entity.MqttDeviceGroup">
            <summary>
            MQTT设备组管理表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.GroupId">
            <summary>
            设备组标识（如：GID_TestGroup）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.GroupName">
            <summary>
            设备组名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.GroupType">
            <summary>
            设备组类型（Production、Test、Development等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.ProductKey">
            <summary>
            产品Key（阿里云IoT产品标识）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.Description">
            <summary>
            设备组描述
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.MaxDeviceCount">
            <summary>
            最大设备数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.CurrentDeviceCount">
            <summary>
            当前设备数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.AllowedTopicPatterns">
            <summary>
            允许的主题模式（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.DeniedTopicPatterns">
            <summary>
            禁止的主题模式（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.DefaultQosLevel">
            <summary>
            默认QoS等级（0,1,2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.MaxMessageSize">
            <summary>
            最大消息大小（字节）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.MessageRateLimit">
            <summary>
            消息速率限制（消息/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.ByteRateLimit">
            <summary>
            字节速率限制（字节/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.KeepAliveTimeout">
            <summary>
            心跳超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.EnableRetainMessage">
            <summary>
            是否允许保留消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.EnableWildcardSubscription">
            <summary>
            是否允许通配符订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.EnableSharedSubscription">
            <summary>
            是否允许共享订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.IpWhitelist">
            <summary>
            IP白名单（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.IpBlacklist">
            <summary>
            IP黑名单（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.AllowedTimeRanges">
            <summary>
            允许连接的时间范围（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.SecurityLevel">
            <summary>
            安全级别（Low、Medium、High）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.EncryptionRequired">
            <summary>
            是否要求加密连接
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.CertificateRequired">
            <summary>
            是否要求客户端证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.AuthMode">
            <summary>
            认证模式（Signature、Username、JWT、Certificate）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.SignMethod">
            <summary>
            签名算法（hmacsha1、hmacsha256、hmacmd5）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.AccessKeyId">
            <summary>
            AccessKey ID（阿里云认证）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.AccessKeySecret">
            <summary>
            AccessKey Secret（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.JwtSecret">
            <summary>
            JWT密钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.JwtExpiry">
            <summary>
            JWT过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.CaCertificate">
            <summary>
            CA证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.ExpireTime">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.CreatedDeviceCount">
            <summary>
            已创建设备数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.OnlineDeviceCount">
            <summary>
            在线设备数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.TotalMessageCount">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.TotalByteCount">
            <summary>
            总字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.AlertRules">
            <summary>
            告警规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.MonitorConfig">
            <summary>
            监控配置（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.Tags">
            <summary>
            标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttDeviceGroup.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.Entity.MqttInstance">
            <summary>
            MQTT 实例信息表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.InstanceName">
            <summary>
            实例名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.InstanceCode">
            <summary>
            实例编码
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.InstanceType">
            <summary>
            实例类型（EMQX、阿里云等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.ServerHost">
            <summary>
            服务器地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.ServerPort">
            <summary>
            服务器端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.ApiHost">
            <summary>
            API地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.ApiPort">
            <summary>
            API端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.ApiUsername">
            <summary>
            API用户名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.ApiPassword">
            <summary>
            API密码（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.Status">
            <summary>
            实例状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.EnableSsl">
            <summary>
            是否启用SSL（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.SslPort">
            <summary>
            SSL端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.EnableWebSocket">
            <summary>
            是否启用WebSocket（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.WsPort">
            <summary>
            WebSocket端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.WssPort">
            <summary>
            WebSocket SSL端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.CurrentConnections">
            <summary>
            当前连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.AliyunProductKey">
            <summary>
            阿里云产品Key
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.AliyunRegionId">
            <summary>
            阿里云区域ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.AliyunInstanceId">
            <summary>
            阿里云实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.DeviceIdPrefix">
            <summary>
            设备ID前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.GroupIdPrefix">
            <summary>
            组ID前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.AliyunCompatible">
            <summary>
            是否兼容阿里云（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.ConfigJson">
            <summary>
            配置JSON
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.Host">
            <summary>
            主机地址（别名）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.Port">
            <summary>
            端口（别名）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.LastHeartbeat">
            <summary>
            最后心跳时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.TotalMessages">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.TotalBytes">
            <summary>
            总字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttInstance.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.Entity.MqttMessageHistory">
            <summary>
            MQTT消息历史记录表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.Payload">
            <summary>
            消息内容
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.QosLevel">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.IsRetained">
            <summary>
            是否保留消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.Direction">
            <summary>
            消息方向（Publish/Subscribe）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.MessageSize">
            <summary>
            消息大小（字节）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.SenderIpAddress">
            <summary>
            发送者IP地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.ReceivedAt">
            <summary>
            接收时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.ProcessStatus">
            <summary>
            处理状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.ProcessedAt">
            <summary>
            处理时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.MessageTags">
            <summary>
            消息标签（JSON格式）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.ExtendedProperties">
            <summary>
            扩展属性（JSON格式）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttMessageHistory.Key">
            <summary>
            键值（用于缓存等场景）
            </summary>
        </member>
        <member name="T:Admin.NET.Application.Entity.MqttSubscription">
            <summary>
            MQTT 订阅关系表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.ClientRefId">
            <summary>
            客户端引用ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.TopicId">
            <summary>
            主题ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.QosLevel">
            <summary>
            QoS等级（0,1,2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.Status">
            <summary>
            订阅状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.SubscribedAt">
            <summary>
            订阅时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.UnsubscribedAt">
            <summary>
            取消订阅时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.MessagesReceived">
            <summary>
            接收消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.BytesReceived">
            <summary>
            接收字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.MessagesDropped">
            <summary>
            丢弃消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.LastMessageId">
            <summary>
            最后消息ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.IsSharedSubscription">
            <summary>
            是否共享订阅（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.SharedGroupName">
            <summary>
            共享组名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.SubscriptionOptions">
            <summary>
            订阅选项（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.MessageFilter">
            <summary>
            消息过滤器
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.MaxQueueLength">
            <summary>
            最大队列长度
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.CurrentQueueLength">
            <summary>
            当前队列长度
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.HandlingStrategy">
            <summary>
            处理策略
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.MaxRetryCount">
            <summary>
            最大重试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.LastError">
            <summary>
            最后错误信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.Statistics">
            <summary>
            统计信息（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.ConfigJson">
            <summary>
            配置JSON
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttSubscription.MessageCount">
            <summary>
            消息数量
            </summary>
        </member>
        <member name="T:Admin.NET.Application.Entity.MqttTopic">
            <summary>
            MQTT 主题信息表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.TopicType">
            <summary>
            主题类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.QosLevel">
            <summary>
            QoS等级（0,1,2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.RetainMessage">
            <summary>
            是否保留消息（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.SubscriptionCount">
            <summary>
            订阅数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.MessageCount">
            <summary>
            消息数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.ByteCount">
            <summary>
            字节数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.LastMessageContent">
            <summary>
            最后消息内容
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.MessageExpiry">
            <summary>
            消息过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.AllowPublish">
            <summary>
            允许发布（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.AllowSubscribe">
            <summary>
            允许订阅（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.PublishPermissions">
            <summary>
            发布权限（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.SubscribePermissions">
            <summary>
            订阅权限（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.IsRetained">
            <summary>
            是否保留（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.TopicTags">
            <summary>
            主题标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.IsSystemTopic">
            <summary>
            是否系统主题（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.MonitorConfig">
            <summary>
            监控配置（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.AlertRules">
            <summary>
            告警规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.ForwardRules">
            <summary>
            转发规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.MqttTopic.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.SysUserEventSubscriber">
            <summary>
            系统用户操作事件订阅
            </summary>
        </member>
        <member name="M:Admin.NET.Application.SysUserEventSubscriber.AddUser(Furion.EventBus.EventHandlerExecutingContext)">
            <summary>
            增加系统用户
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.SysUserEventSubscriber.RegisterUser(Furion.EventBus.EventHandlerExecutingContext)">
            <summary>
            注册用户
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.SysUserEventSubscriber.UpdateUser(Furion.EventBus.EventHandlerExecutingContext)">
            <summary>
            更新系统用户
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.SysUserEventSubscriber.DeleteUser(Furion.EventBus.EventHandlerExecutingContext)">
            <summary>
            删除系统用户
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.SysUserEventSubscriber.SetUserStatus(Furion.EventBus.EventHandlerExecutingContext)">
            <summary>
            设置系统用户状态
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.SysUserEventSubscriber.UpdateUserRole(Furion.EventBus.EventHandlerExecutingContext)">
            <summary>
            授权用户角色
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.SysUserEventSubscriber.UnlockUserLogin(Furion.EventBus.EventHandlerExecutingContext)">
            <summary>
            解除登录锁定
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.SysUserEventSubscriber.Dispose">
            <summary>
            释放服务作用域
            </summary>
        </member>
        <member name="T:Admin.NET.Application.DemoOpenApi">
            <summary>
            示例开放接口
            </summary>
        </member>
        <member name="T:Admin.NET.Application.EmqxRealtimeController">
            <summary>
            EMQX实时数据交换控制器 🧩
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeController.Connect(Admin.NET.Application.EmqxConnectInput)">
            <summary>
            连接到EMQX服务器 🔗
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeController.Disconnect(Admin.NET.Application.EmqxDisconnectInput)">
            <summary>
            断开EMQX连接 ❌
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeController.Publish(Admin.NET.Application.EmqxPublishInput)">
            <summary>
            发布消息到EMQX 📤
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeController.Subscribe(Admin.NET.Application.EmqxSubscribeInput)">
            <summary>
            订阅EMQX主题 📥
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeController.Unsubscribe(Admin.NET.Application.EmqxUnsubscribeInput)">
            <summary>
            取消订阅EMQX主题 📤
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeController.GetConnectionStatus(Admin.NET.Application.EmqxConnectionStatusInput)">
            <summary>
            获取连接状态 📊
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeController.GetActiveConnections">
            <summary>
            获取所有活跃连接 📋
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeController.BatchConnect(Admin.NET.Application.EmqxBatchConnectInput)">
            <summary>
            批量连接EMQX实例 🔗
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeController.BatchDisconnect(Admin.NET.Application.EmqxBatchDisconnectInput)">
            <summary>
            批量断开EMQX连接 ❌
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeController.AutoGroupDevice(Admin.NET.Application.AutoGroupDeviceInput)">
            <summary>
            自动分组设备 🏷️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeController.GenerateClientId(Admin.NET.Application.GenerateClientIdInput)">
            <summary>
            生成标准ClientId 🆔
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeController.ValidateClientId(Admin.NET.Application.ValidateClientIdInput)">
            <summary>
            验证ClientId格式 ✅
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeController.GetDeviceGroupRealtimeStats(Admin.NET.Application.GetDeviceGroupRealtimeStatsInput)">
            <summary>
            获取设备组实时统计 📊
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeController.GetInstanceDeviceOverview(Admin.NET.Application.GetInstanceDeviceOverviewInput)">
            <summary>
            获取实例设备分布概览 🗺️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeController.GetClientIdSuggestions(Admin.NET.Application.GetClientIdSuggestionsInput)">
            <summary>
            获取ClientId建议 💡
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Application.EmqxConnectInput">
            <summary>
            EMQX连接输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxConnectInput.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="T:Admin.NET.Application.EmqxDisconnectInput">
            <summary>
            EMQX断开连接输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxDisconnectInput.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="T:Admin.NET.Application.EmqxPublishInput">
            <summary>
            EMQX发布消息输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxPublishInput.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxPublishInput.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxPublishInput.Payload">
            <summary>
            消息内容
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxPublishInput.Qos">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxPublishInput.Retain">
            <summary>
            是否保留消息
            </summary>
        </member>
        <member name="T:Admin.NET.Application.EmqxSubscribeInput">
            <summary>
            EMQX订阅主题输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxSubscribeInput.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxSubscribeInput.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxSubscribeInput.Qos">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="T:Admin.NET.Application.EmqxUnsubscribeInput">
            <summary>
            EMQX取消订阅输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxUnsubscribeInput.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxUnsubscribeInput.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="T:Admin.NET.Application.EmqxConnectionStatusInput">
            <summary>
            EMQX连接状态查询输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxConnectionStatusInput.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="T:Admin.NET.Application.EmqxBatchConnectInput">
            <summary>
            EMQX批量连接输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxBatchConnectInput.InstanceIds">
            <summary>
            MQTT实例ID列表
            </summary>
        </member>
        <member name="T:Admin.NET.Application.EmqxBatchDisconnectInput">
            <summary>
            EMQX批量断开连接输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxBatchDisconnectInput.InstanceIds">
            <summary>
            MQTT实例ID列表
            </summary>
        </member>
        <member name="T:Admin.NET.Application.EmqxConnectionStatusOutput">
            <summary>
            EMQX连接状态输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxConnectionStatusOutput.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxConnectionStatusOutput.IsConnected">
            <summary>
            是否已连接
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxConnectionStatusOutput.Status">
            <summary>
            连接状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxConnectionStatusOutput.CheckTime">
            <summary>
            检查时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.EmqxActiveConnectionsOutput">
            <summary>
            EMQX活跃连接输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxActiveConnectionsOutput.ActiveInstanceIds">
            <summary>
            活跃的实例ID列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxActiveConnectionsOutput.TotalCount">
            <summary>
            总数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxActiveConnectionsOutput.CheckTime">
            <summary>
            检查时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.EmqxBatchOperationOutput">
            <summary>
            EMQX批量操作输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxBatchOperationOutput.Results">
            <summary>
            操作结果列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxBatchOperationOutput.TotalCount">
            <summary>
            总数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxBatchOperationOutput.SuccessCount">
            <summary>
            成功数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxBatchOperationOutput.FailureCount">
            <summary>
            失败数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxBatchOperationOutput.OperationTime">
            <summary>
            操作时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.EmqxOperationResult">
            <summary>
            EMQX操作结果
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxOperationResult.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxOperationResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxOperationResult.Message">
            <summary>
            消息
            </summary>
        </member>
        <member name="T:Admin.NET.Application.AutoGroupDeviceInput">
            <summary>
            自动分组设备输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AutoGroupDeviceInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AutoGroupDeviceInput.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="T:Admin.NET.Application.AutoGroupDeviceOutput">
            <summary>
            自动分组设备输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AutoGroupDeviceOutput.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AutoGroupDeviceOutput.DeviceGroup">
            <summary>
            设备组信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AutoGroupDeviceOutput.Message">
            <summary>
            消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AutoGroupDeviceOutput.GroupTime">
            <summary>
            分组时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.GenerateClientIdInput">
            <summary>
            生成ClientId输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GenerateClientIdInput.InstancePrefix">
            <summary>
            实例前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GenerateClientIdInput.DeviceType">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GenerateClientIdInput.Location">
            <summary>
            位置
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GenerateClientIdInput.UniqueId">
            <summary>
            唯一标识
            </summary>
        </member>
        <member name="T:Admin.NET.Application.GenerateClientIdOutput">
            <summary>
            生成ClientId输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GenerateClientIdOutput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GenerateClientIdOutput.IsValid">
            <summary>
            是否有效
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GenerateClientIdOutput.GenerateTime">
            <summary>
            生成时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.ValidateClientIdInput">
            <summary>
            验证ClientId输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ValidateClientIdInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="T:Admin.NET.Application.ValidateClientIdOutput">
            <summary>
            验证ClientId输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ValidateClientIdOutput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ValidateClientIdOutput.IsValid">
            <summary>
            是否有效
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ValidateClientIdOutput.Message">
            <summary>
            消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ValidateClientIdOutput.ValidateTime">
            <summary>
            验证时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.GetDeviceGroupRealtimeStatsInput">
            <summary>
            获取设备组实时统计输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetDeviceGroupRealtimeStatsInput.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="T:Admin.NET.Application.GetInstanceDeviceOverviewInput">
            <summary>
            获取实例设备分布概览输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetInstanceDeviceOverviewInput.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="T:Admin.NET.Application.GetClientIdSuggestionsInput">
            <summary>
            获取ClientId建议输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetClientIdSuggestionsInput.DeviceType">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetClientIdSuggestionsInput.Location">
            <summary>
            设备位置
            </summary>
        </member>
        <member name="T:Admin.NET.Application.InstanceDeviceOverviewOutput">
            <summary>
            实例设备分布概览输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.InstanceDeviceOverviewOutput.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.InstanceDeviceOverviewOutput.TotalDeviceCount">
            <summary>
            设备总数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.InstanceDeviceOverviewOutput.OnlineDeviceCount">
            <summary>
            在线设备数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.InstanceDeviceOverviewOutput.DeviceTypeStats">
            <summary>
            设备类型统计
            </summary>
        </member>
        <member name="P:Admin.NET.Application.InstanceDeviceOverviewOutput.LocationStats">
            <summary>
            位置统计
            </summary>
        </member>
        <member name="P:Admin.NET.Application.InstanceDeviceOverviewOutput.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.ClientIdSuggestionsOutput">
            <summary>
            ClientId建议输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientIdSuggestionsOutput.DeviceType">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientIdSuggestionsOutput.Location">
            <summary>
            设备位置
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientIdSuggestionsOutput.Suggestions">
            <summary>
            建议的ClientId列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientIdSuggestionsOutput.GeneratedAt">
            <summary>
            生成时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.DeviceTypeStats">
            <summary>
            设备类型统计
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeviceTypeStats.DeviceType">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeviceTypeStats.OnlineCount">
            <summary>
            在线数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeviceTypeStats.TotalCount">
            <summary>
            总数量
            </summary>
        </member>
        <member name="T:Admin.NET.Application.LocationStats">
            <summary>
            位置统计
            </summary>
        </member>
        <member name="P:Admin.NET.Application.LocationStats.Location">
            <summary>
            位置
            </summary>
        </member>
        <member name="P:Admin.NET.Application.LocationStats.OnlineCount">
            <summary>
            在线数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.LocationStats.TotalCount">
            <summary>
            总数量
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttDeviceGroupInfo">
            <summary>
            MQTT设备组信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupInfo.GroupId">
            <summary>
            设备组ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupInfo.GroupName">
            <summary>
            设备组名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupInfo.DeviceType">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupInfo.DeviceLocation">
            <summary>
            设备位置
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupInfo.InstancePrefix">
            <summary>
            实例前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupInfo.UniqueId">
            <summary>
            唯一标识
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupInfo.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.DeviceGroupRealtimeStats">
            <summary>
            设备组实时统计
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeviceGroupRealtimeStats.GroupId">
            <summary>
            设备组ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeviceGroupRealtimeStats.GroupName">
            <summary>
            设备组名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeviceGroupRealtimeStats.DeviceType">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeviceGroupRealtimeStats.Location">
            <summary>
            设备位置
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeviceGroupRealtimeStats.OnlineDeviceCount">
            <summary>
            在线设备数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeviceGroupRealtimeStats.TotalDeviceCount">
            <summary>
            设备总数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeviceGroupRealtimeStats.RecentMessageCount">
            <summary>
            最近消息数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeviceGroupRealtimeStats.LastUpdateTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.ActiveConnectionInfo">
            <summary>
            活跃连接信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ActiveConnectionInfo.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ActiveConnectionInfo.InstanceName">
            <summary>
            实例名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ActiveConnectionInfo.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ActiveConnectionInfo.IsConnected">
            <summary>
            是否连接
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ActiveConnectionInfo.SenderIpAddress">
            <summary>
            发送者IP地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ActiveConnectionInfo.ConnectedAt">
            <summary>
            连接时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ActiveConnectionInfo.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ActiveConnectionInfo.DeviceGroupId">
            <summary>
            设备组ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ActiveConnectionInfo.DeviceGroupName">
            <summary>
            设备组名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ActiveConnectionInfo.DeviceType">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ActiveConnectionInfo.DeviceLocation">
            <summary>
            设备位置
            </summary>
        </member>
        <member name="T:Admin.NET.Application.RealtimeSubscriptionController">
            <summary>
            实时订阅服务控制器 📡
            </summary>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionController.GetStatistics">
            <summary>
            获取实时统计信息 📊
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionController.GetAvailableInstances">
            <summary>
            获取可用的MQTT实例列表 📋
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionController.TestSubscription(Admin.NET.Application.TestSubscriptionInput)">
            <summary>
            测试订阅连接 🔧
            </summary>
            <param name="input">测试输入</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionController.GetTopicSuggestions(System.Int64)">
            <summary>
            获取主题建议列表 💡
            </summary>
            <param name="instanceId">实例ID</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionController.GetSubscriptionTemplates">
            <summary>
            获取订阅配置模板 📝
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Application.MqttInstanceInfo">
            <summary>
            MQTT实例信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceInfo.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceInfo.IsConnected">
            <summary>
            是否已连接
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceInfo.LastCheckTime">
            <summary>
            最后检查时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.TestSubscriptionInput">
            <summary>
            测试订阅输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TestSubscriptionInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TestSubscriptionInput.TestTopic">
            <summary>
            测试主题（可选）
            </summary>
        </member>
        <member name="T:Admin.NET.Application.TestSubscriptionResult">
            <summary>
            测试订阅结果
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TestSubscriptionResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TestSubscriptionResult.Message">
            <summary>
            消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TestSubscriptionResult.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TestSubscriptionResult.TestTopic">
            <summary>
            测试主题
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TestSubscriptionResult.TestTime">
            <summary>
            测试时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.SubscriptionTemplate">
            <summary>
            订阅配置模板
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionTemplate.Name">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionTemplate.Description">
            <summary>
            模板描述
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionTemplate.Topics">
            <summary>
            主题列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionTemplate.SubscribeToAllTopics">
            <summary>
            是否订阅所有主题
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionTemplate.IncludeHistory">
            <summary>
            是否包含历史消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionTemplate.MaxHistoryCount">
            <summary>
            最大历史消息数量
            </summary>
        </member>
        <member name="T:Admin.NET.Application.EmqxRealtimeHub">
            <summary>
            EMQX实时消息推送Hub
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeHub.OnConnectedAsync">
            <summary>
            客户端连接时
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeHub.OnDisconnectedAsync(System.Exception)">
            <summary>
            客户端断开连接时
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeHub.SubscribeToInstance(System.Int64)">
            <summary>
            订阅MQTT实例的实时消息
            </summary>
            <param name="instanceId">MQTT实例ID</param>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeHub.UnsubscribeFromInstance(System.Int64)">
            <summary>
            取消订阅MQTT实例的实时消息
            </summary>
            <param name="instanceId">MQTT实例ID</param>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeHub.GetSubscribedInstances">
            <summary>
            获取当前订阅的实例列表
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeHub.BroadcastMessage(System.Int64,System.String,System.String)">
            <summary>
            广播MQTT消息到订阅的客户端
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeHub.BroadcastConnectionStatus(System.Int64,System.Boolean)">
            <summary>
            广播连接状态变化到订阅的客户端
            </summary>
        </member>
        <member name="T:Admin.NET.Application.IEmqxRealtimeHubService">
            <summary>
            EMQX实时消息推送服务
            </summary>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeHubService.BroadcastMqttMessageAsync(System.Int64,System.String,System.String)">
            <summary>
            推送MQTT消息到所有订阅的客户端
            </summary>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeHubService.BroadcastConnectionStatusAsync(System.Int64,System.Boolean)">
            <summary>
            推送连接状态变化到所有订阅的客户端
            </summary>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeHubService.BroadcastSystemNotificationAsync(System.String,System.String)">
            <summary>
            推送系统通知到所有客户端
            </summary>
        </member>
        <member name="T:Admin.NET.Application.EmqxRealtimeHubService">
            <summary>
            EMQX实时消息推送服务实现
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeHubService.BroadcastMqttMessageAsync(System.Int64,System.String,System.String)">
            <summary>
            推送MQTT消息到所有订阅的客户端
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeHubService.BroadcastConnectionStatusAsync(System.Int64,System.Boolean)">
            <summary>
            推送连接状态变化到所有订阅的客户端
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeHubService.BroadcastSystemNotificationAsync(System.String,System.String)">
            <summary>
            推送系统通知到所有客户端
            </summary>
        </member>
        <member name="T:Admin.NET.Application.RealtimeSubscriptionHub">
            <summary>
            实时订阅Hub - 专门用于前端实时数据订阅
            </summary>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionHub.OnConnectedAsync">
            <summary>
            客户端连接时
            </summary>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionHub.OnDisconnectedAsync(System.Exception)">
            <summary>
            客户端断开连接时
            </summary>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionHub.Subscribe(Admin.NET.Application.RealtimeSubscriptionRequest)">
            <summary>
            订阅实时数据
            </summary>
            <param name="subscriptionRequest">订阅请求</param>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionHub.Unsubscribe">
            <summary>
            取消订阅
            </summary>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionHub.GetSubscriptionInfo">
            <summary>
            获取当前订阅信息
            </summary>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionHub.GetStatistics">
            <summary>
            获取实时统计信息
            </summary>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionHub.Ping">
            <summary>
            心跳检测
            </summary>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionHub.UpdateSubscription(Admin.NET.Application.RealtimeSubscriptionRequest)">
            <summary>
            更新订阅配置
            </summary>
            <param name="subscriptionRequest">新的订阅请求</param>
        </member>
        <member name="T:Admin.NET.Application.IEmqxRealtimeService">
            <summary>
            EMQX实时数据交换服务接口
            </summary>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.ConnectAsync(System.Int64)">
            <summary>
            连接到EMQX服务器
            </summary>
            <param name="instanceId">MQTT实例ID</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.DisconnectAsync(System.Int64)">
            <summary>
            断开EMQX连接
            </summary>
            <param name="instanceId">MQTT实例ID</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.PublishAsync(System.Int64,System.String,System.String,System.Int32,System.Boolean)">
            <summary>
            发布消息到EMQX
            </summary>
            <param name="instanceId">MQTT实例ID</param>
            <param name="topic">主题</param>
            <param name="payload">消息内容</param>
            <param name="qos">QoS等级</param>
            <param name="retain">是否保留消息</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.SubscribeAsync(System.Int64,System.String,System.Int32)">
            <summary>
            订阅EMQX主题
            </summary>
            <param name="instanceId">MQTT实例ID</param>
            <param name="topic">主题</param>
            <param name="qos">QoS等级</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.UnsubscribeAsync(System.Int64,System.String)">
            <summary>
            取消订阅EMQX主题
            </summary>
            <param name="instanceId">MQTT实例ID</param>
            <param name="topic">主题</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.IsConnectedAsync(System.Int64)">
            <summary>
            获取连接状态
            </summary>
            <param name="instanceId">MQTT实例ID</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.GetActiveConnectionsAsync">
            <summary>
            获取所有活跃连接
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.AutoGroupDeviceByClientId(System.String,System.Int64)">
            <summary>
            根据ClientId自动分组设备
            </summary>
            <param name="clientId">客户端ID</param>
            <param name="instanceId">实例ID</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.GetDeviceGroupByClientId(System.String,System.Int64)">
            <summary>
            获取设备组信息
            </summary>
            <param name="clientId">客户端ID</param>
            <param name="instanceId">实例ID</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.GenerateStandardClientId(System.String,System.String,System.String,System.String)">
            <summary>
            生成标准ClientId格式
            </summary>
            <param name="instancePrefix"></param>
            <param name="deviceType"></param>
            <param name="location"></param>
            <param name="uniqueId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.ValidateClientIdFormat(System.String)">
            <summary>
            验证ClientId格式
            </summary>
            <param name="clientId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.GetDeviceGroupRealtimeStats(System.Int64)">
            <summary>
            获取设备组实时统计
            </summary>
            <param name="instanceId">实例ID</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.GetActiveConnectionDetails(System.Int64)">
            <summary>
            获取指定实例的活跃连接详情
            </summary>
            <param name="instanceId">实例ID</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.BatchAutoGroupDevices(System.Int64)">
            <summary>
            批量自动分组设备
            </summary>
            <param name="instanceId">实例ID</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.GetClientIdSuggestions(System.String,System.String)">
            <summary>
            获取ClientId建议
            </summary>
            <param name="deviceType">设备类型</param>
            <param name="location">设备位置</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.SetMessageReceivedCallback(System.Int64,System.Func{System.String,System.String,System.Threading.Tasks.Task})">
            <summary>
            设置消息接收回调
            </summary>
            <param name="instanceId">MQTT实例ID</param>
            <param name="callback">回调函数</param>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.SetConnectionStatusCallback(System.Int64,System.Func{System.Boolean,System.Threading.Tasks.Task})">
            <summary>
            设置连接状态变化回调
            </summary>
            <param name="instanceId">MQTT实例ID</param>
            <param name="callback">回调函数</param>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.GenerateAliyunClientId(System.String,System.String,System.Int32,System.String)">
            <summary>
            生成符合阿里云标准的ClientId
            </summary>
            <param name="productKey">产品密钥</param>
            <param name="deviceName">设备名称</param>
            <param name="secureMode">安全模式：2=TLS直连，3=TCP直连</param>
            <param name="signMethod">签名方法：hmacsha1, hmacsha256, hmacmd5</param>
            <returns>符合阿里云标准的ClientId</returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.GenerateDeviceName(System.String,System.String,System.String)">
            <summary>
            生成设备名称（按照设备类型_位置_唯一标识格式）
            </summary>
            <param name="deviceType">设备类型</param>
            <param name="location">位置</param>
            <param name="uniqueId">唯一标识</param>
            <returns>设备名称</returns>
        </member>
        <member name="M:Admin.NET.Application.IEmqxRealtimeService.ParseAliyunClientId(System.String)">
            <summary>
            解析阿里云ClientId获取设备信息
            </summary>
            <param name="clientId">ClientId</param>
            <returns>设备信息</returns>
        </member>
        <member name="T:Admin.NET.Application.IRealtimeSubscriptionService">
            <summary>
            实时订阅服务接口
            </summary>
        </member>
        <member name="M:Admin.NET.Application.IRealtimeSubscriptionService.SubscribeAsync(System.String,Admin.NET.Application.RealtimeSubscriptionRequest)">
            <summary>
            订阅实时数据
            </summary>
            <param name="connectionId">连接ID</param>
            <param name="subscriptionRequest">订阅请求</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IRealtimeSubscriptionService.UnsubscribeAsync(System.String)">
            <summary>
            取消订阅
            </summary>
            <param name="connectionId">连接ID</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IRealtimeSubscriptionService.GetSubscriptionInfoAsync(System.String)">
            <summary>
            获取订阅信息
            </summary>
            <param name="connectionId">连接ID</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IRealtimeSubscriptionService.GetStatisticsAsync">
            <summary>
            获取实时统计信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.IRealtimeSubscriptionService.CleanupConnectionAsync(System.String)">
            <summary>
            清理断开的连接
            </summary>
            <param name="connectionId">连接ID</param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Application.RealtimeSubscriptionRequest">
            <summary>
            实时订阅请求
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeSubscriptionRequest.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeSubscriptionRequest.Topics">
            <summary>
            订阅的主题列表（如果为空且SubscribeToAllTopics为true，则订阅所有主题）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeSubscriptionRequest.SubscribeToAllTopics">
            <summary>
            是否订阅所有主题
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeSubscriptionRequest.IncludeHistory">
            <summary>
            是否包含历史消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeSubscriptionRequest.MaxHistoryCount">
            <summary>
            最大历史消息数量
            </summary>
        </member>
        <member name="T:Admin.NET.Application.RealtimeSubscriptionInfo">
            <summary>
            实时订阅信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeSubscriptionInfo.ConnectionId">
            <summary>
            连接ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeSubscriptionInfo.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeSubscriptionInfo.Topics">
            <summary>
            订阅的主题集合
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeSubscriptionInfo.SubscribeToAllTopics">
            <summary>
            是否订阅所有主题
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeSubscriptionInfo.IncludeHistory">
            <summary>
            是否包含历史消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeSubscriptionInfo.MaxHistoryCount">
            <summary>
            最大历史消息数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeSubscriptionInfo.CreatedTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.RealtimeMessage">
            <summary>
            实时消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeMessage.MessageId">
            <summary>
            消息ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeMessage.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeMessage.Payload">
            <summary>
            消息内容
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeMessage.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeMessage.MessageType">
            <summary>
            消息类型
            </summary>
        </member>
        <member name="T:Admin.NET.Application.RealtimeStatistics">
            <summary>
            实时统计信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeStatistics.TotalConnections">
            <summary>
            总连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeStatistics.ActiveInstances">
            <summary>
            活跃的MQTT实例列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeStatistics.ActiveTopics">
            <summary>
            活跃的主题列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeStatistics.CachedMessageCount">
            <summary>
            缓存的消息数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RealtimeStatistics.Timestamp">
            <summary>
            统计时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.EmqxRealtimeConfig">
            <summary>
            EMQX实时数据交换配置 🔧
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.DefaultConnectionTimeout">
            <summary>
            默认连接超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.DefaultKeepAliveInterval">
            <summary>
            默认保活间隔（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.DefaultReconnectInterval">
            <summary>
            默认重连间隔（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.MaxReconnectAttempts">
            <summary>
            最大重连次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.MessageHistoryRetentionDays">
            <summary>
            消息历史记录保留天数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.EnableMessageHistory">
            <summary>
            是否启用消息历史记录
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.EnableAutoReconnect">
            <summary>
            是否启用自动重连
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.EnableSsl">
            <summary>
            是否启用SSL/TLS
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.MaxConcurrentConnections">
            <summary>
            最大并发连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.MaxMessageQueueLength">
            <summary>
            消息队列最大长度
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.BatchProcessMessageCount">
            <summary>
            批量处理消息数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.StatisticsRefreshInterval">
            <summary>
            统计信息刷新间隔（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.EnableMessageCompression">
            <summary>
            是否启用消息压缩
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.DefaultQosLevel">
            <summary>
            默认QoS等级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.EnableMessageDeduplication">
            <summary>
            是否启用消息去重
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.MessageDeduplicationWindow">
            <summary>
            消息去重窗口时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.EnableTopicFilter">
            <summary>
            是否启用主题过滤
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.AllowedTopicPatterns">
            <summary>
            允许的主题模式列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.DeniedTopicPatterns">
            <summary>
            禁止的主题模式列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.EnableClientAuthentication">
            <summary>
            是否启用客户端认证
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.DefaultClientIdPrefix">
            <summary>
            默认客户端ID前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.EnableWillMessage">
            <summary>
            是否启用遗嘱消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.DefaultWillTopic">
            <summary>
            默认遗嘱主题
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.DefaultWillMessage">
            <summary>
            默认遗嘱消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.EnablePerformanceMonitoring">
            <summary>
            是否启用性能监控
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.PerformanceMonitoringSampleInterval">
            <summary>
            性能监控采样间隔（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.EnableLogging">
            <summary>
            是否启用日志记录
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.LogLevel">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.EnableDebugMode">
            <summary>
            是否启用调试模式
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.ConnectionPool">
            <summary>
            连接池配置
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.Cache">
            <summary>
            缓存配置
            </summary>
        </member>
        <member name="P:Admin.NET.Application.EmqxRealtimeConfig.Security">
            <summary>
            安全配置
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeConfig.Validate">
            <summary>
            验证配置
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Application.ConnectionPoolConfig">
            <summary>
            连接池配置
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ConnectionPoolConfig.MinConnections">
            <summary>
            最小连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ConnectionPoolConfig.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ConnectionPoolConfig.IdleTimeout">
            <summary>
            连接空闲超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ConnectionPoolConfig.AcquireTimeout">
            <summary>
            连接获取超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ConnectionPoolConfig.Enabled">
            <summary>
            是否启用连接池
            </summary>
        </member>
        <member name="M:Admin.NET.Application.ConnectionPoolConfig.Validate">
            <summary>
            验证配置
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Application.CacheConfig">
            <summary>
            缓存配置
            </summary>
        </member>
        <member name="P:Admin.NET.Application.CacheConfig.Enabled">
            <summary>
            是否启用缓存
            </summary>
        </member>
        <member name="P:Admin.NET.Application.CacheConfig.ExpirationTime">
            <summary>
            缓存过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.CacheConfig.MaxCacheEntries">
            <summary>
            最大缓存条目数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.CacheConfig.CleanupInterval">
            <summary>
            缓存清理间隔（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.CacheConfig.EnableDistributedCache">
            <summary>
            是否启用分布式缓存
            </summary>
        </member>
        <member name="P:Admin.NET.Application.CacheConfig.RedisConnectionString">
            <summary>
            Redis连接字符串
            </summary>
        </member>
        <member name="M:Admin.NET.Application.CacheConfig.Validate">
            <summary>
            验证配置
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Application.SecurityConfig">
            <summary>
            安全配置
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SecurityConfig.EnableIpWhitelist">
            <summary>
            是否启用IP白名单
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SecurityConfig.IpWhitelist">
            <summary>
            IP白名单
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SecurityConfig.EnableIpBlacklist">
            <summary>
            是否启用IP黑名单
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SecurityConfig.IpBlacklist">
            <summary>
            IP黑名单
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SecurityConfig.EnableRateLimit">
            <summary>
            是否启用速率限制
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SecurityConfig.MaxRequestsPerMinute">
            <summary>
            每分钟最大请求数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SecurityConfig.EnableMessageEncryption">
            <summary>
            是否启用消息加密
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SecurityConfig.EncryptionKey">
            <summary>
            加密密钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SecurityConfig.EnableMessageSigning">
            <summary>
            是否启用消息签名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SecurityConfig.SigningKey">
            <summary>
            签名密钥
            </summary>
        </member>
        <member name="M:Admin.NET.Application.SecurityConfig.Validate">
            <summary>
            验证配置
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Application.ValidationResult">
            <summary>
            验证结果
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ValidationResult.IsValid">
            <summary>
            是否有效
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ValidationResult.Errors">
            <summary>
            错误信息列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ValidationResult.ErrorMessage">
            <summary>
            错误信息字符串
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttConnectionConfig">
            <summary>
            MQTT连接配置
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.Server">
            <summary>
            服务器地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.Port">
            <summary>
            端口号
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.UseSsl">
            <summary>
            是否使用SSL/TLS
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.KeepAliveInterval">
            <summary>
            保活间隔（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.CleanSession">
            <summary>
            是否清除会话
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.AutoReconnect">
            <summary>
            是否启用自动重连
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.ReconnectInterval">
            <summary>
            重连间隔（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.MaxReconnectAttempts">
            <summary>
            最大重连次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.WillTopic">
            <summary>
            遗嘱主题
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.WillMessage">
            <summary>
            遗嘱消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.WillQosLevel">
            <summary>
            遗嘱QoS等级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.WillRetain">
            <summary>
            遗嘱保留标志
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttConnectionConfig.ExtendedProperties">
            <summary>
            扩展属性
            </summary>
        </member>
        <member name="M:Admin.NET.Application.MqttConnectionConfig.Validate">
            <summary>
            验证配置
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttConnectionConfig.GenerateClientId(System.String)">
            <summary>
            生成客户端ID
            </summary>
            <param name="prefix"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttConnectionConfig.GetConnectionString">
            <summary>
            获取连接字符串
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Application.EmqxRealtimeService">
            <summary>
            EMQX实时数据交换服务实现
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.ConnectAsync(System.Int64)">
            <summary>
            连接到EMQX服务器
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.DisconnectAsync(System.Int64)">
            <summary>
            断开EMQX连接
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.PublishAsync(System.Int64,System.String,System.String,System.Int32,System.Boolean)">
            <summary>
            发布消息到EMQX
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.SubscribeAsync(System.Int64,System.String,System.Int32)">
            <summary>
            订阅EMQX主题
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.UnsubscribeAsync(System.Int64,System.String)">
            <summary>
            取消订阅EMQX主题
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.IsConnectedAsync(System.Int64)">
            <summary>
            获取连接状态
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.GetActiveConnectionsAsync">
            <summary>
            获取所有活跃连接
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.GetActiveConnections">
            <summary>
            获取活跃连接列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.AutoGroupDeviceByClientId(System.String,System.Int64)">
            <summary>
            根据ClientId自动分组设备
            </summary>
            <param name="clientId"></param>
            <param name="instanceId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.GetDeviceGroupByClientId(System.String,System.Int64)">
            <summary>
            根据ClientId获取设备组
            </summary>
            <param name="clientId"></param>
            <param name="instanceId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.GetDeviceGroupByClientId(System.String)">
            <summary>
            根据ClientId获取设备组（私有方法）
            </summary>
            <param name="clientId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.ExtractDeviceTypeFromClientId(System.String)">
            <summary>
            从ClientId提取设备类型
            </summary>
            <param name="clientId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.ExtractDeviceLocationFromClientId(System.String)">
            <summary>
            从ClientId提取设备位置
            </summary>
            <param name="clientId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.UpdateDeviceGroupStatistics(System.Int64)">
            <summary>
            更新设备组统计信息
            </summary>
            <param name="groupId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.GenerateStandardClientId(System.String,System.String,System.String,System.String)">
            <summary>
            生成标准ClientId格式
            </summary>
            <param name="instancePrefix"></param>
            <param name="deviceType"></param>
            <param name="location"></param>
            <param name="uniqueId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.ValidateClientIdFormat(System.String)">
            <summary>
            验证ClientId格式
            </summary>
            <param name="clientId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.GetDeviceGroupRealtimeStats(System.Int64)">
            <summary>
            获取设备组实时统计信息
            </summary>
            <param name="instanceId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.GetActiveConnectionDetails(System.Int64)">
            <summary>
            获取指定实例的活跃连接详情
            </summary>
            <param name="instanceId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.BatchAutoGroupDevices(System.Int64)">
            <summary>
            批量自动分组设备
            </summary>
            <param name="instanceId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.GetClientIdSuggestions(System.String,System.String)">
            <summary>
            获取ClientId建议
            </summary>
            <param name="deviceType"></param>
            <param name="location"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.SetMessageReceivedCallback(System.Int64,System.Func{System.String,System.String,System.Threading.Tasks.Task})">
            <summary>
            设置消息接收回调
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.SetConnectionStatusCallback(System.Int64,System.Func{System.Boolean,System.Threading.Tasks.Task})">
            <summary>
            设置连接状态变化回调
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.AutoSubscribeTopics(System.Int64,MQTTnet.Extensions.ManagedClient.IManagedMqttClient)">
            <summary>
            自动订阅主题
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.UpdateInstanceStatus(System.Int64,System.String)">
            <summary>
            更新实例状态
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.UpdateSubscriptionStats(System.Int64,System.String)">
            <summary>
            更新订阅统计
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.RecordSubscription(System.Int64,System.String,System.Int32)">
            <summary>
            记录订阅关系
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.RemoveSubscription(System.Int64,System.String)">
            <summary>
            移除订阅关系
            </summary>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.GenerateAliyunClientId(System.String,System.String,System.Int32,System.String)">
            <summary>
            生成符合阿里云标准的ClientId
            </summary>
            <param name="productKey">产品密钥</param>
            <param name="deviceName">设备名称</param>
            <param name="secureMode">安全模式：2=TLS直连，3=TCP直连</param>
            <param name="signMethod">签名方法：hmacsha1, hmacsha256, hmacmd5</param>
            <returns>符合阿里云标准的ClientId</returns>
            <example>
            使用示例：
            var deviceName = GenerateDeviceName("SENSOR", "WORKSHOP1", "001");
            var clientId = GenerateAliyunClientId("AdminNET", deviceName);
            结果：AdminNET.SENSOR_WORKSHOP1_001|securemode=3,signmethod=hmacsha1|
            </example>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.GenerateDeviceName(System.String,System.String,System.String)">
            <summary>
            生成设备名称（按照设备类型_位置_唯一标识格式）
            </summary>
            <param name="deviceType">设备类型</param>
            <param name="location">位置</param>
            <param name="uniqueId">唯一标识</param>
            <returns>设备名称</returns>
        </member>
        <member name="M:Admin.NET.Application.EmqxRealtimeService.ParseAliyunClientId(System.String)">
            <summary>
            解析阿里云ClientId获取设备信息
            </summary>
            <param name="clientId">ClientId</param>
            <returns>设备信息</returns>
        </member>
        <member name="T:Admin.NET.Application.MqttMessageHistoryService">
            <summary>
            MQTT消息历史记录服务 🧩
            </summary>
        </member>
        <member name="M:Admin.NET.Application.MqttMessageHistoryService.Page(Admin.NET.Application.PageMqttMessageHistoryInput)">
            <summary>
            分页查询MQTT消息历史记录 🔖
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttMessageHistoryService.Detail(Admin.NET.Application.QueryByIdMqttMessageHistoryInput)">
            <summary>
            获取MQTT消息历史记录详情 ℹ️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttMessageHistoryService.Record(Admin.NET.Application.RecordMqttMessageInput)">
            <summary>
            记录MQTT消息 ➕
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttMessageHistoryService.BatchRecord(Admin.NET.Application.BatchRecordMqttMessageInput)">
            <summary>
            批量记录MQTT消息 ➕
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttMessageHistoryService.UpdateProcessStatus(Admin.NET.Application.UpdateMessageProcessStatusInput)">
            <summary>
            更新消息处理状态 ✏️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttMessageHistoryService.Delete(Admin.NET.Application.DeleteMqttMessageHistoryInput)">
            <summary>
            删除MQTT消息历史记录 🗑️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttMessageHistoryService.BatchDelete(Admin.NET.Application.BatchDeleteMqttMessageHistoryInput)">
            <summary>
            批量删除MQTT消息历史记录 🗑️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttMessageHistoryService.CleanHistory(Admin.NET.Application.CleanHistoryInput)">
            <summary>
            清理历史消息 🧹
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttMessageHistoryService.GetStatistics(Admin.NET.Application.GetMessageStatisticsInput)">
            <summary>
            获取消息统计信息 📊
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Application.PageMqttMessageHistoryInput">
            <summary>
            分页查询MQTT消息历史记录输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttMessageHistoryInput.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttMessageHistoryInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttMessageHistoryInput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttMessageHistoryInput.Direction">
            <summary>
            消息方向
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttMessageHistoryInput.ProcessStatus">
            <summary>
            处理状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttMessageHistoryInput.QosLevel">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttMessageHistoryInput.IsRetained">
            <summary>
            是否保留消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttMessageHistoryInput.ReceivedAtStart">
            <summary>
            接收时间开始
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttMessageHistoryInput.ReceivedAtEnd">
            <summary>
            接收时间结束
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttMessageHistoryInput.ProcessedAtStart">
            <summary>
            处理时间开始
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttMessageHistoryInput.ProcessedAtEnd">
            <summary>
            处理时间结束
            </summary>
        </member>
        <member name="T:Admin.NET.Application.QueryByIdMqttMessageHistoryInput">
            <summary>
            查询MQTT消息历史记录详情输入
            </summary>
        </member>
        <member name="T:Admin.NET.Application.RecordMqttMessageInput">
            <summary>
            记录MQTT消息输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RecordMqttMessageInput.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RecordMqttMessageInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RecordMqttMessageInput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RecordMqttMessageInput.Payload">
            <summary>
            消息内容
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RecordMqttMessageInput.QosLevel">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RecordMqttMessageInput.IsRetained">
            <summary>
            是否保留消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RecordMqttMessageInput.Direction">
            <summary>
            消息方向
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RecordMqttMessageInput.SenderIpAddress">
            <summary>
            发送者IP地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RecordMqttMessageInput.MessageTags">
            <summary>
            消息标签
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RecordMqttMessageInput.ExtendedProperties">
            <summary>
            扩展属性
            </summary>
        </member>
        <member name="P:Admin.NET.Application.RecordMqttMessageInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.BatchRecordMqttMessageInput">
            <summary>
            批量记录MQTT消息输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchRecordMqttMessageInput.Messages">
            <summary>
            消息列表
            </summary>
        </member>
        <member name="T:Admin.NET.Application.UpdateMessageProcessStatusInput">
            <summary>
            更新消息处理状态输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.UpdateMessageProcessStatusInput.Id">
            <summary>
            消息ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.UpdateMessageProcessStatusInput.ProcessStatus">
            <summary>
            处理状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.UpdateMessageProcessStatusInput.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.UpdateMessageProcessStatusInput.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="T:Admin.NET.Application.DeleteMqttMessageHistoryInput">
            <summary>
            删除MQTT消息历史记录输入
            </summary>
        </member>
        <member name="T:Admin.NET.Application.BatchDeleteMqttMessageHistoryInput">
            <summary>
            批量删除MQTT消息历史记录输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchDeleteMqttMessageHistoryInput.Ids">
            <summary>
            ID列表
            </summary>
        </member>
        <member name="T:Admin.NET.Application.CleanHistoryInput">
            <summary>
            清理历史消息输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.CleanHistoryInput.InstanceId">
            <summary>
            MQTT实例ID（可选，为0表示所有实例）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.CleanHistoryInput.BeforeDate">
            <summary>
            删除此日期之前的消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.CleanHistoryInput.DaysToKeep">
            <summary>
            保留天数（与BeforeDate二选一）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.CleanHistoryInput.ProcessStatus">
            <summary>
            处理状态过滤
            </summary>
        </member>
        <member name="T:Admin.NET.Application.GetMessageStatisticsInput">
            <summary>
            获取消息统计信息输入
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetMessageStatisticsInput.InstanceId">
            <summary>
            MQTT实例ID（可选）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetMessageStatisticsInput.StartDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetMessageStatisticsInput.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttMessageHistoryOutput">
            <summary>
            MQTT消息历史记录输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageHistoryOutput.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageHistoryOutput.InstanceId">
            <summary>
            MQTT实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageHistoryOutput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageHistoryOutput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageHistoryOutput.Payload">
            <summary>
            消息内容
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageHistoryOutput.QosLevel">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageHistoryOutput.IsRetained">
            <summary>
            是否保留消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageHistoryOutput.Direction">
            <summary>
            消息方向
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageHistoryOutput.MessageSize">
            <summary>
            消息大小
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageHistoryOutput.SenderIpAddress">
            <summary>
            发送者IP地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageHistoryOutput.ReceivedAt">
            <summary>
            接收时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageHistoryOutput.ProcessStatus">
            <summary>
            处理状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageHistoryOutput.ProcessedAt">
            <summary>
            处理时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageHistoryOutput.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageHistoryOutput.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageHistoryOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.BatchRecordMqttMessageOutput">
            <summary>
            批量记录MQTT消息输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchRecordMqttMessageOutput.Results">
            <summary>
            记录结果列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchRecordMqttMessageOutput.TotalCount">
            <summary>
            总数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchRecordMqttMessageOutput.SuccessCount">
            <summary>
            成功数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchRecordMqttMessageOutput.FailureCount">
            <summary>
            失败数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchRecordMqttMessageOutput.RecordTime">
            <summary>
            记录时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttMessageRecordResult">
            <summary>
            MQTT消息记录结果
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageRecordResult.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageRecordResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageRecordResult.Message">
            <summary>
            消息
            </summary>
        </member>
        <member name="T:Admin.NET.Application.CleanHistoryOutput">
            <summary>
            清理历史消息输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.CleanHistoryOutput.DeletedCount">
            <summary>
            删除数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.CleanHistoryOutput.CleanTime">
            <summary>
            清理时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttMessageStatisticsOutput">
            <summary>
            MQTT消息统计信息输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageStatisticsOutput.TotalCount">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageStatisticsOutput.PublishCount">
            <summary>
            发布消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageStatisticsOutput.SubscribeCount">
            <summary>
            订阅消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageStatisticsOutput.ProcessedCount">
            <summary>
            已处理消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageStatisticsOutput.FailedCount">
            <summary>
            失败消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageStatisticsOutput.PendingCount">
            <summary>
            待处理消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageStatisticsOutput.TotalSize">
            <summary>
            总消息大小
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageStatisticsOutput.AverageSize">
            <summary>
            平均消息大小
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageStatisticsOutput.TopTopics">
            <summary>
            热门主题
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttMessageStatisticsOutput.StatisticsTime">
            <summary>
            统计时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.TopicStatistics">
            <summary>
            主题统计信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicStatistics.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicStatistics.MessageCount">
            <summary>
            消息数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicStatistics.TotalSize">
            <summary>
            总大小
            </summary>
        </member>
        <member name="T:Admin.NET.Application.RealtimeSubscriptionService">
            <summary>
            实时订阅服务器 - 为前端提供实时数据订阅服务
            </summary>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionService.SubscribeAsync(System.String,Admin.NET.Application.RealtimeSubscriptionRequest)">
            <summary>
            订阅实时数据
            </summary>
            <param name="connectionId">连接ID</param>
            <param name="subscriptionRequest">订阅请求</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionService.UnsubscribeAsync(System.String)">
            <summary>
            取消订阅
            </summary>
            <param name="connectionId">连接ID</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionService.GetSubscriptionInfoAsync(System.String)">
            <summary>
            获取订阅信息
            </summary>
            <param name="connectionId">连接ID</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionService.GetStatisticsAsync">
            <summary>
            获取实时统计信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionService.OnMqttMessageReceived(System.String,System.String)">
            <summary>
            MQTT消息接收处理
            </summary>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionService.CacheMessage(System.String,Admin.NET.Application.RealtimeMessage)">
            <summary>
            缓存消息
            </summary>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionService.BroadcastMessage(Admin.NET.Application.RealtimeMessage)">
            <summary>
            广播消息
            </summary>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionService.SendHistoryMessages(System.String,Admin.NET.Application.RealtimeSubscriptionInfo)">
            <summary>
            发送历史消息
            </summary>
        </member>
        <member name="M:Admin.NET.Application.RealtimeSubscriptionService.CleanupConnectionAsync(System.String)">
            <summary>
            清理断开的连接
            </summary>
            <param name="connectionId">连接ID</param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Application.MqttClientAuthDto">
            <summary>
            MQTT 客户端认证信息表输出参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.ClientRefId">
            <summary>
            客户端引用ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.AuthMode">
            <summary>
            认证模式（Username、AliyunSignature、JWT等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.PasswordHash">
            <summary>
            密码哈希
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.PasswordSalt">
            <summary>
            密码盐值
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.AccessKeyId">
            <summary>
            AccessKey ID（阿里云认证）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.AccessKeySecret">
            <summary>
            AccessKey Secret（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.SignMethod">
            <summary>
            签名算法（hmacmd5、hmacsha1、hmacsha256）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.JwtSecret">
            <summary>
            JWT密钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.JwtExpiry">
            <summary>
            JWT过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.JwtToken">
            <summary>
            JWT令牌
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.Certificate">
            <summary>
            客户端证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.PrivateKey">
            <summary>
            私钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.CaCertificate">
            <summary>
            CA证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.AllowedIPs">
            <summary>
            允许的IP地址列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.DeniedIPs">
            <summary>
            拒绝的IP地址列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.RateLimit">
            <summary>
            速率限制（消息/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.LoginAttempts">
            <summary>
            登录尝试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.ExpireTime">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.LastAuthTime">
            <summary>
            最后认证时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.LastLoginTime">
            <summary>
            最后登录时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.LastLoginIP">
            <summary>
            最后登录IP
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.SuccessCount">
            <summary>
            认证成功次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.AuthSuccessCount">
            <summary>
            认证成功总数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.FailedCount">
            <summary>
            认证失败次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.IsLocked">
            <summary>
            是否锁定（0:未锁定，1:已锁定）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.LockTime">
            <summary>
            锁定时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.UnlockTime">
            <summary>
            解锁时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.LockReason">
            <summary>
            锁定原因
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.MaxFailedCount">
            <summary>
            最大失败次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.LockDuration">
            <summary>
            锁定持续时间（分钟）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.IpWhitelist">
            <summary>
            IP白名单
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.IpBlacklist">
            <summary>
            IP黑名单
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.AllowedTopics">
            <summary>
            允许的主题列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.DeniedTopics">
            <summary>
            拒绝的主题列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.CurrentConnections">
            <summary>
            当前连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.MaxSubscriptions">
            <summary>
            最大订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.MessageRateLimit">
            <summary>
            消息速率限制
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.ByteRateLimit">
            <summary>
            字节速率限制
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.Key">
            <summary>
            
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.CreateUserId">
            <summary>
            创建者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.UpdateUserId">
            <summary>
            修改者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.IsDelete">
            <summary>
            软删除
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.CreateUserName">
            <summary>
            创建者姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthDto.UpdateUserName">
            <summary>
            修改者姓名
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttClientAuthBaseInput">
            <summary>
            MQTT 客户端认证信息表基础输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.ClientRefId">
            <summary>
            客户端引用ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.AuthMode">
            <summary>
            认证模式（Username、AliyunSignature、JWT等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.PasswordHash">
            <summary>
            密码哈希
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.PasswordSalt">
            <summary>
            密码盐值
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.AccessKeyId">
            <summary>
            AccessKey ID（阿里云认证）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.AccessKeySecret">
            <summary>
            AccessKey Secret（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.SignMethod">
            <summary>
            签名算法（hmacmd5、hmacsha1、hmacsha256）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.JwtSecret">
            <summary>
            JWT密钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.JwtExpiry">
            <summary>
            JWT过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.JwtToken">
            <summary>
            JWT令牌
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.Certificate">
            <summary>
            客户端证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.PrivateKey">
            <summary>
            私钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.CaCertificate">
            <summary>
            CA证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.AllowedIPs">
            <summary>
            允许的IP地址列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.DeniedIPs">
            <summary>
            拒绝的IP地址列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.RateLimit">
            <summary>
            速率限制（消息/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.LoginAttempts">
            <summary>
            登录尝试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.ExpireTime">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.LastAuthTime">
            <summary>
            最后认证时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.LastLoginTime">
            <summary>
            最后登录时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.LastLoginIP">
            <summary>
            最后登录IP
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.SuccessCount">
            <summary>
            认证成功次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.AuthSuccessCount">
            <summary>
            认证成功总数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.FailedCount">
            <summary>
            认证失败次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.IsLocked">
            <summary>
            是否锁定（0:未锁定，1:已锁定）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.LockTime">
            <summary>
            锁定时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.UnlockTime">
            <summary>
            解锁时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.LockReason">
            <summary>
            锁定原因
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.MaxFailedCount">
            <summary>
            最大失败次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.LockDuration">
            <summary>
            锁定持续时间（分钟）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.IpWhitelist">
            <summary>
            IP白名单
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.IpBlacklist">
            <summary>
            IP黑名单
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.AllowedTopics">
            <summary>
            允许的主题列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.DeniedTopics">
            <summary>
            拒绝的主题列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.CurrentConnections">
            <summary>
            当前连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.MaxSubscriptions">
            <summary>
            最大订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.MessageRateLimit">
            <summary>
            消息速率限制
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.ByteRateLimit">
            <summary>
            字节速率限制
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.Key">
            <summary>
            
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthBaseInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.PageMqttClientAuthInput">
            <summary>
            MQTT 客户端认证信息表分页查询输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.ClientRefId">
            <summary>
            客户端引用ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.AuthMode">
            <summary>
            认证模式（Username、AliyunSignature、JWT等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.PasswordHash">
            <summary>
            密码哈希
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.PasswordSalt">
            <summary>
            密码盐值
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.AccessKeyId">
            <summary>
            AccessKey ID（阿里云认证）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.AccessKeySecret">
            <summary>
            AccessKey Secret（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.SignMethod">
            <summary>
            签名算法（hmacmd5、hmacsha1、hmacsha256）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.JwtSecret">
            <summary>
            JWT密钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.JwtExpiry">
            <summary>
            JWT过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.JwtToken">
            <summary>
            JWT令牌
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.Certificate">
            <summary>
            客户端证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.PrivateKey">
            <summary>
            私钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.CaCertificate">
            <summary>
            CA证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.AllowedIPs">
            <summary>
            允许的IP地址列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.DeniedIPs">
            <summary>
            拒绝的IP地址列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.RateLimit">
            <summary>
            速率限制（消息/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.LoginAttempts">
            <summary>
            登录尝试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.ExpireTimeRange">
            <summary>
            过期时间范围
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.LastAuthTimeRange">
            <summary>
            最后认证时间范围
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.LastLoginTimeRange">
            <summary>
            最后登录时间范围
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.LastLoginIP">
            <summary>
            最后登录IP
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.SuccessCount">
            <summary>
            认证成功次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.AuthSuccessCount">
            <summary>
            认证成功总数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.FailedCount">
            <summary>
            认证失败次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.IsLocked">
            <summary>
            是否锁定（0:未锁定，1:已锁定）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.LockTimeRange">
            <summary>
            锁定时间范围
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.UnlockTimeRange">
            <summary>
            解锁时间范围
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.LockReason">
            <summary>
            锁定原因
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.MaxFailedCount">
            <summary>
            最大失败次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.LockDuration">
            <summary>
            锁定持续时间（分钟）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.IpWhitelist">
            <summary>
            IP白名单
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.IpBlacklist">
            <summary>
            IP黑名单
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.AllowedTopics">
            <summary>
            允许的主题列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.DeniedTopics">
            <summary>
            拒绝的主题列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.CurrentConnections">
            <summary>
            当前连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.MaxSubscriptions">
            <summary>
            最大订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.MessageRateLimit">
            <summary>
            消息速率限制
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.ByteRateLimit">
            <summary>
            字节速率限制
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.Key">
            <summary>
            
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientAuthInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.AddMqttClientAuthInput">
            <summary>
            MQTT 客户端认证信息表增加输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.ClientRefId">
            <summary>
            客户端引用ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.AuthMode">
            <summary>
            认证模式（Username、AliyunSignature、JWT等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.PasswordHash">
            <summary>
            密码哈希
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.PasswordSalt">
            <summary>
            密码盐值
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.AccessKeyId">
            <summary>
            AccessKey ID（阿里云认证）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.AccessKeySecret">
            <summary>
            AccessKey Secret（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.SignMethod">
            <summary>
            签名算法（hmacmd5、hmacsha1、hmacsha256）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.JwtSecret">
            <summary>
            JWT密钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.JwtExpiry">
            <summary>
            JWT过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.JwtToken">
            <summary>
            JWT令牌
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.Certificate">
            <summary>
            客户端证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.PrivateKey">
            <summary>
            私钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.CaCertificate">
            <summary>
            CA证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.AllowedIPs">
            <summary>
            允许的IP地址列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.DeniedIPs">
            <summary>
            拒绝的IP地址列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.RateLimit">
            <summary>
            速率限制（消息/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.LoginAttempts">
            <summary>
            登录尝试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.ExpireTime">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.LastAuthTime">
            <summary>
            最后认证时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.LastLoginTime">
            <summary>
            最后登录时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.LastLoginIP">
            <summary>
            最后登录IP
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.SuccessCount">
            <summary>
            认证成功次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.AuthSuccessCount">
            <summary>
            认证成功总数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.FailedCount">
            <summary>
            认证失败次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.IsLocked">
            <summary>
            是否锁定（0:未锁定，1:已锁定）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.LockTime">
            <summary>
            锁定时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.UnlockTime">
            <summary>
            解锁时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.LockReason">
            <summary>
            锁定原因
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.MaxFailedCount">
            <summary>
            最大失败次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.LockDuration">
            <summary>
            锁定持续时间（分钟）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.IpWhitelist">
            <summary>
            IP白名单
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.IpBlacklist">
            <summary>
            IP黑名单
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.AllowedTopics">
            <summary>
            允许的主题列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.DeniedTopics">
            <summary>
            拒绝的主题列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.CurrentConnections">
            <summary>
            当前连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.MaxSubscriptions">
            <summary>
            最大订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.MessageRateLimit">
            <summary>
            消息速率限制
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.ByteRateLimit">
            <summary>
            字节速率限制
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.Key">
            <summary>
            
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientAuthInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.DeleteMqttClientAuthInput">
            <summary>
            MQTT 客户端认证信息表删除输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeleteMqttClientAuthInput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="T:Admin.NET.Application.UpdateMqttClientAuthInput">
            <summary>
            MQTT 客户端认证信息表更新输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.Id">
            <summary>
            主键Id
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.InstanceId">
            <summary>
            实例ID
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.ClientId">
            <summary>
            客户端ID
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.ClientRefId">
            <summary>
            客户端引用ID
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.AuthMode">
            <summary>
            认证模式（Username、AliyunSignature、JWT等）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.Username">
            <summary>
            用户名
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.Password">
            <summary>
            密码
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.PasswordHash">
            <summary>
            密码哈希
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.PasswordSalt">
            <summary>
            密码盐值
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.AccessKeyId">
            <summary>
            AccessKey ID（阿里云认证）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.AccessKeySecret">
            <summary>
            AccessKey Secret（加密存储）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.SignMethod">
            <summary>
            签名算法（hmacmd5、hmacsha1、hmacsha256）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.JwtSecret">
            <summary>
            JWT密钥
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.JwtExpiry">
            <summary>
            JWT过期时间（秒）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.JwtToken">
            <summary>
            JWT令牌
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.Certificate">
            <summary>
            客户端证书
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.PrivateKey">
            <summary>
            私钥
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.CaCertificate">
            <summary>
            CA证书
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.AllowedIPs">
            <summary>
            允许的IP地址列表
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.DeniedIPs">
            <summary>
            拒绝的IP地址列表
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.RateLimit">
            <summary>
            速率限制（消息/秒）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.LoginAttempts">
            <summary>
            登录尝试次数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.ExpireTime">
            <summary>
            过期时间
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.LastAuthTime">
            <summary>
            最后认证时间
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.LastLoginTime">
            <summary>
            最后登录时间
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.LastLoginIP">
            <summary>
            最后登录IP
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.SuccessCount">
            <summary>
            认证成功次数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.AuthSuccessCount">
            <summary>
            认证成功总数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.FailedCount">
            <summary>
            认证失败次数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.IsLocked">
            <summary>
            是否锁定（0:未锁定，1:已锁定）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.LockTime">
            <summary>
            锁定时间
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.UnlockTime">
            <summary>
            解锁时间
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.LockReason">
            <summary>
            锁定原因
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.MaxFailedCount">
            <summary>
            最大失败次数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.LockDuration">
            <summary>
            锁定持续时间（分钟）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.IpWhitelist">
            <summary>
            IP白名单
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.IpBlacklist">
            <summary>
            IP黑名单
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.AllowedTopics">
            <summary>
            允许的主题列表
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.DeniedTopics">
            <summary>
            拒绝的主题列表
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.MaxConnections">
            <summary>
            最大连接数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.CurrentConnections">
            <summary>
            当前连接数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.MaxSubscriptions">
            <summary>
            最大订阅数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.MessageRateLimit">
            <summary>
            消息速率限制
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.ByteRateLimit">
            <summary>
            字节速率限制
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.Key">
            <summary>
            
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientAuthInput.Remark">
            <summary>
            备注
            </summary>    
        </member>
        <member name="T:Admin.NET.Application.QueryByIdMqttClientAuthInput">
            <summary>
            MQTT 客户端认证信息表主键查询输入参数
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttClientAuthOutput">
            <summary>
            MQTT 客户端认证信息表输出参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.ClientRefId">
            <summary>
            客户端引用ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.AuthMode">
            <summary>
            认证模式（Username、AliyunSignature、JWT等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.PasswordHash">
            <summary>
            密码哈希
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.PasswordSalt">
            <summary>
            密码盐值
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.AccessKeyId">
            <summary>
            AccessKey ID（阿里云认证）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.AccessKeySecret">
            <summary>
            AccessKey Secret（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.SignMethod">
            <summary>
            签名算法（hmacmd5、hmacsha1、hmacsha256）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.JwtSecret">
            <summary>
            JWT密钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.JwtExpiry">
            <summary>
            JWT过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.JwtToken">
            <summary>
            JWT令牌
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.Certificate">
            <summary>
            客户端证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.PrivateKey">
            <summary>
            私钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.CaCertificate">
            <summary>
            CA证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.AllowedIPs">
            <summary>
            允许的IP地址列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.DeniedIPs">
            <summary>
            拒绝的IP地址列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.RateLimit">
            <summary>
            速率限制（消息/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.LoginAttempts">
            <summary>
            登录尝试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.ExpireTime">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.LastAuthTime">
            <summary>
            最后认证时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.LastLoginTime">
            <summary>
            最后登录时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.LastLoginIP">
            <summary>
            最后登录IP
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.SuccessCount">
            <summary>
            认证成功次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.AuthSuccessCount">
            <summary>
            认证成功总数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.FailedCount">
            <summary>
            认证失败次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.IsLocked">
            <summary>
            是否锁定（0:未锁定，1:已锁定）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.LockTime">
            <summary>
            锁定时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.UnlockTime">
            <summary>
            解锁时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.LockReason">
            <summary>
            锁定原因
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.MaxFailedCount">
            <summary>
            最大失败次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.LockDuration">
            <summary>
            锁定持续时间（分钟）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.IpWhitelist">
            <summary>
            IP白名单
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.IpBlacklist">
            <summary>
            IP黑名单
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.AllowedTopics">
            <summary>
            允许的主题列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.DeniedTopics">
            <summary>
            拒绝的主题列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.CurrentConnections">
            <summary>
            当前连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.MaxSubscriptions">
            <summary>
            最大订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.MessageRateLimit">
            <summary>
            消息速率限制
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.ByteRateLimit">
            <summary>
            字节速率限制
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.Key">
            <summary>
            
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.CreateUserId">
            <summary>
            创建者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.UpdateUserId">
            <summary>
            修改者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.IsDelete">
            <summary>
            软删除
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.CreateUserName">
            <summary>
            创建者姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientAuthOutput.UpdateUserName">
            <summary>
            修改者姓名
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttClientAuthService">
            <summary>
            MQTT 客户端认证信息表服务 🧩
            </summary>
        </member>
        <member name="M:Admin.NET.Application.MqttClientAuthService.Page(Admin.NET.Application.PageMqttClientAuthInput)">
            <summary>
            分页查询MQTT 客户端认证信息表 🔖
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttClientAuthService.Detail(Admin.NET.Application.QueryByIdMqttClientAuthInput)">
            <summary>
            获取MQTT 客户端认证信息表详情 ℹ️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttClientAuthService.Add(Admin.NET.Application.AddMqttClientAuthInput)">
            <summary>
            增加MQTT 客户端认证信息表 ➕
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttClientAuthService.Update(Admin.NET.Application.UpdateMqttClientAuthInput)">
            <summary>
            更新MQTT 客户端认证信息表 ✏️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttClientAuthService.Delete(Admin.NET.Application.DeleteMqttClientAuthInput)">
            <summary>
            删除MQTT 客户端认证信息表 ❌
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttClientAuthService.BatchDelete(System.Collections.Generic.List{Admin.NET.Application.DeleteMqttClientAuthInput})">
            <summary>
            批量删除MQTT 客户端认证信息表 ❌
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Application.MqttClientDto">
            <summary>
            MQTT 客户端连接信息表输出参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.GroupId">
            <summary>
            设备组ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.ProductKey">
            <summary>
            产品Key
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.DeviceSecret">
            <summary>
            设备密钥（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.IpAddress">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.Port">
            <summary>
            端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.Status">
            <summary>
            连接状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.ProtocolVersion">
            <summary>
            协议版本
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.KeepAlive">
            <summary>
            心跳间隔（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.CleanSession">
            <summary>
            清除会话（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.ConnectedAt">
            <summary>
            连接时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.DisconnectedAt">
            <summary>
            断开时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.MessagesSent">
            <summary>
            发送消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.MessagesReceived">
            <summary>
            接收消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.BytesSent">
            <summary>
            发送字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.BytesReceived">
            <summary>
            接收字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.SubscriptionCount">
            <summary>
            订阅数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.MaxSubscriptions">
            <summary>
            最大订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.DeviceType">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.DeviceVersion">
            <summary>
            设备版本
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.DeviceTags">
            <summary>
            设备标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.LocationInfo">
            <summary>
            位置信息（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.IsOnline">
            <summary>
            是否在线（0:离线，1:在线）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.AuthMode">
            <summary>
            认证模式
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.ClientType">
            <summary>
            客户端类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.LastError">
            <summary>
            最后错误信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.CreateUserId">
            <summary>
            创建者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.UpdateUserId">
            <summary>
            修改者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.IsDelete">
            <summary>
            软删除
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.CreateUserName">
            <summary>
            创建者姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientDto.UpdateUserName">
            <summary>
            修改者姓名
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttClientBaseInput">
            <summary>
            MQTT 客户端连接信息表基础输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.GroupId">
            <summary>
            设备组ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.ProductKey">
            <summary>
            产品Key
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.DeviceSecret">
            <summary>
            设备密钥（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.IpAddress">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.Port">
            <summary>
            端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.Status">
            <summary>
            连接状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.ProtocolVersion">
            <summary>
            协议版本
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.KeepAlive">
            <summary>
            心跳间隔（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.CleanSession">
            <summary>
            清除会话（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.ConnectedAt">
            <summary>
            连接时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.DisconnectedAt">
            <summary>
            断开时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.MessagesSent">
            <summary>
            发送消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.MessagesReceived">
            <summary>
            接收消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.BytesSent">
            <summary>
            发送字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.BytesReceived">
            <summary>
            接收字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.SubscriptionCount">
            <summary>
            订阅数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.MaxSubscriptions">
            <summary>
            最大订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.DeviceType">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.DeviceVersion">
            <summary>
            设备版本
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.DeviceTags">
            <summary>
            设备标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.LocationInfo">
            <summary>
            位置信息（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.IsOnline">
            <summary>
            是否在线（0:离线，1:在线）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.AuthMode">
            <summary>
            认证模式
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.ClientType">
            <summary>
            客户端类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.LastError">
            <summary>
            最后错误信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientBaseInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.PageMqttClientInput">
            <summary>
            MQTT 客户端连接信息表分页查询输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.GroupId">
            <summary>
            设备组ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.ProductKey">
            <summary>
            产品Key
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.DeviceSecret">
            <summary>
            设备密钥（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.IpAddress">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.Port">
            <summary>
            端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.Status">
            <summary>
            连接状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.ProtocolVersion">
            <summary>
            协议版本
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.KeepAlive">
            <summary>
            心跳间隔（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.CleanSession">
            <summary>
            清除会话（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.ConnectedAtRange">
            <summary>
            连接时间范围
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.DisconnectedAtRange">
            <summary>
            断开时间范围
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.LastActivityRange">
            <summary>
            最后活动时间范围
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.MessagesSent">
            <summary>
            发送消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.MessagesReceived">
            <summary>
            接收消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.BytesSent">
            <summary>
            发送字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.BytesReceived">
            <summary>
            接收字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.SubscriptionCount">
            <summary>
            订阅数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.MaxSubscriptions">
            <summary>
            最大订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.DeviceType">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.DeviceVersion">
            <summary>
            设备版本
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.DeviceTags">
            <summary>
            设备标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.LocationInfo">
            <summary>
            位置信息（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.IsOnline">
            <summary>
            是否在线（0:离线，1:在线）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.AuthMode">
            <summary>
            认证模式
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.ClientType">
            <summary>
            客户端类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.LastError">
            <summary>
            最后错误信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttClientInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.AddMqttClientInput">
            <summary>
            MQTT 客户端连接信息表增加输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.GroupId">
            <summary>
            设备组ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.ProductKey">
            <summary>
            产品Key
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.DeviceSecret">
            <summary>
            设备密钥（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.IpAddress">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.Port">
            <summary>
            端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.Status">
            <summary>
            连接状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.ProtocolVersion">
            <summary>
            协议版本
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.KeepAlive">
            <summary>
            心跳间隔（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.CleanSession">
            <summary>
            清除会话（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.ConnectedAt">
            <summary>
            连接时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.DisconnectedAt">
            <summary>
            断开时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.MessagesSent">
            <summary>
            发送消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.MessagesReceived">
            <summary>
            接收消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.BytesSent">
            <summary>
            发送字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.BytesReceived">
            <summary>
            接收字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.SubscriptionCount">
            <summary>
            订阅数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.MaxSubscriptions">
            <summary>
            最大订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.DeviceType">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.DeviceVersion">
            <summary>
            设备版本
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.DeviceTags">
            <summary>
            设备标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.LocationInfo">
            <summary>
            位置信息（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.IsOnline">
            <summary>
            是否在线（0:离线，1:在线）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.AuthMode">
            <summary>
            认证模式
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.ClientType">
            <summary>
            客户端类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.LastError">
            <summary>
            最后错误信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttClientInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.DeleteMqttClientInput">
            <summary>
            MQTT 客户端连接信息表删除输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeleteMqttClientInput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="T:Admin.NET.Application.UpdateMqttClientInput">
            <summary>
            MQTT 客户端连接信息表更新输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.Id">
            <summary>
            主键Id
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.InstanceId">
            <summary>
            实例ID
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.ClientId">
            <summary>
            客户端ID
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.DeviceName">
            <summary>
            设备名称
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.GroupId">
            <summary>
            设备组ID
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.ProductKey">
            <summary>
            产品Key
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.DeviceSecret">
            <summary>
            设备密钥（加密存储）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.IpAddress">
            <summary>
            IP地址
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.Port">
            <summary>
            端口
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.Status">
            <summary>
            连接状态
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.ProtocolVersion">
            <summary>
            协议版本
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.KeepAlive">
            <summary>
            心跳间隔（秒）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.CleanSession">
            <summary>
            清除会话（0:否，1:是）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.ConnectedAt">
            <summary>
            连接时间
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.DisconnectedAt">
            <summary>
            断开时间
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.LastActivity">
            <summary>
            最后活动时间
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.MessagesSent">
            <summary>
            发送消息数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.MessagesReceived">
            <summary>
            接收消息数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.BytesSent">
            <summary>
            发送字节数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.BytesReceived">
            <summary>
            接收字节数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.SubscriptionCount">
            <summary>
            订阅数量
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.MaxSubscriptions">
            <summary>
            最大订阅数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.DeviceType">
            <summary>
            设备类型
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.DeviceVersion">
            <summary>
            设备版本
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.DeviceTags">
            <summary>
            设备标签（JSON）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.LocationInfo">
            <summary>
            位置信息（JSON）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.IsOnline">
            <summary>
            是否在线（0:离线，1:在线）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.AuthMode">
            <summary>
            认证模式
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.ClientType">
            <summary>
            客户端类型
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.LastError">
            <summary>
            最后错误信息
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttClientInput.Remark">
            <summary>
            备注
            </summary>    
        </member>
        <member name="T:Admin.NET.Application.QueryByIdMqttClientInput">
            <summary>
            MQTT 客户端连接信息表主键查询输入参数
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttClientOutput">
            <summary>
            MQTT 客户端连接信息表输出参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.GroupId">
            <summary>
            设备组ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.ProductKey">
            <summary>
            产品Key
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.DeviceSecret">
            <summary>
            设备密钥（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.IpAddress">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.Port">
            <summary>
            端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.Status">
            <summary>
            连接状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.ProtocolVersion">
            <summary>
            协议版本
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.KeepAlive">
            <summary>
            心跳间隔（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.CleanSession">
            <summary>
            清除会话（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.ConnectedAt">
            <summary>
            连接时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.DisconnectedAt">
            <summary>
            断开时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.MessagesSent">
            <summary>
            发送消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.MessagesReceived">
            <summary>
            接收消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.BytesSent">
            <summary>
            发送字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.BytesReceived">
            <summary>
            接收字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.SubscriptionCount">
            <summary>
            订阅数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.MaxSubscriptions">
            <summary>
            最大订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.DeviceType">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.DeviceVersion">
            <summary>
            设备版本
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.DeviceTags">
            <summary>
            设备标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.LocationInfo">
            <summary>
            位置信息（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.IsOnline">
            <summary>
            是否在线（0:离线，1:在线）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.AuthMode">
            <summary>
            认证模式
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.ClientType">
            <summary>
            客户端类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.LastError">
            <summary>
            最后错误信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.CreateUserId">
            <summary>
            创建者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.UpdateUserId">
            <summary>
            修改者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.IsDelete">
            <summary>
            软删除
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.CreateUserName">
            <summary>
            创建者姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttClientOutput.UpdateUserName">
            <summary>
            修改者姓名
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttClientService">
            <summary>
            MQTT 客户端连接信息表服务 🧩
            </summary>
        </member>
        <member name="M:Admin.NET.Application.MqttClientService.Page(Admin.NET.Application.PageMqttClientInput)">
            <summary>
            分页查询MQTT 客户端连接信息表 🔖
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttClientService.Detail(Admin.NET.Application.QueryByIdMqttClientInput)">
            <summary>
            获取MQTT 客户端连接信息表详情 ℹ️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttClientService.Add(Admin.NET.Application.AddMqttClientInput)">
            <summary>
            增加MQTT 客户端连接信息表 ➕
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttClientService.Update(Admin.NET.Application.UpdateMqttClientInput)">
            <summary>
            更新MQTT 客户端连接信息表 ✏️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttClientService.Delete(Admin.NET.Application.DeleteMqttClientInput)">
            <summary>
            删除MQTT 客户端连接信息表 ❌
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttClientService.BatchDelete(System.Collections.Generic.List{Admin.NET.Application.DeleteMqttClientInput})">
            <summary>
            批量删除MQTT 客户端连接信息表 ❌
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Application.MqttDeviceGroupDto">
            <summary>
            MQTT设备组管理表输出参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.GroupId">
            <summary>
            设备组标识（如：GID_TestGroup）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.GroupName">
            <summary>
            设备组名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.GroupType">
            <summary>
            设备组类型（Production、Test、Development等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.ProductKey">
            <summary>
            产品Key（阿里云IoT产品标识）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.Description">
            <summary>
            设备组描述
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.MaxDeviceCount">
            <summary>
            最大设备数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.CurrentDeviceCount">
            <summary>
            当前设备数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.AllowedTopicPatterns">
            <summary>
            允许的主题模式（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.DeniedTopicPatterns">
            <summary>
            禁止的主题模式（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.DefaultQosLevel">
            <summary>
            默认QoS等级（0,1,2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.MaxMessageSize">
            <summary>
            最大消息大小（字节）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.MessageRateLimit">
            <summary>
            消息速率限制（消息/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.ByteRateLimit">
            <summary>
            字节速率限制（字节/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.KeepAliveTimeout">
            <summary>
            心跳超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.EnableRetainMessage">
            <summary>
            是否允许保留消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.EnableWildcardSubscription">
            <summary>
            是否允许通配符订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.EnableSharedSubscription">
            <summary>
            是否允许共享订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.IpWhitelist">
            <summary>
            IP白名单（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.IpBlacklist">
            <summary>
            IP黑名单（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.AllowedTimeRanges">
            <summary>
            允许连接的时间范围（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.SecurityLevel">
            <summary>
            安全级别（Low、Medium、High）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.EncryptionRequired">
            <summary>
            是否要求加密连接
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.CertificateRequired">
            <summary>
            是否要求客户端证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.AuthMode">
            <summary>
            认证模式（Signature、Username、JWT、Certificate）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.SignMethod">
            <summary>
            签名算法（hmacsha1、hmacsha256、hmacmd5）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.AccessKeyId">
            <summary>
            AccessKey ID（阿里云认证）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.AccessKeySecret">
            <summary>
            AccessKey Secret（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.JwtSecret">
            <summary>
            JWT密钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.JwtExpiry">
            <summary>
            JWT过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.CaCertificate">
            <summary>
            CA证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.ExpireTime">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.CreatedDeviceCount">
            <summary>
            已创建设备数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.OnlineDeviceCount">
            <summary>
            在线设备数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.TotalMessageCount">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.TotalByteCount">
            <summary>
            总字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.AlertRules">
            <summary>
            告警规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.MonitorConfig">
            <summary>
            监控配置（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.Tags">
            <summary>
            标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.CreateUserId">
            <summary>
            创建者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.UpdateUserId">
            <summary>
            修改者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.IsDelete">
            <summary>
            软删除
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.CreateUserName">
            <summary>
            创建者姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupDto.UpdateUserName">
            <summary>
            修改者姓名
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttDeviceGroupBaseInput">
            <summary>
            MQTT设备组管理表基础输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.GroupId">
            <summary>
            设备组标识（如：GID_TestGroup）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.GroupName">
            <summary>
            设备组名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.GroupType">
            <summary>
            设备组类型（Production、Test、Development等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.ProductKey">
            <summary>
            产品Key（阿里云IoT产品标识）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.Description">
            <summary>
            设备组描述
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.MaxDeviceCount">
            <summary>
            最大设备数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.CurrentDeviceCount">
            <summary>
            当前设备数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.AllowedTopicPatterns">
            <summary>
            允许的主题模式（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.DeniedTopicPatterns">
            <summary>
            禁止的主题模式（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.DefaultQosLevel">
            <summary>
            默认QoS等级（0,1,2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.MaxMessageSize">
            <summary>
            最大消息大小（字节）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.MessageRateLimit">
            <summary>
            消息速率限制（消息/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.ByteRateLimit">
            <summary>
            字节速率限制（字节/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.KeepAliveTimeout">
            <summary>
            心跳超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.EnableRetainMessage">
            <summary>
            是否允许保留消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.EnableWildcardSubscription">
            <summary>
            是否允许通配符订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.EnableSharedSubscription">
            <summary>
            是否允许共享订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.IpWhitelist">
            <summary>
            IP白名单（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.IpBlacklist">
            <summary>
            IP黑名单（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.AllowedTimeRanges">
            <summary>
            允许连接的时间范围（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.SecurityLevel">
            <summary>
            安全级别（Low、Medium、High）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.EncryptionRequired">
            <summary>
            是否要求加密连接
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.CertificateRequired">
            <summary>
            是否要求客户端证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.AuthMode">
            <summary>
            认证模式（Signature、Username、JWT、Certificate）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.SignMethod">
            <summary>
            签名算法（hmacsha1、hmacsha256、hmacmd5）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.AccessKeyId">
            <summary>
            AccessKey ID（阿里云认证）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.AccessKeySecret">
            <summary>
            AccessKey Secret（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.JwtSecret">
            <summary>
            JWT密钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.JwtExpiry">
            <summary>
            JWT过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.CaCertificate">
            <summary>
            CA证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.ExpireTime">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.CreatedDeviceCount">
            <summary>
            已创建设备数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.OnlineDeviceCount">
            <summary>
            在线设备数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.TotalMessageCount">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.TotalByteCount">
            <summary>
            总字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.AlertRules">
            <summary>
            告警规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.MonitorConfig">
            <summary>
            监控配置（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.Tags">
            <summary>
            标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupBaseInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.PageMqttDeviceGroupInput">
            <summary>
            MQTT设备组管理表分页查询输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.GroupId">
            <summary>
            设备组标识（如：GID_TestGroup）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.GroupName">
            <summary>
            设备组名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.GroupType">
            <summary>
            设备组类型（Production、Test、Development等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.ProductKey">
            <summary>
            产品Key（阿里云IoT产品标识）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.Description">
            <summary>
            设备组描述
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.MaxDeviceCount">
            <summary>
            最大设备数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.CurrentDeviceCount">
            <summary>
            当前设备数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.AllowedTopicPatterns">
            <summary>
            允许的主题模式（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.DeniedTopicPatterns">
            <summary>
            禁止的主题模式（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.DefaultQosLevel">
            <summary>
            默认QoS等级（0,1,2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.MaxMessageSize">
            <summary>
            最大消息大小（字节）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.MessageRateLimit">
            <summary>
            消息速率限制（消息/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.ByteRateLimit">
            <summary>
            字节速率限制（字节/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.KeepAliveTimeout">
            <summary>
            心跳超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.EnableRetainMessage">
            <summary>
            是否允许保留消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.EnableWildcardSubscription">
            <summary>
            是否允许通配符订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.EnableSharedSubscription">
            <summary>
            是否允许共享订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.IpWhitelist">
            <summary>
            IP白名单（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.IpBlacklist">
            <summary>
            IP黑名单（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.AllowedTimeRanges">
            <summary>
            允许连接的时间范围（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.SecurityLevel">
            <summary>
            安全级别（Low、Medium、High）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.EncryptionRequired">
            <summary>
            是否要求加密连接
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.CertificateRequired">
            <summary>
            是否要求客户端证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.AuthMode">
            <summary>
            认证模式（Signature、Username、JWT、Certificate）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.SignMethod">
            <summary>
            签名算法（hmacsha1、hmacsha256、hmacmd5）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.AccessKeyId">
            <summary>
            AccessKey ID（阿里云认证）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.AccessKeySecret">
            <summary>
            AccessKey Secret（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.JwtSecret">
            <summary>
            JWT密钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.JwtExpiry">
            <summary>
            JWT过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.CaCertificate">
            <summary>
            CA证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.ExpireTimeRange">
            <summary>
            过期时间范围
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.LastActivityRange">
            <summary>
            最后活动时间范围
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.CreatedDeviceCount">
            <summary>
            已创建设备数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.OnlineDeviceCount">
            <summary>
            在线设备数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.TotalMessageCount">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.TotalByteCount">
            <summary>
            总字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.LastMessageTimeRange">
            <summary>
            最后消息时间范围
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.AlertRules">
            <summary>
            告警规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.MonitorConfig">
            <summary>
            监控配置（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.Tags">
            <summary>
            标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttDeviceGroupInput.SelectKeyList">
            <summary>
            选中主键列表
            </summary>
        </member>
        <member name="T:Admin.NET.Application.AddMqttDeviceGroupInput">
            <summary>
            MQTT设备组管理表增加输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.GroupId">
            <summary>
            设备组标识（如：GID_TestGroup）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.GroupName">
            <summary>
            设备组名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.GroupType">
            <summary>
            设备组类型（Production、Test、Development等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.ProductKey">
            <summary>
            产品Key（阿里云IoT产品标识）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.Description">
            <summary>
            设备组描述
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.MaxDeviceCount">
            <summary>
            最大设备数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.CurrentDeviceCount">
            <summary>
            当前设备数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.AllowedTopicPatterns">
            <summary>
            允许的主题模式（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.DeniedTopicPatterns">
            <summary>
            禁止的主题模式（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.DefaultQosLevel">
            <summary>
            默认QoS等级（0,1,2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.MaxMessageSize">
            <summary>
            最大消息大小（字节）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.MessageRateLimit">
            <summary>
            消息速率限制（消息/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.ByteRateLimit">
            <summary>
            字节速率限制（字节/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.KeepAliveTimeout">
            <summary>
            心跳超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.EnableRetainMessage">
            <summary>
            是否允许保留消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.EnableWildcardSubscription">
            <summary>
            是否允许通配符订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.EnableSharedSubscription">
            <summary>
            是否允许共享订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.IpWhitelist">
            <summary>
            IP白名单（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.IpBlacklist">
            <summary>
            IP黑名单（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.AllowedTimeRanges">
            <summary>
            允许连接的时间范围（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.SecurityLevel">
            <summary>
            安全级别（Low、Medium、High）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.EncryptionRequired">
            <summary>
            是否要求加密连接
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.CertificateRequired">
            <summary>
            是否要求客户端证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.AuthMode">
            <summary>
            认证模式（Signature、Username、JWT、Certificate）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.SignMethod">
            <summary>
            签名算法（hmacsha1、hmacsha256、hmacmd5）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.AccessKeyId">
            <summary>
            AccessKey ID（阿里云认证）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.AccessKeySecret">
            <summary>
            AccessKey Secret（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.JwtSecret">
            <summary>
            JWT密钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.JwtExpiry">
            <summary>
            JWT过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.CaCertificate">
            <summary>
            CA证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.ExpireTime">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.CreatedDeviceCount">
            <summary>
            已创建设备数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.OnlineDeviceCount">
            <summary>
            在线设备数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.TotalMessageCount">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.TotalByteCount">
            <summary>
            总字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.AlertRules">
            <summary>
            告警规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.MonitorConfig">
            <summary>
            监控配置（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.Tags">
            <summary>
            标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttDeviceGroupInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.DeleteMqttDeviceGroupInput">
            <summary>
            MQTT设备组管理表删除输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeleteMqttDeviceGroupInput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="T:Admin.NET.Application.UpdateMqttDeviceGroupInput">
            <summary>
            MQTT设备组管理表更新输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.Id">
            <summary>
            主键Id
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.InstanceId">
            <summary>
            实例ID
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.GroupId">
            <summary>
            设备组标识（如：GID_TestGroup）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.GroupName">
            <summary>
            设备组名称
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.GroupType">
            <summary>
            设备组类型（Production、Test、Development等）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.ProductKey">
            <summary>
            产品Key（阿里云IoT产品标识）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.Description">
            <summary>
            设备组描述
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.MaxDeviceCount">
            <summary>
            最大设备数量
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.CurrentDeviceCount">
            <summary>
            当前设备数量
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.AllowedTopicPatterns">
            <summary>
            允许的主题模式（JSON数组）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.DeniedTopicPatterns">
            <summary>
            禁止的主题模式（JSON数组）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.DefaultQosLevel">
            <summary>
            默认QoS等级（0,1,2）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.MaxMessageSize">
            <summary>
            最大消息大小（字节）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.MessageRateLimit">
            <summary>
            消息速率限制（消息/秒）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.ByteRateLimit">
            <summary>
            字节速率限制（字节/秒）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.MaxConnections">
            <summary>
            最大连接数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.KeepAliveTimeout">
            <summary>
            心跳超时时间（秒）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.EnableRetainMessage">
            <summary>
            是否允许保留消息
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.EnableWildcardSubscription">
            <summary>
            是否允许通配符订阅
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.EnableSharedSubscription">
            <summary>
            是否允许共享订阅
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.IpWhitelist">
            <summary>
            IP白名单（JSON数组）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.IpBlacklist">
            <summary>
            IP黑名单（JSON数组）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.AllowedTimeRanges">
            <summary>
            允许连接的时间范围（JSON）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.SecurityLevel">
            <summary>
            安全级别（Low、Medium、High）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.EncryptionRequired">
            <summary>
            是否要求加密连接
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.CertificateRequired">
            <summary>
            是否要求客户端证书
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.AuthMode">
            <summary>
            认证模式（Signature、Username、JWT、Certificate）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.SignMethod">
            <summary>
            签名算法（hmacsha1、hmacsha256、hmacmd5）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.AccessKeyId">
            <summary>
            AccessKey ID（阿里云认证）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.AccessKeySecret">
            <summary>
            AccessKey Secret（加密存储）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.JwtSecret">
            <summary>
            JWT密钥
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.JwtExpiry">
            <summary>
            JWT过期时间（秒）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.CaCertificate">
            <summary>
            CA证书
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.IsEnabled">
            <summary>
            是否启用
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.ExpireTime">
            <summary>
            过期时间
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.LastActivity">
            <summary>
            最后活动时间
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.CreatedDeviceCount">
            <summary>
            已创建设备数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.OnlineDeviceCount">
            <summary>
            在线设备数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.TotalMessageCount">
            <summary>
            总消息数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.TotalByteCount">
            <summary>
            总字节数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.LastMessageTime">
            <summary>
            最后消息时间
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.AlertRules">
            <summary>
            告警规则（JSON）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.MonitorConfig">
            <summary>
            监控配置（JSON）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.Tags">
            <summary>
            标签（JSON）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttDeviceGroupInput.Remark">
            <summary>
            备注
            </summary>    
        </member>
        <member name="T:Admin.NET.Application.QueryByIdMqttDeviceGroupInput">
            <summary>
            MQTT设备组管理表主键查询输入参数
            </summary>
        </member>
        <member name="T:Admin.NET.Application.ImportMqttDeviceGroupInput">
            <summary>
            MQTT设备组管理表数据导入实体
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.GroupId">
            <summary>
            设备组标识（如：GID_TestGroup）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.GroupName">
            <summary>
            设备组名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.GroupType">
            <summary>
            设备组类型（Production、Test、Development等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.ProductKey">
            <summary>
            产品Key（阿里云IoT产品标识）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.Description">
            <summary>
            设备组描述
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.MaxDeviceCount">
            <summary>
            最大设备数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.CurrentDeviceCount">
            <summary>
            当前设备数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.AllowedTopicPatterns">
            <summary>
            允许的主题模式（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.DeniedTopicPatterns">
            <summary>
            禁止的主题模式（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.DefaultQosLevel">
            <summary>
            默认QoS等级（0,1,2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.MaxMessageSize">
            <summary>
            最大消息大小（字节）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.MessageRateLimit">
            <summary>
            消息速率限制（消息/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.ByteRateLimit">
            <summary>
            字节速率限制（字节/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.KeepAliveTimeout">
            <summary>
            心跳超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.EnableRetainMessage">
            <summary>
            是否允许保留消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.EnableWildcardSubscription">
            <summary>
            是否允许通配符订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.EnableSharedSubscription">
            <summary>
            是否允许共享订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.IpWhitelist">
            <summary>
            IP白名单（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.IpBlacklist">
            <summary>
            IP黑名单（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.AllowedTimeRanges">
            <summary>
            允许连接的时间范围（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.SecurityLevel">
            <summary>
            安全级别（Low、Medium、High）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.EncryptionRequired">
            <summary>
            是否要求加密连接
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.CertificateRequired">
            <summary>
            是否要求客户端证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.AuthMode">
            <summary>
            认证模式（Signature、Username、JWT、Certificate）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.SignMethod">
            <summary>
            签名算法（hmacsha1、hmacsha256、hmacmd5）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.AccessKeyId">
            <summary>
            AccessKey ID（阿里云认证）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.AccessKeySecret">
            <summary>
            AccessKey Secret（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.JwtSecret">
            <summary>
            JWT密钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.JwtExpiry">
            <summary>
            JWT过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.CaCertificate">
            <summary>
            CA证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.ExpireTime">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.CreatedDeviceCount">
            <summary>
            已创建设备数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.OnlineDeviceCount">
            <summary>
            在线设备数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.TotalMessageCount">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.TotalByteCount">
            <summary>
            总字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.AlertRules">
            <summary>
            告警规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.MonitorConfig">
            <summary>
            监控配置（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.Tags">
            <summary>
            标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ImportMqttDeviceGroupInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttDeviceGroupOutput">
            <summary>
            MQTT设备组管理表输出参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.GroupId">
            <summary>
            设备组标识（如：GID_TestGroup）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.GroupName">
            <summary>
            设备组名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.GroupType">
            <summary>
            设备组类型（Production、Test、Development等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.ProductKey">
            <summary>
            产品Key（阿里云IoT产品标识）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.Description">
            <summary>
            设备组描述
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.MaxDeviceCount">
            <summary>
            最大设备数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.CurrentDeviceCount">
            <summary>
            当前设备数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.AllowedTopicPatterns">
            <summary>
            允许的主题模式（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.DeniedTopicPatterns">
            <summary>
            禁止的主题模式（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.DefaultQosLevel">
            <summary>
            默认QoS等级（0,1,2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.MaxMessageSize">
            <summary>
            最大消息大小（字节）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.MessageRateLimit">
            <summary>
            消息速率限制（消息/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.ByteRateLimit">
            <summary>
            字节速率限制（字节/秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.ConnectionTimeout">
            <summary>
            连接超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.KeepAliveTimeout">
            <summary>
            心跳超时时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.EnableRetainMessage">
            <summary>
            是否允许保留消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.EnableWildcardSubscription">
            <summary>
            是否允许通配符订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.EnableSharedSubscription">
            <summary>
            是否允许共享订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.IpWhitelist">
            <summary>
            IP白名单（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.IpBlacklist">
            <summary>
            IP黑名单（JSON数组）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.AllowedTimeRanges">
            <summary>
            允许连接的时间范围（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.SecurityLevel">
            <summary>
            安全级别（Low、Medium、High）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.EncryptionRequired">
            <summary>
            是否要求加密连接
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.CertificateRequired">
            <summary>
            是否要求客户端证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.AuthMode">
            <summary>
            认证模式（Signature、Username、JWT、Certificate）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.SignMethod">
            <summary>
            签名算法（hmacsha1、hmacsha256、hmacmd5）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.AccessKeyId">
            <summary>
            AccessKey ID（阿里云认证）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.AccessKeySecret">
            <summary>
            AccessKey Secret（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.JwtSecret">
            <summary>
            JWT密钥
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.JwtExpiry">
            <summary>
            JWT过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.CaCertificate">
            <summary>
            CA证书
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.ExpireTime">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.CreatedDeviceCount">
            <summary>
            已创建设备数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.OnlineDeviceCount">
            <summary>
            在线设备数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.TotalMessageCount">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.TotalByteCount">
            <summary>
            总字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.AlertRules">
            <summary>
            告警规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.MonitorConfig">
            <summary>
            监控配置（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.Tags">
            <summary>
            标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.CreateUserId">
            <summary>
            创建者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.UpdateUserId">
            <summary>
            修改者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.IsDelete">
            <summary>
            软删除
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.CreateUserName">
            <summary>
            创建者姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttDeviceGroupOutput.UpdateUserName">
            <summary>
            修改者姓名
            </summary>
        </member>
        <member name="T:Admin.NET.Application.ExportMqttDeviceGroupOutput">
            <summary>
            MQTT设备组管理表数据导入模板实体
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttDeviceGroupService">
            <summary>
            MQTT设备组管理表服务 🧩
            </summary>
        </member>
        <member name="M:Admin.NET.Application.MqttDeviceGroupService.Page(Admin.NET.Application.PageMqttDeviceGroupInput)">
            <summary>
            分页查询MQTT设备组管理表 🔖
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttDeviceGroupService.Detail(Admin.NET.Application.QueryByIdMqttDeviceGroupInput)">
            <summary>
            获取MQTT设备组管理表详情 ℹ️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttDeviceGroupService.Add(Admin.NET.Application.AddMqttDeviceGroupInput)">
            <summary>
            增加MQTT设备组管理表 ➕
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttDeviceGroupService.Update(Admin.NET.Application.UpdateMqttDeviceGroupInput)">
            <summary>
            更新MQTT设备组管理表 ✏️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttDeviceGroupService.Delete(Admin.NET.Application.DeleteMqttDeviceGroupInput)">
            <summary>
            删除MQTT设备组管理表 ❌
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttDeviceGroupService.BatchDelete(System.Collections.Generic.List{Admin.NET.Application.DeleteMqttDeviceGroupInput})">
            <summary>
            批量删除MQTT设备组管理表 ❌
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttDeviceGroupService.Export(Admin.NET.Application.PageMqttDeviceGroupInput)">
            <summary>
            导出MQTT设备组管理表记录 🔖
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttDeviceGroupService.DownloadTemplate">
            <summary>
            下载MQTT设备组管理表数据导入模板 ⬇️
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttDeviceGroupService.ImportData(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            导入MQTT设备组管理表记录 💾
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Application.MqttInstanceDto">
            <summary>
            MQTT 实例信息表输出参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.InstanceName">
            <summary>
            实例名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.InstanceCode">
            <summary>
            实例编码
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.InstanceType">
            <summary>
            实例类型（EMQX、阿里云等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.ServerHost">
            <summary>
            服务器地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.ServerPort">
            <summary>
            服务器端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.ApiHost">
            <summary>
            API地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.ApiPort">
            <summary>
            API端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.ApiUsername">
            <summary>
            API用户名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.ApiPassword">
            <summary>
            API密码（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.Status">
            <summary>
            实例状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.EnableSsl">
            <summary>
            是否启用SSL（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.SslPort">
            <summary>
            SSL端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.EnableWebSocket">
            <summary>
            是否启用WebSocket（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.WsPort">
            <summary>
            WebSocket端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.WssPort">
            <summary>
            WebSocket SSL端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.CurrentConnections">
            <summary>
            当前连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.AliyunProductKey">
            <summary>
            阿里云产品Key
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.AliyunRegionId">
            <summary>
            阿里云区域ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.AliyunInstanceId">
            <summary>
            阿里云实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.DeviceIdPrefix">
            <summary>
            设备ID前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.GroupIdPrefix">
            <summary>
            组ID前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.AliyunCompatible">
            <summary>
            是否兼容阿里云（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.ConfigJson">
            <summary>
            配置JSON
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.LastHeartbeat">
            <summary>
            最后心跳时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.TotalMessages">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.TotalBytes">
            <summary>
            总字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.CreateUserId">
            <summary>
            创建者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.UpdateUserId">
            <summary>
            修改者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.IsDelete">
            <summary>
            软删除
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.CreateUserName">
            <summary>
            创建者姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceDto.UpdateUserName">
            <summary>
            修改者姓名
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttInstanceBaseInput">
            <summary>
            MQTT 实例信息表基础输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.InstanceName">
            <summary>
            实例名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.InstanceCode">
            <summary>
            实例编码
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.InstanceType">
            <summary>
            实例类型（EMQX、阿里云等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.ServerHost">
            <summary>
            服务器地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.ServerPort">
            <summary>
            服务器端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.ApiHost">
            <summary>
            API地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.ApiPort">
            <summary>
            API端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.ApiUsername">
            <summary>
            API用户名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.ApiPassword">
            <summary>
            API密码（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.Status">
            <summary>
            实例状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.EnableSsl">
            <summary>
            是否启用SSL（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.SslPort">
            <summary>
            SSL端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.EnableWebSocket">
            <summary>
            是否启用WebSocket（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.WsPort">
            <summary>
            WebSocket端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.WssPort">
            <summary>
            WebSocket SSL端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.CurrentConnections">
            <summary>
            当前连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.AliyunProductKey">
            <summary>
            阿里云产品Key
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.AliyunRegionId">
            <summary>
            阿里云区域ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.AliyunInstanceId">
            <summary>
            阿里云实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.DeviceIdPrefix">
            <summary>
            设备ID前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.GroupIdPrefix">
            <summary>
            组ID前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.AliyunCompatible">
            <summary>
            是否兼容阿里云（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.ConfigJson">
            <summary>
            配置JSON
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.LastHeartbeat">
            <summary>
            最后心跳时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.TotalMessages">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.TotalBytes">
            <summary>
            总字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceBaseInput.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.PageMqttInstanceInput">
            <summary>
            MQTT 实例信息表分页查询输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.InstanceName">
            <summary>
            实例名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.InstanceCode">
            <summary>
            实例编码
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.InstanceType">
            <summary>
            实例类型（EMQX、阿里云等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.ServerHost">
            <summary>
            服务器地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.ServerPort">
            <summary>
            服务器端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.ApiHost">
            <summary>
            API地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.ApiPort">
            <summary>
            API端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.ApiUsername">
            <summary>
            API用户名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.ApiPassword">
            <summary>
            API密码（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.Status">
            <summary>
            实例状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.EnableSsl">
            <summary>
            是否启用SSL（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.SslPort">
            <summary>
            SSL端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.EnableWebSocket">
            <summary>
            是否启用WebSocket（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.WsPort">
            <summary>
            WebSocket端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.WssPort">
            <summary>
            WebSocket SSL端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.CurrentConnections">
            <summary>
            当前连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.AliyunProductKey">
            <summary>
            阿里云产品Key
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.AliyunRegionId">
            <summary>
            阿里云区域ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.AliyunInstanceId">
            <summary>
            阿里云实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.DeviceIdPrefix">
            <summary>
            设备ID前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.GroupIdPrefix">
            <summary>
            组ID前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.AliyunCompatible">
            <summary>
            是否兼容阿里云（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.ConfigJson">
            <summary>
            配置JSON
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.LastHeartbeatRange">
            <summary>
            最后心跳时间范围
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.TotalMessages">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.TotalBytes">
            <summary>
            总字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttInstanceInput.LastActivityRange">
            <summary>
            最后活动时间范围
            </summary>
        </member>
        <member name="T:Admin.NET.Application.AddMqttInstanceInput">
            <summary>
            MQTT 实例信息表增加输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.InstanceName">
            <summary>
            实例名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.InstanceCode">
            <summary>
            实例编码
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.InstanceType">
            <summary>
            实例类型（EMQX、阿里云等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.ServerHost">
            <summary>
            服务器地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.ServerPort">
            <summary>
            服务器端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.ApiHost">
            <summary>
            API地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.ApiPort">
            <summary>
            API端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.ApiUsername">
            <summary>
            API用户名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.ApiPassword">
            <summary>
            API密码（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.Status">
            <summary>
            实例状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.EnableSsl">
            <summary>
            是否启用SSL（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.SslPort">
            <summary>
            SSL端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.EnableWebSocket">
            <summary>
            是否启用WebSocket（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.WsPort">
            <summary>
            WebSocket端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.WssPort">
            <summary>
            WebSocket SSL端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.CurrentConnections">
            <summary>
            当前连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.AliyunProductKey">
            <summary>
            阿里云产品Key
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.AliyunRegionId">
            <summary>
            阿里云区域ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.AliyunInstanceId">
            <summary>
            阿里云实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.DeviceIdPrefix">
            <summary>
            设备ID前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.GroupIdPrefix">
            <summary>
            组ID前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.AliyunCompatible">
            <summary>
            是否兼容阿里云（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.ConfigJson">
            <summary>
            配置JSON
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.LastHeartbeat">
            <summary>
            最后心跳时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.TotalMessages">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.TotalBytes">
            <summary>
            总字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttInstanceInput.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.DeleteMqttInstanceInput">
            <summary>
            MQTT 实例信息表删除输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeleteMqttInstanceInput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="T:Admin.NET.Application.UpdateMqttInstanceInput">
            <summary>
            MQTT 实例信息表更新输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.Id">
            <summary>
            主键Id
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.InstanceName">
            <summary>
            实例名称
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.InstanceCode">
            <summary>
            实例编码
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.InstanceType">
            <summary>
            实例类型（EMQX、阿里云等）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.ServerHost">
            <summary>
            服务器地址
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.ServerPort">
            <summary>
            服务器端口
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.ApiHost">
            <summary>
            API地址
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.ApiPort">
            <summary>
            API端口
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.ApiUsername">
            <summary>
            API用户名
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.ApiPassword">
            <summary>
            API密码（加密存储）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.Status">
            <summary>
            实例状态
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.EnableSsl">
            <summary>
            是否启用SSL（0:否，1:是）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.SslPort">
            <summary>
            SSL端口
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.EnableWebSocket">
            <summary>
            是否启用WebSocket（0:否，1:是）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.WsPort">
            <summary>
            WebSocket端口
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.WssPort">
            <summary>
            WebSocket SSL端口
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.MaxConnections">
            <summary>
            最大连接数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.CurrentConnections">
            <summary>
            当前连接数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.AliyunProductKey">
            <summary>
            阿里云产品Key
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.AliyunRegionId">
            <summary>
            阿里云区域ID
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.AliyunInstanceId">
            <summary>
            阿里云实例ID
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.DeviceIdPrefix">
            <summary>
            设备ID前缀
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.GroupIdPrefix">
            <summary>
            组ID前缀
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.AliyunCompatible">
            <summary>
            是否兼容阿里云（0:否，1:是）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.ConfigJson">
            <summary>
            配置JSON
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.LastHeartbeat">
            <summary>
            最后心跳时间
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.TotalMessages">
            <summary>
            总消息数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.TotalBytes">
            <summary>
            总字节数
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.Remark">
            <summary>
            备注
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttInstanceInput.LastActivity">
            <summary>
            最后活动时间
            </summary>    
        </member>
        <member name="T:Admin.NET.Application.QueryByIdMqttInstanceInput">
            <summary>
            MQTT 实例信息表主键查询输入参数
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttInstanceOutput">
            <summary>
            MQTT 实例信息表输出参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.InstanceName">
            <summary>
            实例名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.InstanceCode">
            <summary>
            实例编码
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.InstanceType">
            <summary>
            实例类型（EMQX、阿里云等）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.ServerHost">
            <summary>
            服务器地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.ServerPort">
            <summary>
            服务器端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.ApiHost">
            <summary>
            API地址
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.ApiPort">
            <summary>
            API端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.ApiUsername">
            <summary>
            API用户名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.ApiPassword">
            <summary>
            API密码（加密存储）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.Status">
            <summary>
            实例状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.EnableSsl">
            <summary>
            是否启用SSL（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.SslPort">
            <summary>
            SSL端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.EnableWebSocket">
            <summary>
            是否启用WebSocket（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.WsPort">
            <summary>
            WebSocket端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.WssPort">
            <summary>
            WebSocket SSL端口
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.CurrentConnections">
            <summary>
            当前连接数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.AliyunProductKey">
            <summary>
            阿里云产品Key
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.AliyunRegionId">
            <summary>
            阿里云区域ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.AliyunInstanceId">
            <summary>
            阿里云实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.DeviceIdPrefix">
            <summary>
            设备ID前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.GroupIdPrefix">
            <summary>
            组ID前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.AliyunCompatible">
            <summary>
            是否兼容阿里云（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.ConfigJson">
            <summary>
            配置JSON
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.LastHeartbeat">
            <summary>
            最后心跳时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.TotalMessages">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.TotalBytes">
            <summary>
            总字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.CreateUserId">
            <summary>
            创建者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.UpdateUserId">
            <summary>
            修改者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.IsDelete">
            <summary>
            软删除
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.CreateUserName">
            <summary>
            创建者姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttInstanceOutput.UpdateUserName">
            <summary>
            修改者姓名
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttInstanceService">
            <summary>
            MQTT 实例信息表服务 🧩
            </summary>
        </member>
        <member name="M:Admin.NET.Application.MqttInstanceService.Page(Admin.NET.Application.PageMqttInstanceInput)">
            <summary>
            分页查询MQTT 实例信息表 🔖
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttInstanceService.Detail(Admin.NET.Application.QueryByIdMqttInstanceInput)">
            <summary>
            获取MQTT 实例信息表详情 ℹ️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttInstanceService.Add(Admin.NET.Application.AddMqttInstanceInput)">
            <summary>
            增加MQTT 实例信息表 ➕
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttInstanceService.Update(Admin.NET.Application.UpdateMqttInstanceInput)">
            <summary>
            更新MQTT 实例信息表 ✏️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttInstanceService.Delete(Admin.NET.Application.DeleteMqttInstanceInput)">
            <summary>
            删除MQTT 实例信息表 ❌
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttInstanceService.BatchDelete(System.Collections.Generic.List{Admin.NET.Application.DeleteMqttInstanceInput})">
            <summary>
            批量删除MQTT 实例信息表 ❌
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Application.MqttSubscriptionDto">
            <summary>
            MQTT订阅关系表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.ClientRefId">
            <summary>
            客户端引用ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.TopicId">
            <summary>
            主题ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.QosLevel">
            <summary>
            QoS等级（0、1、2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.Status">
            <summary>
            订阅状态（Active、Inactive、Suspended）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.SubscribedAt">
            <summary>
            订阅时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.UnsubscribedAt">
            <summary>
            取消订阅时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.MessagesReceived">
            <summary>
            接收消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.BytesReceived">
            <summary>
            接收字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.MessagesDropped">
            <summary>
            丢弃消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.LastMessageId">
            <summary>
            最后消息ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.IsSharedSubscription">
            <summary>
            是否共享订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.SharedGroupName">
            <summary>
            共享组名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.SubscriptionOptions">
            <summary>
            订阅选项（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.MessageFilter">
            <summary>
            消息过滤器
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.MaxQueueLength">
            <summary>
            最大队列长度
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.CurrentQueueLength">
            <summary>
            当前队列长度
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.HandlingStrategy">
            <summary>
            处理策略（FIFO、LIFO、Priority）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.MaxRetryCount">
            <summary>
            最大重试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.LastError">
            <summary>
            最后错误信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.Statistics">
            <summary>
            统计信息（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.ConfigJson">
            <summary>
            配置信息（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.CreateUserId">
            <summary>
            创建用户ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionDto.UpdateUserId">
            <summary>
            更新用户ID
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttSubscriptionInput">
            <summary>
            MQTT订阅关系表输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionInput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionInput.Status">
            <summary>
            订阅状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttSubscriptionBaseInput">
            <summary>
            MQTT订阅关系表基础输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.ClientRefId">
            <summary>
            客户端引用ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.TopicId">
            <summary>
            主题ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.QosLevel">
            <summary>
            QoS等级（0、1、2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.Status">
            <summary>
            订阅状态（Active、Inactive、Suspended）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.SubscribedAt">
            <summary>
            订阅时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.UnsubscribedAt">
            <summary>
            取消订阅时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.MessagesReceived">
            <summary>
            接收消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.BytesReceived">
            <summary>
            接收字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.MessagesDropped">
            <summary>
            丢弃消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.LastMessageId">
            <summary>
            最后消息ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.IsSharedSubscription">
            <summary>
            是否共享订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.SharedGroupName">
            <summary>
            共享组名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.SubscriptionOptions">
            <summary>
            订阅选项（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.ConfigJson">
            <summary>
            配置JSON
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.MessageFilter">
            <summary>
            消息过滤器
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.MaxQueueLength">
            <summary>
            最大队列长度
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.CurrentQueueLength">
            <summary>
            当前队列长度
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.HandlingStrategy">
            <summary>
            处理策略（FIFO、LIFO、Priority）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.MaxRetryCount">
            <summary>
            最大重试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.LastError">
            <summary>
            最后错误信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.Statistics">
            <summary>
            统计信息（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionBaseInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.AddMqttSubscriptionInput">
            <summary>
            增加MQTT订阅关系表参数
            </summary>
        </member>
        <member name="T:Admin.NET.Application.DeleteMqttSubscriptionInput">
            <summary>
            删除MQTT订阅关系表参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeleteMqttSubscriptionInput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="T:Admin.NET.Application.UpdateMqttSubscriptionInput">
            <summary>
            更新MQTT订阅关系表参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttSubscriptionInput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="T:Admin.NET.Application.QueryByIdMqttSubscriptionInput">
            <summary>
            主键查询MQTT订阅关系表参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.QueryByIdMqttSubscriptionInput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="T:Admin.NET.Application.PageMqttSubscriptionInput">
            <summary>
            分页查询MQTT订阅关系表参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.Keyword">
            <summary>
            关键字查询
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.ClientRefId">
            <summary>
            客户端引用ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.TopicId">
            <summary>
            主题ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.QosLevel">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.Status">
            <summary>
            订阅状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.IsSharedSubscription">
            <summary>
            是否共享订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.SharedGroupName">
            <summary>
            共享组名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.SubscribedAtStart">
            <summary>
            订阅开始时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.SubscribedAtEnd">
            <summary>
            订阅结束时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.LastActivityStart">
            <summary>
            最后活动开始时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.LastActivityEnd">
            <summary>
            最后活动结束时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.HandlingStrategy">
            <summary>
            处理策略
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.SubscribedAtRange">
            <summary>
            订阅时间范围
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.UnsubscribedAtRange">
            <summary>
            取消订阅时间范围
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.LastActivityRange">
            <summary>
            最后活动时间范围
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttSubscriptionInput.LastMessageTimeRange">
            <summary>
            最后消息时间范围
            </summary>
        </member>
        <member name="T:Admin.NET.Application.SubscribeTopicInput">
            <summary>
            订阅主题参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscribeTopicInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscribeTopicInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscribeTopicInput.ClientRefId">
            <summary>
            客户端引用ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscribeTopicInput.TopicId">
            <summary>
            主题ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscribeTopicInput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscribeTopicInput.QosLevel">
            <summary>
            QoS等级（0、1、2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscribeTopicInput.IsSharedSubscription">
            <summary>
            是否共享订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscribeTopicInput.SharedGroupName">
            <summary>
            共享组名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscribeTopicInput.SubscriptionOptions">
            <summary>
            订阅选项（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscribeTopicInput.MessageFilter">
            <summary>
            消息过滤器
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscribeTopicInput.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscribeTopicInput.MaxQueueLength">
            <summary>
            最大队列长度
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscribeTopicInput.HandlingStrategy">
            <summary>
            处理策略（FIFO、LIFO、Priority）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscribeTopicInput.MaxRetryCount">
            <summary>
            最大重试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscribeTopicInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscribeTopicInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.UnsubscribeTopicInput">
            <summary>
            取消订阅主题参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.UnsubscribeTopicInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.UnsubscribeTopicInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.UnsubscribeTopicInput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.UnsubscribeTopicInput.UnsubscribeReason">
            <summary>
            取消订阅原因
            </summary>
        </member>
        <member name="T:Admin.NET.Application.BatchSubscribeInput">
            <summary>
            批量订阅主题参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchSubscribeInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchSubscribeInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchSubscribeInput.Topics">
            <summary>
            订阅主题列表
            </summary>
        </member>
        <member name="T:Admin.NET.Application.BatchSubscribeTopicItem">
            <summary>
            批量订阅主题项
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchSubscribeTopicItem.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchSubscribeTopicItem.QosLevel">
            <summary>
            QoS等级（0、1、2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchSubscribeTopicItem.MessageFilter">
            <summary>
            消息过滤器
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchSubscribeTopicItem.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="T:Admin.NET.Application.BatchUnsubscribeInput">
            <summary>
            批量取消订阅参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchUnsubscribeInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchUnsubscribeInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchUnsubscribeInput.TopicNames">
            <summary>
            主题名称列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchUnsubscribeInput.UnsubscribeReason">
            <summary>
            取消订阅原因
            </summary>
        </member>
        <member name="T:Admin.NET.Application.BatchOperationMqttSubscriptionInput">
            <summary>
            批量操作MQTT订阅关系参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchOperationMqttSubscriptionInput.Ids">
            <summary>
            主键Id列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchOperationMqttSubscriptionInput.Operation">
            <summary>
            操作类型（enable、disable、delete、activate、suspend）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchOperationMqttSubscriptionInput.Reason">
            <summary>
            操作原因
            </summary>
        </member>
        <member name="T:Admin.NET.Application.ResetSubscriptionStatsInput">
            <summary>
            重置订阅统计参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ResetSubscriptionStatsInput.Ids">
            <summary>
            主键Id列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ResetSubscriptionStatsInput.ResetType">
            <summary>
            重置类型（all、messages、errors、queue）
            </summary>
        </member>
        <member name="T:Admin.NET.Application.ExportMqttSubscriptionInput">
            <summary>
            导出MQTT订阅关系参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionInput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionInput.Status">
            <summary>
            订阅状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionInput.IsSharedSubscription">
            <summary>
            是否共享订阅
            </summary>
        </member>
        <member name="T:Admin.NET.Application.GetClientSubscriptionsInput">
            <summary>
            获取客户端订阅列表参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetClientSubscriptionsInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetClientSubscriptionsInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetClientSubscriptionsInput.IncludeStatistics">
            <summary>
            是否包含统计信息
            </summary>
        </member>
        <member name="T:Admin.NET.Application.GetTopicSubscribersInput">
            <summary>
            获取主题订阅者列表参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetTopicSubscribersInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetTopicSubscribersInput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetTopicSubscribersInput.IncludeStatistics">
            <summary>
            是否包含统计信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetTopicSubscribersInput.ActiveOnly">
            <summary>
            是否只显示活跃订阅
            </summary>
        </member>
        <member name="T:Admin.NET.Application.BatchDeleteMqttSubscriptionInput">
            <summary>
            批量删除MQTT订阅关系输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchDeleteMqttSubscriptionInput.Ids">
            <summary>
            主键Id集合
            </summary>
        </member>
        <member name="T:Admin.NET.Application.ResetSubscriptionStatisticsInput">
            <summary>
            重置订阅统计信息输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ResetSubscriptionStatisticsInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ResetSubscriptionStatisticsInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ResetSubscriptionStatisticsInput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="T:Admin.NET.Application.GetSubscriptionStatisticsInput">
            <summary>
            获取订阅统计信息输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetSubscriptionStatisticsInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetSubscriptionStatisticsInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetSubscriptionStatisticsInput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.GetSubscriptionStatisticsInput.TimeRangeHours">
            <summary>
            统计时间范围（小时）
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttSubscriptionOutput">
            <summary>
            MQTT订阅关系表输出参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.ClientRefId">
            <summary>
            客户端引用ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.TopicId">
            <summary>
            主题ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.QosLevel">
            <summary>
            QoS等级（0、1、2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.Status">
            <summary>
            订阅状态（Active、Inactive、Suspended）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.SubscribedAt">
            <summary>
            订阅时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.UnsubscribedAt">
            <summary>
            取消订阅时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.MessagesReceived">
            <summary>
            接收消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.BytesReceived">
            <summary>
            接收字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.MessagesDropped">
            <summary>
            丢弃消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.LastMessageId">
            <summary>
            最后消息ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.IsSharedSubscription">
            <summary>
            是否共享订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.SharedGroupName">
            <summary>
            共享组名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.SubscriptionOptions">
            <summary>
            订阅选项（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.MessageFilter">
            <summary>
            消息过滤器
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.MaxQueueLength">
            <summary>
            最大队列长度
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.CurrentQueueLength">
            <summary>
            当前队列长度
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.HandlingStrategy">
            <summary>
            处理策略（FIFO、LIFO、Priority）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.MaxRetryCount">
            <summary>
            最大重试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.LastError">
            <summary>
            最后错误信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.Statistics">
            <summary>
            统计信息（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.ConfigJson">
            <summary>
            配置信息（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.CreateUserId">
            <summary>
            创建用户ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttSubscriptionOutput.UpdateUserId">
            <summary>
            更新用户ID
            </summary>
        </member>
        <member name="T:Admin.NET.Application.SubscriptionOperationOutput">
            <summary>
            订阅操作结果输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionOperationOutput.IsSuccess">
            <summary>
            操作是否成功
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionOperationOutput.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionOperationOutput.SubscriptionId">
            <summary>
            订阅ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionOperationOutput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionOperationOutput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionOperationOutput.QosLevel">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionOperationOutput.OperationTime">
            <summary>
            操作时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.BatchSubscriptionOperationOutput">
            <summary>
            批量订阅操作结果输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchSubscriptionOperationOutput.TotalCount">
            <summary>
            总数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchSubscriptionOperationOutput.SuccessCount">
            <summary>
            成功数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchSubscriptionOperationOutput.FailedCount">
            <summary>
            失败数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchSubscriptionOperationOutput.Results">
            <summary>
            操作结果详情
            </summary>
        </member>
        <member name="P:Admin.NET.Application.BatchSubscriptionOperationOutput.OperationTime">
            <summary>
            操作时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.ClientSubscriptionsOutput">
            <summary>
            客户端订阅列表输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionsOutput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionsOutput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionsOutput.TotalSubscriptions">
            <summary>
            总订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionsOutput.ActiveSubscriptions">
            <summary>
            活跃订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionsOutput.SuspendedSubscriptions">
            <summary>
            暂停订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionsOutput.Subscriptions">
            <summary>
            订阅列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionsOutput.Statistics">
            <summary>
            统计信息
            </summary>
        </member>
        <member name="T:Admin.NET.Application.ClientSubscriptionStatistics">
            <summary>
            客户端订阅统计信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionStatistics.TotalMessagesReceived">
            <summary>
            总接收消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionStatistics.TotalBytesReceived">
            <summary>
            总接收字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionStatistics.TotalMessagesDropped">
            <summary>
            总丢弃消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionStatistics.AverageQosLevel">
            <summary>
            平均QoS等级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionStatistics.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionStatistics.QosDistribution">
            <summary>
            QoS分布
            </summary>
        </member>
        <member name="T:Admin.NET.Application.TopicSubscribersOutput">
            <summary>
            主题订阅者列表输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscribersOutput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscribersOutput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscribersOutput.TotalSubscribers">
            <summary>
            总订阅者数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscribersOutput.ActiveSubscribers">
            <summary>
            活跃订阅者数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscribersOutput.SharedGroupCount">
            <summary>
            共享订阅组数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscribersOutput.Subscribers">
            <summary>
            订阅者列表
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscribersOutput.Statistics">
            <summary>
            统计信息
            </summary>
        </member>
        <member name="T:Admin.NET.Application.TopicSubscriberInfo">
            <summary>
            主题订阅者信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriberInfo.SubscriptionId">
            <summary>
            订阅ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriberInfo.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriberInfo.QosLevel">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriberInfo.Status">
            <summary>
            订阅状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriberInfo.IsSharedSubscription">
            <summary>
            是否共享订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriberInfo.SharedGroupName">
            <summary>
            共享组名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriberInfo.SubscribedAt">
            <summary>
            订阅时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriberInfo.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriberInfo.MessagesReceived">
            <summary>
            接收消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriberInfo.CurrentQueueLength">
            <summary>
            当前队列长度
            </summary>
        </member>
        <member name="T:Admin.NET.Application.TopicSubscriptionStatistics">
            <summary>
            主题订阅统计信息
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriptionStatistics.TotalMessagesReceived">
            <summary>
            总接收消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriptionStatistics.TotalBytesReceived">
            <summary>
            总接收字节数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriptionStatistics.TotalMessagesDropped">
            <summary>
            总丢弃消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriptionStatistics.AverageQosLevel">
            <summary>
            平均QoS等级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriptionStatistics.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriptionStatistics.QosDistribution">
            <summary>
            QoS分布
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriptionStatistics.SharedGroupDistribution">
            <summary>
            共享组分布
            </summary>
        </member>
        <member name="T:Admin.NET.Application.SubscriptionStatisticsOutput">
            <summary>
            订阅统计信息输出
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionStatisticsOutput.TotalSubscriptions">
            <summary>
            总订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionStatisticsOutput.ActiveSubscriptions">
            <summary>
            活跃订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionStatisticsOutput.SuspendedSubscriptions">
            <summary>
            暂停订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionStatisticsOutput.SharedSubscriptions">
            <summary>
            共享订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionStatisticsOutput.TodayNewSubscriptions">
            <summary>
            今日新增订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionStatisticsOutput.TodayUnsubscriptions">
            <summary>
            今日取消订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionStatisticsOutput.QosDistribution">
            <summary>
            QoS分布
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionStatisticsOutput.StatusDistribution">
            <summary>
            状态分布
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionStatisticsOutput.PopularTopics">
            <summary>
            热门主题（按订阅数排序）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.SubscriptionStatisticsOutput.ActiveClients">
            <summary>
            活跃客户端（按订阅数排序）
            </summary>
        </member>
        <member name="T:Admin.NET.Application.TopicSubscriptionCount">
            <summary>
            主题订阅数统计
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriptionCount.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriptionCount.SubscriptionCount">
            <summary>
            订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.TopicSubscriptionCount.ActiveCount">
            <summary>
            活跃订阅数
            </summary>
        </member>
        <member name="T:Admin.NET.Application.ClientSubscriptionCount">
            <summary>
            客户端订阅数统计
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionCount.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionCount.SubscriptionCount">
            <summary>
            订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionCount.ActiveCount">
            <summary>
            活跃订阅数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ClientSubscriptionCount.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="T:Admin.NET.Application.ExportMqttSubscriptionOutput">
            <summary>
            导出MQTT订阅关系表参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionOutput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionOutput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionOutput.QosLevel">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionOutput.Status">
            <summary>
            订阅状态
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionOutput.IsSharedSubscription">
            <summary>
            是否共享订阅
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionOutput.SharedGroupName">
            <summary>
            共享组名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionOutput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionOutput.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionOutput.MessagesReceived">
            <summary>
            接收消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionOutput.MessagesDropped">
            <summary>
            丢弃消息数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionOutput.SubscribedAt">
            <summary>
            订阅时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionOutput.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.ExportMqttSubscriptionOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttSubscriptionService">
            <summary>
            MQTT订阅关系服务
            </summary>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.Page(Admin.NET.Application.PageMqttSubscriptionInput)">
            <summary>
            分页查询MQTT订阅关系
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.Add(Admin.NET.Application.AddMqttSubscriptionInput)">
            <summary>
            增加MQTT订阅关系
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.Delete(Admin.NET.Application.DeleteMqttSubscriptionInput)">
            <summary>
            删除MQTT订阅关系
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.Update(Admin.NET.Application.UpdateMqttSubscriptionInput)">
            <summary>
            更新MQTT订阅关系
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.Detail(Admin.NET.Application.QueryByIdMqttSubscriptionInput)">
            <summary>
            获取MQTT订阅关系详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.List(Admin.NET.Application.MqttSubscriptionInput)">
            <summary>
            获取MQTT订阅关系列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.BatchDelete(Admin.NET.Application.BatchDeleteMqttSubscriptionInput)">
            <summary>
            批量删除MQTT订阅关系
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.Subscribe(Admin.NET.Application.SubscribeTopicInput)">
            <summary>
            订阅主题
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.Unsubscribe(Admin.NET.Application.UnsubscribeTopicInput)">
            <summary>
            取消订阅主题
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.BatchSubscribe(Admin.NET.Application.BatchSubscribeInput)">
            <summary>
            批量订阅主题
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.BatchUnsubscribe(Admin.NET.Application.BatchUnsubscribeInput)">
            <summary>
            批量取消订阅主题
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.GetClientSubscriptions(Admin.NET.Application.GetClientSubscriptionsInput)">
            <summary>
            获取客户端订阅列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.GetTopicSubscribers(Admin.NET.Application.GetTopicSubscribersInput)">
            <summary>
            获取主题订阅者列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.ResetStatistics(Admin.NET.Application.ResetSubscriptionStatisticsInput)">
            <summary>
            重置订阅统计信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.GetStatistics(Admin.NET.Application.GetSubscriptionStatisticsInput)">
            <summary>
            获取订阅统计信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.Export(Admin.NET.Application.ExportMqttSubscriptionInput)">
            <summary>
            导出MQTT订阅关系
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttSubscriptionService.ClearRelatedCache(System.String,System.String,System.Nullable{System.Int64})">
            <summary>
            清除相关缓存
            </summary>
            <param name="clientId"></param>
            <param name="topicName"></param>
            <param name="instanceId"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Application.MqttTopicDto">
            <summary>
            MQTT 主题信息表输出参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.TopicType">
            <summary>
            主题类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.QosLevel">
            <summary>
            QoS等级（0,1,2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.RetainMessage">
            <summary>
            是否保留消息（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.SubscriptionCount">
            <summary>
            订阅数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.MessageCount">
            <summary>
            消息数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.ByteCount">
            <summary>
            字节数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.LastMessageContent">
            <summary>
            最后消息内容
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.MessageExpiry">
            <summary>
            消息过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.AllowPublish">
            <summary>
            允许发布（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.AllowSubscribe">
            <summary>
            允许订阅（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.PublishPermissions">
            <summary>
            发布权限（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.SubscribePermissions">
            <summary>
            订阅权限（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.IsRetained">
            <summary>
            是否保留（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.TopicTags">
            <summary>
            主题标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.IsSystemTopic">
            <summary>
            是否系统主题（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.MonitorConfig">
            <summary>
            监控配置（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.AlertRules">
            <summary>
            告警规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.ForwardRules">
            <summary>
            转发规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.CreateUserId">
            <summary>
            创建者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.UpdateUserId">
            <summary>
            修改者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.IsDelete">
            <summary>
            软删除
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.CreateUserName">
            <summary>
            创建者姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicDto.UpdateUserName">
            <summary>
            修改者姓名
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttTopicBaseInput">
            <summary>
            MQTT 主题信息表基础输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.TopicType">
            <summary>
            主题类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.QosLevel">
            <summary>
            QoS等级（0,1,2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.RetainMessage">
            <summary>
            是否保留消息（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.SubscriptionCount">
            <summary>
            订阅数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.MessageCount">
            <summary>
            消息数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.ByteCount">
            <summary>
            字节数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.LastMessageContent">
            <summary>
            最后消息内容
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.MessageExpiry">
            <summary>
            消息过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.AllowPublish">
            <summary>
            允许发布（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.AllowSubscribe">
            <summary>
            允许订阅（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.PublishPermissions">
            <summary>
            发布权限（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.SubscribePermissions">
            <summary>
            订阅权限（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.IsRetained">
            <summary>
            是否保留（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.TopicTags">
            <summary>
            主题标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.IsSystemTopic">
            <summary>
            是否系统主题（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.MonitorConfig">
            <summary>
            监控配置（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.AlertRules">
            <summary>
            告警规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.ForwardRules">
            <summary>
            转发规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicBaseInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.PageMqttTopicInput">
            <summary>
            MQTT 主题信息表分页查询输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.TopicType">
            <summary>
            主题类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.QosLevel">
            <summary>
            QoS等级（0,1,2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.RetainMessage">
            <summary>
            是否保留消息（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.SubscriptionCount">
            <summary>
            订阅数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.MessageCount">
            <summary>
            消息数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.ByteCount">
            <summary>
            字节数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.LastMessageTimeRange">
            <summary>
            最后消息时间范围
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.LastMessageContent">
            <summary>
            最后消息内容
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.MessageExpiry">
            <summary>
            消息过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.AllowPublish">
            <summary>
            允许发布（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.AllowSubscribe">
            <summary>
            允许订阅（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.PublishPermissions">
            <summary>
            发布权限（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.SubscribePermissions">
            <summary>
            订阅权限（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.IsRetained">
            <summary>
            是否保留（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.TopicTags">
            <summary>
            主题标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.IsSystemTopic">
            <summary>
            是否系统主题（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.MonitorConfig">
            <summary>
            监控配置（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.AlertRules">
            <summary>
            告警规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.ForwardRules">
            <summary>
            转发规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.PageMqttTopicInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.AddMqttTopicInput">
            <summary>
            MQTT 主题信息表增加输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.TopicType">
            <summary>
            主题类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.QosLevel">
            <summary>
            QoS等级（0,1,2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.RetainMessage">
            <summary>
            是否保留消息（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.SubscriptionCount">
            <summary>
            订阅数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.MessageCount">
            <summary>
            消息数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.ByteCount">
            <summary>
            字节数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.LastMessageContent">
            <summary>
            最后消息内容
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.MessageExpiry">
            <summary>
            消息过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.AllowPublish">
            <summary>
            允许发布（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.AllowSubscribe">
            <summary>
            允许订阅（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.PublishPermissions">
            <summary>
            发布权限（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.SubscribePermissions">
            <summary>
            订阅权限（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.IsRetained">
            <summary>
            是否保留（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.TopicTags">
            <summary>
            主题标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.IsSystemTopic">
            <summary>
            是否系统主题（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.MonitorConfig">
            <summary>
            监控配置（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.AlertRules">
            <summary>
            告警规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.ForwardRules">
            <summary>
            转发规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.AddMqttTopicInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Application.DeleteMqttTopicInput">
            <summary>
            MQTT 主题信息表删除输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.DeleteMqttTopicInput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="T:Admin.NET.Application.UpdateMqttTopicInput">
            <summary>
            MQTT 主题信息表更新输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.Id">
            <summary>
            主键Id
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.InstanceId">
            <summary>
            实例ID
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.TopicName">
            <summary>
            主题名称
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.TopicType">
            <summary>
            主题类型
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.QosLevel">
            <summary>
            QoS等级（0,1,2）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.RetainMessage">
            <summary>
            是否保留消息（0:否，1:是）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.SubscriptionCount">
            <summary>
            订阅数量
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.MessageCount">
            <summary>
            消息数量
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.ByteCount">
            <summary>
            字节数量
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.LastMessageTime">
            <summary>
            最后消息时间
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.LastMessageContent">
            <summary>
            最后消息内容
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.MessageExpiry">
            <summary>
            消息过期时间（秒）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.AllowPublish">
            <summary>
            允许发布（0:否，1:是）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.AllowSubscribe">
            <summary>
            允许订阅（0:否，1:是）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.PublishPermissions">
            <summary>
            发布权限（JSON）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.SubscribePermissions">
            <summary>
            订阅权限（JSON）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.IsRetained">
            <summary>
            是否保留（0:否，1:是）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.Description">
            <summary>
            描述
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.TopicTags">
            <summary>
            主题标签（JSON）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.IsSystemTopic">
            <summary>
            是否系统主题（0:否，1:是）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.MonitorConfig">
            <summary>
            监控配置（JSON）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.AlertRules">
            <summary>
            告警规则（JSON）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.ForwardRules">
            <summary>
            转发规则（JSON）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>    
        </member>
        <member name="P:Admin.NET.Application.UpdateMqttTopicInput.Remark">
            <summary>
            备注
            </summary>    
        </member>
        <member name="T:Admin.NET.Application.QueryByIdMqttTopicInput">
            <summary>
            MQTT 主题信息表主键查询输入参数
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttTopicOutput">
            <summary>
            MQTT 主题信息表输出参数
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.InstanceId">
            <summary>
            实例ID
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.TopicName">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.TopicType">
            <summary>
            主题类型
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.QosLevel">
            <summary>
            QoS等级（0,1,2）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.RetainMessage">
            <summary>
            是否保留消息（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.SubscriptionCount">
            <summary>
            订阅数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.MessageCount">
            <summary>
            消息数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.ByteCount">
            <summary>
            字节数量
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.LastMessageContent">
            <summary>
            最后消息内容
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.MessageExpiry">
            <summary>
            消息过期时间（秒）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.AllowPublish">
            <summary>
            允许发布（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.AllowSubscribe">
            <summary>
            允许订阅（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.PublishPermissions">
            <summary>
            发布权限（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.SubscribePermissions">
            <summary>
            订阅权限（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.IsRetained">
            <summary>
            是否保留（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.TopicTags">
            <summary>
            主题标签（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.IsEnabled">
            <summary>
            是否启用（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.IsSystemTopic">
            <summary>
            是否系统主题（0:否，1:是）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.MonitorConfig">
            <summary>
            监控配置（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.AlertRules">
            <summary>
            告警规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.ForwardRules">
            <summary>
            转发规则（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.ExtendedProperties">
            <summary>
            扩展属性（JSON）
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.CreateUserId">
            <summary>
            创建者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.UpdateUserId">
            <summary>
            修改者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.IsDelete">
            <summary>
            软删除
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.CreateUserName">
            <summary>
            创建者姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Application.MqttTopicOutput.UpdateUserName">
            <summary>
            修改者姓名
            </summary>
        </member>
        <member name="T:Admin.NET.Application.MqttTopicService">
            <summary>
            MQTT 主题信息表服务 🧩
            </summary>
        </member>
        <member name="M:Admin.NET.Application.MqttTopicService.Page(Admin.NET.Application.PageMqttTopicInput)">
            <summary>
            分页查询MQTT 主题信息表 🔖
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttTopicService.Detail(Admin.NET.Application.QueryByIdMqttTopicInput)">
            <summary>
            获取MQTT 主题信息表详情 ℹ️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttTopicService.Add(Admin.NET.Application.AddMqttTopicInput)">
            <summary>
            增加MQTT 主题信息表 ➕
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttTopicService.Update(Admin.NET.Application.UpdateMqttTopicInput)">
            <summary>
            更新MQTT 主题信息表 ✏️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttTopicService.Delete(Admin.NET.Application.DeleteMqttTopicInput)">
            <summary>
            删除MQTT 主题信息表 ❌
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Application.MqttTopicService.BatchDelete(System.Collections.Generic.List{Admin.NET.Application.DeleteMqttTopicInput})">
            <summary>
            批量删除MQTT 主题信息表 ❌
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
    </members>
</doc>
