import {useBaseApi} from '/@@/api/base';

// @(Model.BusName)接口服务
export const use@(Model.ClassName)Api = () => {
	const baseApi = useBaseApi("@(Model.LowerClassName)");
	return {
		// 分页查询@(Model.BusName)
		page: baseApi.page,
		// 查看@(Model.BusName)详细
		detail: baseApi.detail,
		// 新增@(Model.BusName)
		add: baseApi.add,
		// 更新@(Model.BusName)
		update: baseApi.update,
		@if (Model.HasSetStatus) {
		@:// 设置@(Model.BusName)状态
		@:setStatus: baseApi.setStatus,
		}
		// 删除@(Model.BusName)
		delete: baseApi.delete,
		// 批量删除@(Model.BusName)
		batchDelete: baseApi.batchDelete,
		@if (Model.ImportFieldList.Count > 0) {
		@:// 导出@(Model.BusName)数据
		@:exportData: baseApi.exportData,
		@:// 导入@(Model.BusName)数据
		@:importData: baseApi.importData,
		@:// 下载@(Model.BusName)数据导入模板
		@:downloadTemplate: baseApi.downloadTemplate,
		}
		@if (Model.DropdownFieldList.Count > 0) {
		@:// 获取下拉列表数据
		@:getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
		}
		@foreach (var column in Model.UploadFieldList) {
		@:// 上传@(column.ColumnComment)
		@:upload@(column.PropertyName): (params: any, cancel: boolean = false) => baseApi.uploadFile(params, 'upload@(column.PropertyName)', cancel),
		}
	}
}

// @(Model.BusName)实体
export interface @(Model.ClassName) {
@{
var typeMap = new Dictionary<string, string>() {
	{ "bool", "boolean" },
	{ "int", "number" },
	{ "long", "number" },
	{ "double", "number" },
	{ "float", "number" },
	{ "decimal", "number" },
	{ "byte", "number" }
};
foreach (var column in Model.TableField) {
	@:// @(column.ColumnComment)
	@:@(column.LowerPropertyName)@(column.WhetherRequired == "Y" ? "?" : ""): @(Regex.IsMatch("@(column.DataType.Trim('?'))", ".*?Enum") ? "number" : typeMap.GetValueOrDefault(column.DataType.Trim('?').ToLower(), "string"));
}
}
}