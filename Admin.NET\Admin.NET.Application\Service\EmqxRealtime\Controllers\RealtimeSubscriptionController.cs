using Admin.NET.Application.Service.EmqxRealtime.DTOs;
using Admin.NET.Application.Service.EmqxRealtime.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Admin.NET.Application.Service.EmqxRealtime.Controllers;

/// <summary>
/// 实时订阅控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class RealtimeSubscriptionController : ControllerBase
{
    private readonly ILogger<RealtimeSubscriptionController> _logger;
    private readonly IRealtimeSubscriptionService _subscriptionService;
    private readonly IDeviceGroupService _deviceGroupService;
    private readonly IDataAggregationService _aggregationService;
    private readonly IRedisCacheService _cacheService;

    public RealtimeSubscriptionController(
        ILogger<RealtimeSubscriptionController> logger,
        IRealtimeSubscriptionService subscriptionService,
        IDeviceGroupService deviceGroupService,
        IDataAggregationService aggregationService,
        IRedisCacheService cacheService)
    {
        _logger = logger;
        _subscriptionService = subscriptionService;
        _deviceGroupService = deviceGroupService;
        _aggregationService = aggregationService;
        _cacheService = cacheService;
    }

    /// <summary>
    /// 获取活跃连接列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("connections")]
    public async Task<IActionResult> GetActiveConnections()
    {
        try
        {
            var connections = await _subscriptionService.GetActiveConnectionsAsync();
            return Ok(new { success = true, data = connections });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取活跃连接失败");
            return StatusCode(500, new { success = false, message = "获取活跃连接失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 获取订阅统计信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("statistics")]
    public async Task<IActionResult> GetStatistics()
    {
        try
        {
            var statistics = await _subscriptionService.GetStatisticsAsync();
            return Ok(new { success = true, data = statistics });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取统计信息失败");
            return StatusCode(500, new { success = false, message = "获取统计信息失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 获取历史消息
    /// </summary>
    /// <param name="instanceId">实例ID</param>
    /// <param name="topics">主题列表</param>
    /// <param name="count">消息数量</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <returns></returns>
    [HttpGet("history")]
    public async Task<IActionResult> GetHistoryMessages(
        [FromQuery] int instanceId,
        [FromQuery] string[]? topics = null,
        [FromQuery] int count = 100,
        [FromQuery] DateTime? startTime = null,
        [FromQuery] DateTime? endTime = null)
    {
        try
        {
            if (instanceId <= 0)
            {
                return BadRequest(new { success = false, message = "无效的实例ID" });
            }

            if (count <= 0 || count > 1000)
            {
                return BadRequest(new { success = false, message = "消息数量必须在1-1000之间" });
            }

            var topicList = topics?.ToList() ?? new List<string>();
            var messages = await _aggregationService.GetHistoryMessagesAsync(
                instanceId, topicList, count, startTime, endTime);

            return Ok(new { success = true, data = messages });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取历史消息失败");
            return StatusCode(500, new { success = false, message = "获取历史消息失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 手动触发数据聚合
    /// </summary>
    /// <returns></returns>
    [HttpPost("aggregate")]
    public async Task<IActionResult> TriggerAggregation()
    {
        try
        {
            await _aggregationService.TriggerAggregationAsync();
            return Ok(new { success = true, message = "数据聚合已触发" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "触发数据聚合失败");
            return StatusCode(500, new { success = false, message = "触发数据聚合失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 获取聚合统计信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("aggregation/statistics")]
    public async Task<IActionResult> GetAggregationStatistics()
    {
        try
        {
            var statistics = await _aggregationService.GetAggregationStatisticsAsync();
            return Ok(new { success = true, data = statistics });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取聚合统计信息失败");
            return StatusCode(500, new { success = false, message = "获取聚合统计信息失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 启动自动聚合
    /// </summary>
    /// <returns></returns>
    [HttpPost("aggregation/start")]
    public async Task<IActionResult> StartAutoAggregation()
    {
        try
        {
            await _aggregationService.StartAutoAggregationAsync();
            return Ok(new { success = true, message = "自动聚合已启动" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动自动聚合失败");
            return StatusCode(500, new { success = false, message = "启动自动聚合失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 停止自动聚合
    /// </summary>
    /// <returns></returns>
    [HttpPost("aggregation/stop")]
    public async Task<IActionResult> StopAutoAggregation()
    {
        try
        {
            await _aggregationService.StopAutoAggregationAsync();
            return Ok(new { success = true, message = "自动聚合已停止" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止自动聚合失败");
            return StatusCode(500, new { success = false, message = "停止自动聚合失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 清理过期历史消息
    /// </summary>
    /// <param name="days">保留天数</param>
    /// <returns></returns>
    [HttpDelete("history/cleanup")]
    public async Task<IActionResult> CleanupExpiredMessages([FromQuery] int days = 30)
    {
        try
        {
            if (days <= 0 || days > 365)
            {
                return BadRequest(new { success = false, message = "保留天数必须在1-365之间" });
            }

            var deletedCount = await _aggregationService.CleanupExpiredMessagesAsync(days);
            return Ok(new { success = true, message = $"已清理 {deletedCount} 条过期消息" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期消息失败");
            return StatusCode(500, new { success = false, message = "清理过期消息失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 清空聚合队列
    /// </summary>
    /// <returns></returns>
    [HttpDelete("aggregation/queue")]
    public async Task<IActionResult> ClearAggregationQueue()
    {
        try
        {
            await _aggregationService.ClearQueueAsync();
            return Ok(new { success = true, message = "聚合队列已清空" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清空聚合队列失败");
            return StatusCode(500, new { success = false, message = "清空聚合队列失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("cache/statistics")]
    public async Task<IActionResult> GetCacheStatistics()
    {
        try
        {
            var statistics = await _cacheService.GetStatisticsAsync("cache_stats");
            return Ok(new { success = true, data = statistics });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取缓存统计信息失败");
            return StatusCode(500, new { success = false, message = "获取缓存统计信息失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 清理缓存
    /// </summary>
    /// <param name="pattern">键模式</param>
    /// <returns></returns>
    [HttpDelete("cache/cleanup")]
    public async Task<IActionResult> CleanupCache([FromQuery] string pattern = "*")
    {
        try
        {
            // 注意：这里需要谨慎处理，避免误删重要缓存
            if (string.IsNullOrEmpty(pattern) || pattern == "*")
            {
                return BadRequest(new { success = false, message = "请指定具体的缓存键模式" });
            }

            // 这里可以根据需要实现批量删除逻辑
            return Ok(new { success = true, message = "缓存清理功能需要进一步实现" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理缓存失败");
            return StatusCode(500, new { success = false, message = "清理缓存失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 健康检查
    /// </summary>
    /// <returns></returns>
    [HttpGet("health")]
    [AllowAnonymous]
    public IActionResult HealthCheck()
    {
        try
        {
            return Ok(new 
            { 
                success = true, 
                status = "healthy", 
                timestamp = DateTime.Now,
                version = "1.0.0"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "健康检查失败");
            return StatusCode(500, new { success = false, status = "unhealthy", details = ex.Message });
        }
    }
}