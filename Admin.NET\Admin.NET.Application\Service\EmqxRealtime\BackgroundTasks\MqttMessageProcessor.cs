using Admin.NET.Application.Service.EmqxRealtime.DTOs;
using Admin.NET.Application.Service.EmqxRealtime.Interfaces;
using Admin.NET.Application.Entity;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Extensions.ManagedClient;
using System.Collections.Concurrent;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace Admin.NET.Application.Service.EmqxRealtime.BackgroundTasks;

/// <summary>
/// MQTT消息处理后台服务
/// </summary>
public class MqttMessageProcessor : BackgroundService
{
    private readonly ILogger<MqttMessageProcessor> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly IHubContext<Hubs.RealtimeSubscriptionHub> _hubContext;
    private readonly Configuration.MqttOptions _mqttOptions;
    private IMqttClient? _mqttClient;
    private readonly SemaphoreSlim _reconnectSemaphore = new(1, 1);
    private bool _isReconnecting = false;

    public MqttMessageProcessor(
        ILogger<MqttMessageProcessor> logger,
        IServiceProvider serviceProvider,
        IHubContext<Hubs.RealtimeSubscriptionHub> hubContext,
        IOptions<Configuration.MqttOptions> mqttOptions)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _hubContext = hubContext;
        _mqttOptions = mqttOptions.Value;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("MQTT消息处理服务启动");

        try
        {
            await InitializeMqttClientAsync();
            await ConnectToMqttBrokerAsync();

            // 保持服务运行
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(5000, stoppingToken);
                
                // 检查连接状态
                if (_mqttClient?.IsConnected != true && !_isReconnecting)
                {
                    _ = Task.Run(async () => await ReconnectAsync(), stoppingToken);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MQTT消息处理服务执行失败");
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("MQTT消息处理服务停止");

        if (_mqttClient?.IsConnected == true)
        {
            await _mqttClient.DisconnectAsync();
        }

        _mqttClient?.Dispose();
        _reconnectSemaphore.Dispose();

        await base.StopAsync(cancellationToken);
    }

    private async Task InitializeMqttClientAsync()
    {
        var factory = new MqttFactory();
        _mqttClient = factory.CreateMqttClient();

        // 配置事件处理
        _mqttClient.ApplicationMessageReceivedAsync += OnMessageReceivedAsync;
        _mqttClient.ConnectedAsync += OnConnectedAsync;
        _mqttClient.DisconnectedAsync += OnDisconnectedAsync;
        _mqttClient.ConnectingFailedAsync += OnConnectingFailedAsync;

        _logger.LogInformation("MQTT客户端初始化完成");
    }

    private async Task ConnectToMqttBrokerAsync()
    {
        try
        {
            var options = new MqttClientOptionsBuilder()
                .WithTcpServer(_mqttOptions.Server, _mqttOptions.Port)
                .WithCredentials(_mqttOptions.Username, _mqttOptions.Password)
                .WithClientId(_mqttOptions.ClientId ?? $"mqtt-processor-{Environment.MachineName}-{Guid.NewGuid():N}")
                .WithCleanSession(_mqttOptions.CleanSession)
                .WithKeepAlivePeriod(TimeSpan.FromSeconds(_mqttOptions.KeepAliveInterval))
                .WithTimeout(TimeSpan.FromSeconds(_mqttOptions.ConnectTimeout))
                .Build();

            if (_mqttOptions.UseSsl)
            {
                options.ChannelOptions = new MQTTnet.Client.Options.MqttClientTcpOptions
                {
                    TlsOptions = new MQTTnet.Client.Options.MqttClientTlsOptions
                    {
                        UseTls = true
                    }
                };
            }

            await _mqttClient!.ConnectAsync(options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接MQTT代理失败");
            throw;
        }
    }

    private async Task ReconnectAsync()
    {
        if (!await _reconnectSemaphore.WaitAsync(100))
            return;

        try
        {
            _isReconnecting = true;
            _logger.LogWarning("开始重连MQTT代理");

            var retryCount = 0;
            var maxRetries = _mqttOptions.MaxReconnectAttempts;
            var retryDelay = _mqttOptions.ReconnectDelay;

            while (retryCount < maxRetries && _mqttClient?.IsConnected != true)
            {
                try
                {
                    await Task.Delay(retryDelay * 1000);
                    await ConnectToMqttBrokerAsync();
                    
                    if (_mqttClient?.IsConnected == true)
                    {
                        _logger.LogInformation("MQTT代理重连成功");
                        break;
                    }
                }
                catch (Exception ex)
                {
                    retryCount++;
                    _logger.LogWarning(ex, "MQTT重连失败，第 {RetryCount}/{MaxRetries} 次尝试", retryCount, maxRetries);
                    
                    if (retryCount >= maxRetries)
                    {
                        _logger.LogError("MQTT重连达到最大尝试次数，停止重连");
                        break;
                    }
                }
            }
        }
        finally
        {
            _isReconnecting = false;
            _reconnectSemaphore.Release();
        }
    }

    private async Task OnMessageReceivedAsync(MqttApplicationMessageReceivedEventArgs e)
    {
        try
        {
            var topic = e.ApplicationMessage.Topic;
            var payload = e.ApplicationMessage.Payload;
            var qos = (int)e.ApplicationMessage.QualityOfServiceLevel;
            var retain = e.ApplicationMessage.Retain;
            var timestamp = DateTime.Now;

            // 解析消息内容
            var content = payload != null ? Encoding.UTF8.GetString(payload) : string.Empty;
            
            // 提取设备ID（从主题中提取，假设主题格式为 device/{deviceId}/...）
            var deviceId = ExtractDeviceIdFromTopic(topic);

            // 创建消息对象
            var message = new MqttMessageHistory
            {
                Id = Guid.NewGuid().ToString(),
                Topic = topic,
                Payload = content,
                QoS = qos,
                Retain = retain,
                Timestamp = timestamp,
                InstanceId = 1, // 这里可以根据实际情况设置
                DeviceId = deviceId,
                MessageSize = payload?.Length ?? 0,
                ClientId = e.ClientId,
                CreateTime = timestamp,
                IsAggregated = false
            };

            // 使用作用域服务处理消息
            using var scope = _serviceProvider.CreateScope();
            await ProcessMessageAsync(scope.ServiceProvider, message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理MQTT消息失败: Topic={Topic}", e.ApplicationMessage.Topic);
        }
    }

    private async Task ProcessMessageAsync(IServiceProvider serviceProvider, MqttMessageHistory message)
    {
        try
        {
            var subscriptionService = serviceProvider.GetRequiredService<IRealtimeSubscriptionService>();
            var deviceGroupService = serviceProvider.GetRequiredService<IDeviceGroupService>();
            var aggregationService = serviceProvider.GetRequiredService<IDataAggregationService>();

            // 匹配设备分组
            if (!string.IsNullOrEmpty(message.DeviceId))
            {
                var deviceGroup = await deviceGroupService.GetGroupByDeviceIdAsync(message.DeviceId);
                if (deviceGroup != null)
                {
                    message.DeviceGroupId = deviceGroup.Id;
                }
            }

            // 如果没有通过设备ID匹配到分组，尝试通过主题匹配
            if (!message.DeviceGroupId.HasValue)
            {
                var deviceGroup = await deviceGroupService.GetGroupByTopicAsync(message.Topic);
                if (deviceGroup != null)
                {
                    message.DeviceGroupId = deviceGroup.Id;
                }
            }

            // 处理实时订阅
            await subscriptionService.ProcessMqttMessageAsync(message);

            // 添加到聚合队列
            await aggregationService.AddMessageToQueueAsync(message);

            // 发送到SignalR客户端
            await SendMessageToSubscribers(message);

            _logger.LogDebug("MQTT消息处理完成: Topic={Topic}, DeviceId={DeviceId}, GroupId={GroupId}", 
                message.Topic, message.DeviceId, message.DeviceGroupId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理消息业务逻辑失败: Topic={Topic}", message.Topic);
        }
    }

    private async Task SendMessageToSubscribers(MqttMessageHistory message)
    {
        try
        {
            // 创建消息DTO
            var messageDto = new MessageDto
            {
                Id = message.Id,
                Topic = message.Topic,
                Payload = message.Payload,
                QoS = message.QoS,
                Retain = message.Retain,
                Timestamp = message.Timestamp,
                InstanceId = message.InstanceId,
                DeviceId = message.DeviceId,
                DeviceGroup = message.DeviceGroupId?.ToString(),
                MessageSize = message.MessageSize,
                ClientId = message.ClientId
            };

            // 发送到所有订阅的客户端
            await _hubContext.Clients.All.SendAsync("MessageReceived", messageDto);

            // 如果有设备分组，发送到分组订阅者
            if (message.DeviceGroupId.HasValue)
            {
                var groupName = $"DeviceGroup_{message.DeviceGroupId}";
                await _hubContext.Clients.Group(groupName).SendAsync("GroupMessageReceived", messageDto);
            }

            // 发送到主题订阅者
            var topicGroup = $"Topic_{message.Topic.Replace("/", "_").Replace("+", "_plus_").Replace("#", "_hash_")}";
            await _hubContext.Clients.Group(topicGroup).SendAsync("TopicMessageReceived", messageDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送消息到SignalR客户端失败: Topic={Topic}", message.Topic);
        }
    }

    private string? ExtractDeviceIdFromTopic(string topic)
    {
        try
        {
            // 假设主题格式为: device/{deviceId}/... 或 {deviceId}/...
            var parts = topic.Split('/');
            
            if (parts.Length >= 2 && parts[0].Equals("device", StringComparison.OrdinalIgnoreCase))
            {
                return parts[1];
            }
            
            if (parts.Length >= 1)
            {
                return parts[0];
            }

            return null;
        }
        catch
        {
            return null;
        }
    }

    private async Task OnConnectedAsync(MqttClientConnectedEventArgs e)
    {
        _logger.LogInformation("MQTT客户端连接成功: {ResultCode}", e.ConnectResult.ResultCode);

        try
        {
            // 订阅所有主题
            var subscribeOptions = new MqttClientSubscribeOptionsBuilder()
                .WithTopicFilter("#", MQTTnet.Protocol.MqttQualityOfServiceLevel.AtMostOnce)
                .Build();

            await _mqttClient!.SubscribeAsync(subscribeOptions);
            _logger.LogInformation("已订阅所有MQTT主题");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "订阅MQTT主题失败");
        }
    }

    private Task OnDisconnectedAsync(MqttClientDisconnectedEventArgs e)
    {
        _logger.LogWarning("MQTT客户端连接断开: {Reason}, Exception: {Exception}", 
            e.Reason, e.Exception?.Message);
        return Task.CompletedTask;
    }

    private Task OnConnectingFailedAsync(ConnectingFailedEventArgs e)
    {
        _logger.LogError(e.Exception, "MQTT客户端连接失败");
        return Task.CompletedTask;
    }
}