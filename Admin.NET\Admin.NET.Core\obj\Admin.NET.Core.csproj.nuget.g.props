﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;D:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="D:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ItemGroup Condition=" '$(TargetFramework)' == 'net8.0' AND '$(ExcludeRestorePackageImports)' != 'true' ">
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\Microsoft.CSharp.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\Microsoft.CSharp.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\Microsoft.CSharp.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\Microsoft.CSharp.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\Microsoft.VisualBasic.Core.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\Microsoft.VisualBasic.Core.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\Microsoft.VisualBasic.Core.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\Microsoft.VisualBasic.Core.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\Microsoft.VisualBasic.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\Microsoft.VisualBasic.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\Microsoft.VisualBasic.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\Microsoft.VisualBasic.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\Microsoft.Win32.Primitives.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\Microsoft.Win32.Primitives.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\Microsoft.Win32.Primitives.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\Microsoft.Win32.Primitives.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\Microsoft.Win32.Registry.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\Microsoft.Win32.Registry.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\Microsoft.Win32.Registry.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\Microsoft.Win32.Registry.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.AppContext.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.AppContext.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.AppContext.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.AppContext.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Buffers.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Buffers.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Buffers.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Buffers.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Collections.Concurrent.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Collections.Concurrent.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Collections.Concurrent.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Collections.Concurrent.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Collections.Immutable.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Collections.Immutable.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Collections.Immutable.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Collections.Immutable.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Collections.NonGeneric.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Collections.NonGeneric.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Collections.NonGeneric.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Collections.NonGeneric.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Collections.Specialized.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Collections.Specialized.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Collections.Specialized.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Collections.Specialized.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Collections.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Collections.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Collections.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Collections.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ComponentModel.Annotations.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ComponentModel.Annotations.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ComponentModel.Annotations.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ComponentModel.Annotations.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ComponentModel.DataAnnotations.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ComponentModel.DataAnnotations.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ComponentModel.DataAnnotations.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ComponentModel.DataAnnotations.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ComponentModel.EventBasedAsync.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ComponentModel.EventBasedAsync.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ComponentModel.EventBasedAsync.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ComponentModel.EventBasedAsync.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ComponentModel.Primitives.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ComponentModel.Primitives.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ComponentModel.Primitives.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ComponentModel.Primitives.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ComponentModel.TypeConverter.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ComponentModel.TypeConverter.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ComponentModel.TypeConverter.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ComponentModel.TypeConverter.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ComponentModel.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ComponentModel.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ComponentModel.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ComponentModel.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Configuration.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Configuration.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Configuration.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Configuration.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Console.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Console.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Console.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Console.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Core.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Core.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Core.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Core.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Data.Common.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Data.Common.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Data.Common.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Data.Common.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Data.DataSetExtensions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Data.DataSetExtensions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Data.DataSetExtensions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Data.DataSetExtensions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Data.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Data.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Data.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Data.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.Contracts.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.Contracts.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.Contracts.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.Contracts.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.Debug.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.Debug.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.Debug.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.Debug.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.DiagnosticSource.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.DiagnosticSource.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.DiagnosticSource.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.DiagnosticSource.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.FileVersionInfo.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.FileVersionInfo.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.FileVersionInfo.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.FileVersionInfo.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.Process.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.Process.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.Process.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.Process.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.StackTrace.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.StackTrace.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.StackTrace.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.StackTrace.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.TextWriterTraceListener.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.TextWriterTraceListener.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.TextWriterTraceListener.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.TextWriterTraceListener.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.Tools.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.Tools.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.Tools.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.Tools.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.TraceSource.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.TraceSource.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.TraceSource.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.TraceSource.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.Tracing.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Diagnostics.Tracing.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.Tracing.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.Tracing.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Drawing.Primitives.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Drawing.Primitives.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Drawing.Primitives.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Drawing.Primitives.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Drawing.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Drawing.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Drawing.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Drawing.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Dynamic.Runtime.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Dynamic.Runtime.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Dynamic.Runtime.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Dynamic.Runtime.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Formats.Asn1.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Formats.Asn1.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Formats.Asn1.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Formats.Asn1.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Formats.Tar.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Formats.Tar.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Formats.Tar.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Formats.Tar.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Globalization.Calendars.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Globalization.Calendars.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Globalization.Calendars.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Globalization.Calendars.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Globalization.Extensions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Globalization.Extensions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Globalization.Extensions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Globalization.Extensions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Globalization.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Globalization.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Globalization.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Globalization.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.Compression.Brotli.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.Compression.Brotli.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.Compression.Brotli.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.Compression.Brotli.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.Compression.FileSystem.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.Compression.FileSystem.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.Compression.FileSystem.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.Compression.FileSystem.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.Compression.ZipFile.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.Compression.ZipFile.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.Compression.ZipFile.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.Compression.ZipFile.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.Compression.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.Compression.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.Compression.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.Compression.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.FileSystem.AccessControl.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.FileSystem.AccessControl.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.FileSystem.AccessControl.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.FileSystem.AccessControl.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.FileSystem.DriveInfo.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.FileSystem.DriveInfo.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.FileSystem.DriveInfo.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.FileSystem.DriveInfo.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.FileSystem.Primitives.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.FileSystem.Primitives.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.FileSystem.Primitives.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.FileSystem.Primitives.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.FileSystem.Watcher.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.FileSystem.Watcher.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.FileSystem.Watcher.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.FileSystem.Watcher.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.FileSystem.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.FileSystem.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.FileSystem.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.FileSystem.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.IsolatedStorage.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.IsolatedStorage.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.IsolatedStorage.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.IsolatedStorage.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.MemoryMappedFiles.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.MemoryMappedFiles.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.MemoryMappedFiles.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.MemoryMappedFiles.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.Pipes.AccessControl.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.Pipes.AccessControl.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.Pipes.AccessControl.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.Pipes.AccessControl.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.Pipes.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.Pipes.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.Pipes.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.Pipes.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.UnmanagedMemoryStream.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.UnmanagedMemoryStream.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.UnmanagedMemoryStream.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.UnmanagedMemoryStream.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.IO.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Linq.Expressions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Linq.Expressions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Linq.Expressions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Linq.Expressions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Linq.Parallel.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Linq.Parallel.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Linq.Parallel.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Linq.Parallel.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Linq.Queryable.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Linq.Queryable.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Linq.Queryable.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Linq.Queryable.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Linq.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Linq.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Linq.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Linq.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Memory.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Memory.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Memory.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Memory.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Http.Json.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Http.Json.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Http.Json.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Http.Json.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Http.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Http.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Http.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Http.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.HttpListener.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.HttpListener.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.HttpListener.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.HttpListener.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Mail.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Mail.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Mail.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Mail.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.NameResolution.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.NameResolution.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.NameResolution.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.NameResolution.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.NetworkInformation.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.NetworkInformation.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.NetworkInformation.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.NetworkInformation.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Ping.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Ping.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Ping.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Ping.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Primitives.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Primitives.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Primitives.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Primitives.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Quic.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Quic.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Quic.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Quic.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Requests.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Requests.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Requests.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Requests.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Security.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Security.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Security.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Security.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.ServicePoint.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.ServicePoint.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.ServicePoint.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.ServicePoint.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Sockets.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.Sockets.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Sockets.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Sockets.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.WebClient.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.WebClient.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.WebClient.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.WebClient.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.WebHeaderCollection.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.WebHeaderCollection.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.WebHeaderCollection.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.WebHeaderCollection.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.WebProxy.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.WebProxy.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.WebProxy.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.WebProxy.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.WebSockets.Client.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.WebSockets.Client.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.WebSockets.Client.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.WebSockets.Client.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.WebSockets.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.WebSockets.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.WebSockets.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.WebSockets.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Net.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Numerics.Vectors.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Numerics.Vectors.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Numerics.Vectors.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Numerics.Vectors.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Numerics.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Numerics.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Numerics.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Numerics.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ObjectModel.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ObjectModel.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ObjectModel.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ObjectModel.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.DispatchProxy.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.DispatchProxy.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.DispatchProxy.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.DispatchProxy.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.Emit.ILGeneration.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.Emit.ILGeneration.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.Emit.ILGeneration.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.Emit.ILGeneration.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.Emit.Lightweight.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.Emit.Lightweight.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.Emit.Lightweight.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.Emit.Lightweight.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.Emit.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.Emit.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.Emit.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.Emit.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.Extensions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.Extensions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.Extensions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.Extensions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.Metadata.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.Metadata.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.Metadata.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.Metadata.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.Primitives.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.Primitives.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.Primitives.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.Primitives.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.TypeExtensions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.TypeExtensions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.TypeExtensions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.TypeExtensions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Reflection.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Resources.Reader.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Resources.Reader.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Resources.Reader.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Resources.Reader.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Resources.ResourceManager.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Resources.ResourceManager.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Resources.ResourceManager.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Resources.ResourceManager.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Resources.Writer.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Resources.Writer.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Resources.Writer.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Resources.Writer.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.CompilerServices.Unsafe.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.CompilerServices.Unsafe.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.CompilerServices.Unsafe.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.CompilerServices.Unsafe.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.CompilerServices.VisualC.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.CompilerServices.VisualC.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.CompilerServices.VisualC.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.CompilerServices.VisualC.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Extensions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Extensions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Extensions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Extensions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Handles.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Handles.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Handles.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Handles.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.InteropServices.JavaScript.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.InteropServices.JavaScript.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.InteropServices.JavaScript.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.InteropServices.JavaScript.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.InteropServices.RuntimeInformation.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.InteropServices.RuntimeInformation.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.InteropServices.RuntimeInformation.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.InteropServices.RuntimeInformation.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.InteropServices.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.InteropServices.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.InteropServices.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.InteropServices.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Intrinsics.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Intrinsics.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Intrinsics.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Intrinsics.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Loader.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Loader.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Loader.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Loader.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Numerics.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Numerics.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Numerics.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Numerics.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Serialization.Formatters.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Serialization.Formatters.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Serialization.Formatters.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Serialization.Formatters.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Serialization.Json.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Serialization.Json.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Serialization.Json.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Serialization.Json.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Serialization.Primitives.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Serialization.Primitives.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Serialization.Primitives.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Serialization.Primitives.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Serialization.Xml.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Serialization.Xml.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Serialization.Xml.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Serialization.Xml.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Serialization.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.Serialization.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Serialization.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Serialization.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Runtime.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.AccessControl.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.AccessControl.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.AccessControl.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.AccessControl.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Claims.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Claims.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Claims.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Claims.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Cryptography.Algorithms.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Cryptography.Algorithms.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Cryptography.Algorithms.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Cryptography.Algorithms.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Cryptography.Cng.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Cryptography.Cng.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Cryptography.Cng.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Cryptography.Cng.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Cryptography.Csp.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Cryptography.Csp.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Cryptography.Csp.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Cryptography.Csp.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Cryptography.Encoding.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Cryptography.Encoding.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Cryptography.Encoding.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Cryptography.Encoding.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Cryptography.OpenSsl.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Cryptography.OpenSsl.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Cryptography.OpenSsl.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Cryptography.OpenSsl.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Cryptography.Primitives.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Cryptography.Primitives.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Cryptography.Primitives.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Cryptography.Primitives.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Cryptography.X509Certificates.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Cryptography.X509Certificates.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Cryptography.X509Certificates.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Cryptography.X509Certificates.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Cryptography.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Cryptography.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Cryptography.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Cryptography.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Principal.Windows.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Principal.Windows.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Principal.Windows.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Principal.Windows.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Principal.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.Principal.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Principal.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Principal.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.SecureString.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.SecureString.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.SecureString.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.SecureString.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Security.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ServiceModel.Web.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ServiceModel.Web.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ServiceModel.Web.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ServiceModel.Web.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ServiceProcess.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ServiceProcess.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ServiceProcess.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ServiceProcess.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Text.Encoding.CodePages.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Text.Encoding.CodePages.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Text.Encoding.CodePages.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Text.Encoding.CodePages.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Text.Encoding.Extensions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Text.Encoding.Extensions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Text.Encoding.Extensions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Text.Encoding.Extensions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Text.Encoding.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Text.Encoding.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Text.Encoding.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Text.Encoding.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Text.Encodings.Web.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Text.Encodings.Web.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Text.Encodings.Web.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Text.Encodings.Web.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Text.Json.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Text.Json.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Text.Json.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Text.Json.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Text.RegularExpressions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Text.RegularExpressions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Text.RegularExpressions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Text.RegularExpressions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.Channels.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.Channels.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.Channels.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.Channels.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.Overlapped.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.Overlapped.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.Overlapped.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.Overlapped.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.Tasks.Dataflow.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.Tasks.Dataflow.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.Tasks.Dataflow.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.Tasks.Dataflow.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.Tasks.Extensions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.Tasks.Extensions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.Tasks.Extensions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.Tasks.Extensions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.Tasks.Parallel.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.Tasks.Parallel.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.Tasks.Parallel.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.Tasks.Parallel.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.Tasks.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.Tasks.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.Tasks.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.Tasks.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.Thread.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.Thread.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.Thread.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.Thread.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.ThreadPool.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.ThreadPool.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.ThreadPool.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.ThreadPool.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.Timer.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.Timer.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.Timer.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.Timer.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Threading.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Transactions.Local.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Transactions.Local.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Transactions.Local.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Transactions.Local.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Transactions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Transactions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Transactions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Transactions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ValueTuple.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.ValueTuple.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ValueTuple.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ValueTuple.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Web.HttpUtility.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Web.HttpUtility.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Web.HttpUtility.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Web.HttpUtility.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Web.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Web.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Web.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Web.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Windows.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Windows.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Windows.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Windows.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.Linq.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.Linq.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.Linq.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.Linq.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.ReaderWriter.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.ReaderWriter.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.ReaderWriter.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.ReaderWriter.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.Serialization.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.Serialization.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.Serialization.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.Serialization.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.XDocument.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.XDocument.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.XDocument.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.XDocument.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.XPath.XDocument.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.XPath.XDocument.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.XPath.XDocument.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.XPath.XDocument.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.XPath.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.XPath.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.XPath.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.XPath.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.XmlDocument.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.XmlDocument.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.XmlDocument.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.XmlDocument.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.XmlSerializer.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.XmlSerializer.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.XmlSerializer.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.XmlSerializer.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.Xml.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\System.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\WindowsBase.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\WindowsBase.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\WindowsBase.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\WindowsBase.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\mscorlib.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\mscorlib.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\mscorlib.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\mscorlib.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\netstandard.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\ref\netstandard.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\netstandard.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\netstandard.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Host\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Management\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Security\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Utility\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\unix\lib\net8.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\CimCmdlets\CimCmdlets.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\CimCmdlets\CimCmdlets.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net8.0\Modules\CimCmdlets\CimCmdlets.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net8.0\Modules\CimCmdlets\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net8.0\Modules\CimCmdlets\CimCmdlets.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\Diagnostics.format.ps1xml" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\Diagnostics.format.ps1xml')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\Diagnostics.format.ps1xml</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\Diagnostics.format.ps1xml</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\Event.format.ps1xml" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\Event.format.ps1xml')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\Event.format.ps1xml</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\Event.format.ps1xml</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\GetEvent.types.ps1xml" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\GetEvent.types.ps1xml')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\GetEvent.types.ps1xml</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\GetEvent.types.ps1xml</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\Microsoft.PowerShell.Diagnostics.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\Microsoft.PowerShell.Diagnostics.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\Microsoft.PowerShell.Diagnostics.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Diagnostics\Microsoft.PowerShell.Diagnostics.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Host\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Management\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Security\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Security\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Utility\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net8.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.WSMan.Management\Microsoft.WSMan.Management.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.WSMan.Management\Microsoft.WSMan.Management.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net8.0\Modules\Microsoft.WSMan.Management\Microsoft.WSMan.Management.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net8.0\Modules\Microsoft.WSMan.Management\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net8.0\Modules\Microsoft.WSMan.Management\Microsoft.WSMan.Management.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.WSMan.Management\WSMan.format.ps1xml" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\Microsoft.WSMan.Management\WSMan.format.ps1xml')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net8.0\Modules\Microsoft.WSMan.Management\WSMan.format.ps1xml</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net8.0\Modules\Microsoft.WSMan.Management\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net8.0\Modules\Microsoft.WSMan.Management\WSMan.format.ps1xml</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\PSDiagnostics\PSDiagnostics.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\PSDiagnostics\PSDiagnostics.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net8.0\Modules\PSDiagnostics\PSDiagnostics.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net8.0\Modules\PSDiagnostics\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net8.0\Modules\PSDiagnostics\PSDiagnostics.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\PSDiagnostics\PSDiagnostics.psm1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.4.11\contentFiles\any\any\runtimes\win\lib\net8.0\Modules\PSDiagnostics\PSDiagnostics.psm1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.4.11</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net8.0\Modules\PSDiagnostics\PSDiagnostics.psm1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net8.0\Modules\PSDiagnostics\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net8.0\Modules\PSDiagnostics\PSDiagnostics.psm1</Link>
    </None>
  </ItemGroup>
  <ItemGroup Condition=" '$(TargetFramework)' == 'net9.0' AND '$(ExcludeRestorePackageImports)' != 'true' ">
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\Microsoft.CSharp.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\Microsoft.CSharp.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\Microsoft.CSharp.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\Microsoft.CSharp.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\Microsoft.VisualBasic.Core.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\Microsoft.VisualBasic.Core.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\Microsoft.VisualBasic.Core.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\Microsoft.VisualBasic.Core.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\Microsoft.VisualBasic.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\Microsoft.VisualBasic.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\Microsoft.VisualBasic.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\Microsoft.VisualBasic.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\Microsoft.Win32.Primitives.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\Microsoft.Win32.Primitives.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\Microsoft.Win32.Primitives.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\Microsoft.Win32.Primitives.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\Microsoft.Win32.Registry.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\Microsoft.Win32.Registry.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\Microsoft.Win32.Registry.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\Microsoft.Win32.Registry.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.AppContext.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.AppContext.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.AppContext.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.AppContext.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Buffers.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Buffers.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Buffers.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Buffers.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Collections.Concurrent.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Collections.Concurrent.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Collections.Concurrent.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Collections.Concurrent.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Collections.Immutable.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Collections.Immutable.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Collections.Immutable.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Collections.Immutable.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Collections.NonGeneric.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Collections.NonGeneric.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Collections.NonGeneric.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Collections.NonGeneric.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Collections.Specialized.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Collections.Specialized.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Collections.Specialized.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Collections.Specialized.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Collections.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Collections.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Collections.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Collections.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ComponentModel.Annotations.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ComponentModel.Annotations.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ComponentModel.Annotations.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ComponentModel.Annotations.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ComponentModel.DataAnnotations.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ComponentModel.DataAnnotations.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ComponentModel.DataAnnotations.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ComponentModel.DataAnnotations.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ComponentModel.EventBasedAsync.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ComponentModel.EventBasedAsync.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ComponentModel.EventBasedAsync.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ComponentModel.EventBasedAsync.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ComponentModel.Primitives.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ComponentModel.Primitives.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ComponentModel.Primitives.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ComponentModel.Primitives.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ComponentModel.TypeConverter.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ComponentModel.TypeConverter.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ComponentModel.TypeConverter.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ComponentModel.TypeConverter.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ComponentModel.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ComponentModel.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ComponentModel.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ComponentModel.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Configuration.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Configuration.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Configuration.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Configuration.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Console.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Console.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Console.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Console.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Core.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Core.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Core.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Core.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Data.Common.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Data.Common.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Data.Common.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Data.Common.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Data.DataSetExtensions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Data.DataSetExtensions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Data.DataSetExtensions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Data.DataSetExtensions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Data.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Data.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Data.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Data.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.Contracts.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.Contracts.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.Contracts.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.Contracts.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.Debug.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.Debug.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.Debug.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.Debug.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.DiagnosticSource.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.DiagnosticSource.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.DiagnosticSource.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.DiagnosticSource.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.FileVersionInfo.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.FileVersionInfo.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.FileVersionInfo.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.FileVersionInfo.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.Process.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.Process.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.Process.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.Process.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.StackTrace.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.StackTrace.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.StackTrace.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.StackTrace.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.TextWriterTraceListener.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.TextWriterTraceListener.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.TextWriterTraceListener.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.TextWriterTraceListener.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.Tools.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.Tools.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.Tools.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.Tools.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.TraceSource.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.TraceSource.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.TraceSource.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.TraceSource.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.Tracing.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Diagnostics.Tracing.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Diagnostics.Tracing.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Diagnostics.Tracing.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Drawing.Primitives.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Drawing.Primitives.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Drawing.Primitives.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Drawing.Primitives.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Drawing.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Drawing.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Drawing.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Drawing.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Dynamic.Runtime.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Dynamic.Runtime.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Dynamic.Runtime.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Dynamic.Runtime.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Formats.Asn1.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Formats.Asn1.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Formats.Asn1.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Formats.Asn1.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Formats.Tar.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Formats.Tar.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Formats.Tar.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Formats.Tar.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Globalization.Calendars.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Globalization.Calendars.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Globalization.Calendars.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Globalization.Calendars.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Globalization.Extensions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Globalization.Extensions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Globalization.Extensions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Globalization.Extensions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Globalization.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Globalization.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Globalization.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Globalization.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.Compression.Brotli.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.Compression.Brotli.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.Compression.Brotli.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.Compression.Brotli.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.Compression.FileSystem.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.Compression.FileSystem.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.Compression.FileSystem.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.Compression.FileSystem.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.Compression.ZipFile.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.Compression.ZipFile.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.Compression.ZipFile.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.Compression.ZipFile.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.Compression.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.Compression.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.Compression.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.Compression.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.FileSystem.AccessControl.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.FileSystem.AccessControl.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.FileSystem.AccessControl.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.FileSystem.AccessControl.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.FileSystem.DriveInfo.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.FileSystem.DriveInfo.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.FileSystem.DriveInfo.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.FileSystem.DriveInfo.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.FileSystem.Primitives.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.FileSystem.Primitives.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.FileSystem.Primitives.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.FileSystem.Primitives.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.FileSystem.Watcher.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.FileSystem.Watcher.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.FileSystem.Watcher.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.FileSystem.Watcher.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.FileSystem.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.FileSystem.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.FileSystem.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.FileSystem.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.IsolatedStorage.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.IsolatedStorage.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.IsolatedStorage.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.IsolatedStorage.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.MemoryMappedFiles.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.MemoryMappedFiles.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.MemoryMappedFiles.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.MemoryMappedFiles.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.Pipelines.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.Pipelines.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.Pipelines.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.Pipelines.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.Pipes.AccessControl.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.Pipes.AccessControl.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.Pipes.AccessControl.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.Pipes.AccessControl.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.Pipes.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.Pipes.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.Pipes.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.Pipes.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.UnmanagedMemoryStream.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.UnmanagedMemoryStream.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.UnmanagedMemoryStream.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.UnmanagedMemoryStream.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.IO.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.IO.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.IO.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Linq.Expressions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Linq.Expressions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Linq.Expressions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Linq.Expressions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Linq.Parallel.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Linq.Parallel.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Linq.Parallel.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Linq.Parallel.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Linq.Queryable.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Linq.Queryable.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Linq.Queryable.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Linq.Queryable.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Linq.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Linq.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Linq.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Linq.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Memory.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Memory.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Memory.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Memory.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Http.Json.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Http.Json.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Http.Json.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Http.Json.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Http.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Http.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Http.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Http.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.HttpListener.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.HttpListener.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.HttpListener.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.HttpListener.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Mail.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Mail.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Mail.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Mail.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.NameResolution.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.NameResolution.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.NameResolution.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.NameResolution.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.NetworkInformation.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.NetworkInformation.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.NetworkInformation.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.NetworkInformation.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Ping.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Ping.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Ping.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Ping.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Primitives.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Primitives.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Primitives.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Primitives.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Quic.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Quic.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Quic.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Quic.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Requests.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Requests.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Requests.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Requests.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Security.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Security.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Security.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Security.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.ServicePoint.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.ServicePoint.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.ServicePoint.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.ServicePoint.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Sockets.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.Sockets.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.Sockets.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.Sockets.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.WebClient.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.WebClient.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.WebClient.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.WebClient.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.WebHeaderCollection.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.WebHeaderCollection.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.WebHeaderCollection.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.WebHeaderCollection.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.WebProxy.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.WebProxy.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.WebProxy.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.WebProxy.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.WebSockets.Client.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.WebSockets.Client.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.WebSockets.Client.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.WebSockets.Client.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.WebSockets.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.WebSockets.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.WebSockets.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.WebSockets.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Net.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Net.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Net.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Numerics.Vectors.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Numerics.Vectors.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Numerics.Vectors.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Numerics.Vectors.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Numerics.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Numerics.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Numerics.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Numerics.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ObjectModel.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ObjectModel.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ObjectModel.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ObjectModel.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.DispatchProxy.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.DispatchProxy.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.DispatchProxy.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.DispatchProxy.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.Emit.ILGeneration.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.Emit.ILGeneration.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.Emit.ILGeneration.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.Emit.ILGeneration.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.Emit.Lightweight.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.Emit.Lightweight.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.Emit.Lightweight.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.Emit.Lightweight.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.Emit.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.Emit.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.Emit.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.Emit.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.Extensions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.Extensions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.Extensions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.Extensions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.Metadata.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.Metadata.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.Metadata.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.Metadata.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.Primitives.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.Primitives.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.Primitives.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.Primitives.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.TypeExtensions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.TypeExtensions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.TypeExtensions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.TypeExtensions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Reflection.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Reflection.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Reflection.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Resources.Reader.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Resources.Reader.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Resources.Reader.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Resources.Reader.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Resources.ResourceManager.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Resources.ResourceManager.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Resources.ResourceManager.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Resources.ResourceManager.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Resources.Writer.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Resources.Writer.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Resources.Writer.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Resources.Writer.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.CompilerServices.Unsafe.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.CompilerServices.Unsafe.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.CompilerServices.Unsafe.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.CompilerServices.Unsafe.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.CompilerServices.VisualC.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.CompilerServices.VisualC.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.CompilerServices.VisualC.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.CompilerServices.VisualC.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Extensions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Extensions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Extensions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Extensions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Handles.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Handles.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Handles.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Handles.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.InteropServices.JavaScript.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.InteropServices.JavaScript.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.InteropServices.JavaScript.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.InteropServices.JavaScript.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.InteropServices.RuntimeInformation.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.InteropServices.RuntimeInformation.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.InteropServices.RuntimeInformation.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.InteropServices.RuntimeInformation.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.InteropServices.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.InteropServices.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.InteropServices.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.InteropServices.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Intrinsics.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Intrinsics.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Intrinsics.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Intrinsics.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Loader.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Loader.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Loader.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Loader.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Numerics.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Numerics.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Numerics.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Numerics.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Serialization.Formatters.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Serialization.Formatters.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Serialization.Formatters.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Serialization.Formatters.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Serialization.Json.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Serialization.Json.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Serialization.Json.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Serialization.Json.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Serialization.Primitives.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Serialization.Primitives.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Serialization.Primitives.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Serialization.Primitives.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Serialization.Xml.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Serialization.Xml.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Serialization.Xml.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Serialization.Xml.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Serialization.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.Serialization.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.Serialization.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.Serialization.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Runtime.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Runtime.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Runtime.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.AccessControl.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.AccessControl.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.AccessControl.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.AccessControl.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Claims.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Claims.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Claims.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Claims.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Cryptography.Algorithms.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Cryptography.Algorithms.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Cryptography.Algorithms.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Cryptography.Algorithms.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Cryptography.Cng.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Cryptography.Cng.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Cryptography.Cng.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Cryptography.Cng.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Cryptography.Csp.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Cryptography.Csp.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Cryptography.Csp.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Cryptography.Csp.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Cryptography.Encoding.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Cryptography.Encoding.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Cryptography.Encoding.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Cryptography.Encoding.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Cryptography.OpenSsl.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Cryptography.OpenSsl.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Cryptography.OpenSsl.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Cryptography.OpenSsl.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Cryptography.Primitives.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Cryptography.Primitives.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Cryptography.Primitives.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Cryptography.Primitives.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Cryptography.X509Certificates.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Cryptography.X509Certificates.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Cryptography.X509Certificates.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Cryptography.X509Certificates.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Cryptography.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Cryptography.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Cryptography.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Cryptography.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Principal.Windows.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Principal.Windows.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Principal.Windows.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Principal.Windows.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Principal.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.Principal.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.Principal.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.Principal.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.SecureString.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.SecureString.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.SecureString.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.SecureString.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Security.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Security.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Security.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ServiceModel.Web.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ServiceModel.Web.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ServiceModel.Web.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ServiceModel.Web.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ServiceProcess.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ServiceProcess.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ServiceProcess.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ServiceProcess.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Text.Encoding.CodePages.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Text.Encoding.CodePages.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Text.Encoding.CodePages.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Text.Encoding.CodePages.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Text.Encoding.Extensions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Text.Encoding.Extensions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Text.Encoding.Extensions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Text.Encoding.Extensions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Text.Encoding.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Text.Encoding.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Text.Encoding.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Text.Encoding.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Text.Encodings.Web.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Text.Encodings.Web.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Text.Encodings.Web.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Text.Encodings.Web.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Text.Json.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Text.Json.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Text.Json.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Text.Json.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Text.RegularExpressions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Text.RegularExpressions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Text.RegularExpressions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Text.RegularExpressions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.Channels.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.Channels.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.Channels.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.Channels.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.Overlapped.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.Overlapped.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.Overlapped.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.Overlapped.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.Tasks.Dataflow.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.Tasks.Dataflow.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.Tasks.Dataflow.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.Tasks.Dataflow.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.Tasks.Extensions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.Tasks.Extensions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.Tasks.Extensions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.Tasks.Extensions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.Tasks.Parallel.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.Tasks.Parallel.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.Tasks.Parallel.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.Tasks.Parallel.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.Tasks.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.Tasks.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.Tasks.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.Tasks.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.Thread.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.Thread.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.Thread.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.Thread.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.ThreadPool.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.ThreadPool.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.ThreadPool.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.ThreadPool.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.Timer.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.Timer.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.Timer.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.Timer.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Threading.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Threading.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Threading.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Transactions.Local.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Transactions.Local.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Transactions.Local.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Transactions.Local.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Transactions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Transactions.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Transactions.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Transactions.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ValueTuple.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.ValueTuple.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.ValueTuple.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.ValueTuple.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Web.HttpUtility.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Web.HttpUtility.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Web.HttpUtility.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Web.HttpUtility.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Web.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Web.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Web.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Web.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Windows.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Windows.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Windows.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Windows.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.Linq.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.Linq.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.Linq.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.Linq.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.ReaderWriter.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.ReaderWriter.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.ReaderWriter.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.ReaderWriter.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.Serialization.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.Serialization.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.Serialization.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.Serialization.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.XDocument.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.XDocument.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.XDocument.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.XDocument.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.XPath.XDocument.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.XPath.XDocument.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.XPath.XDocument.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.XPath.XDocument.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.XPath.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.XPath.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.XPath.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.XPath.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.XmlDocument.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.XmlDocument.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.XmlDocument.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.XmlDocument.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.XmlSerializer.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.XmlSerializer.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.XmlSerializer.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.XmlSerializer.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.Xml.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.Xml.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.Xml.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\System.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\System.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\System.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\WindowsBase.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\WindowsBase.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\WindowsBase.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\WindowsBase.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\mscorlib.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\mscorlib.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\mscorlib.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\mscorlib.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\netstandard.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\ref\netstandard.dll')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>ref\netstandard.dll</TargetPath>
      <DestinationSubDirectory>ref\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>ref\netstandard.dll</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Host\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Management\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Security\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Utility\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\CimCmdlets\CimCmdlets.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\CimCmdlets\CimCmdlets.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net9.0\Modules\CimCmdlets\CimCmdlets.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net9.0\Modules\CimCmdlets\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net9.0\Modules\CimCmdlets\CimCmdlets.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\Diagnostics.format.ps1xml" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\Diagnostics.format.ps1xml')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\Diagnostics.format.ps1xml</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\Diagnostics.format.ps1xml</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\Event.format.ps1xml" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\Event.format.ps1xml')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\Event.format.ps1xml</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\Event.format.ps1xml</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\GetEvent.types.ps1xml" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\GetEvent.types.ps1xml')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\GetEvent.types.ps1xml</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\GetEvent.types.ps1xml</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\Microsoft.PowerShell.Diagnostics.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\Microsoft.PowerShell.Diagnostics.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\Microsoft.PowerShell.Diagnostics.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\Microsoft.PowerShell.Diagnostics.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Host\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Management\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Security\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Security\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Utility\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.WSMan.Management\Microsoft.WSMan.Management.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.WSMan.Management\Microsoft.WSMan.Management.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net9.0\Modules\Microsoft.WSMan.Management\Microsoft.WSMan.Management.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net9.0\Modules\Microsoft.WSMan.Management\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net9.0\Modules\Microsoft.WSMan.Management\Microsoft.WSMan.Management.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.WSMan.Management\WSMan.format.ps1xml" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\Microsoft.WSMan.Management\WSMan.format.ps1xml')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net9.0\Modules\Microsoft.WSMan.Management\WSMan.format.ps1xml</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net9.0\Modules\Microsoft.WSMan.Management\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net9.0\Modules\Microsoft.WSMan.Management\WSMan.format.ps1xml</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\PSDiagnostics\PSDiagnostics.psd1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\PSDiagnostics\PSDiagnostics.psd1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net9.0\Modules\PSDiagnostics\PSDiagnostics.psd1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net9.0\Modules\PSDiagnostics\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net9.0\Modules\PSDiagnostics\PSDiagnostics.psd1</Link>
    </None>
    <None Include="$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\PSDiagnostics\PSDiagnostics.psm1" Condition="Exists('$(NuGetPackageRoot)microsoft.powershell.sdk\7.5.2\contentFiles\any\any\runtimes\win\lib\net9.0\Modules\PSDiagnostics\PSDiagnostics.psm1')">
      <NuGetPackageId>Microsoft.PowerShell.SDK</NuGetPackageId>
      <NuGetPackageVersion>7.5.2</NuGetPackageVersion>
      <NuGetItemType>None</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>runtimes\win\lib\net9.0\Modules\PSDiagnostics\PSDiagnostics.psm1</TargetPath>
      <DestinationSubDirectory>runtimes\win\lib\net9.0\Modules\PSDiagnostics\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>runtimes\win\lib\net9.0\Modules\PSDiagnostics\PSDiagnostics.psm1</Link>
    </None>
  </ItemGroup>
  <ImportGroup Condition=" '$(TargetFramework)' == 'net8.0' AND '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)sixlabors.imagesharp.web\3.1.5\build\SixLabors.ImageSharp.Web.props" Condition="Exists('$(NuGetPackageRoot)sixlabors.imagesharp.web\3.1.5\build\SixLabors.ImageSharp.Web.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.codeanalysis.analyzers\3.3.4\buildTransitive\Microsoft.CodeAnalysis.Analyzers.props" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.analyzers\3.3.4\buildTransitive\Microsoft.CodeAnalysis.Analyzers.props')" />
  </ImportGroup>
  <ImportGroup Condition=" '$(TargetFramework)' == 'net9.0' AND '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)sixlabors.imagesharp.web\3.1.5\build\SixLabors.ImageSharp.Web.props" Condition="Exists('$(NuGetPackageRoot)sixlabors.imagesharp.web\3.1.5\build\SixLabors.ImageSharp.Web.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.codeanalysis.analyzers\3.11.0\buildTransitive\Microsoft.CodeAnalysis.Analyzers.props" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.analyzers\3.11.0\buildTransitive\Microsoft.CodeAnalysis.Analyzers.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(TargetFramework)' == 'net8.0' AND '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgMicrosoft_Extensions_ApiDescription_Server Condition=" '$(PkgMicrosoft_Extensions_ApiDescription_Server)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.extensions.apidescription.server\8.0.0</PkgMicrosoft_Extensions_ApiDescription_Server>
    <PkgMicrosoft_CodeAnalysis_Analyzers Condition=" '$(PkgMicrosoft_CodeAnalysis_Analyzers)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4</PkgMicrosoft_CodeAnalysis_Analyzers>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(TargetFramework)' == 'net9.0' AND '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgMicrosoft_Extensions_ApiDescription_Server Condition=" '$(PkgMicrosoft_Extensions_ApiDescription_Server)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.extensions.apidescription.server\9.0.0</PkgMicrosoft_Extensions_ApiDescription_Server>
    <PkgMicrosoft_CodeAnalysis_Analyzers Condition=" '$(PkgMicrosoft_CodeAnalysis_Analyzers)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.11.0</PkgMicrosoft_CodeAnalysis_Analyzers>
  </PropertyGroup>
</Project>