using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application.Service.EmqxRealtime.DTOs;

/// <summary>
/// 订阅请求DTO
/// </summary>
public class SubscriptionRequest
{
    /// <summary>
    /// MQTT实例ID
    /// </summary>
    [Required]
    public int InstanceId { get; set; }

    /// <summary>
    /// 订阅的主题列表
    /// </summary>
    public List<string> Topics { get; set; } = new();

    /// <summary>
    /// 是否订阅所有主题
    /// </summary>
    public bool SubscribeToAllTopics { get; set; } = false;

    /// <summary>
    /// 是否包含历史消息
    /// </summary>
    public bool IncludeHistory { get; set; } = false;

    /// <summary>
    /// 最大历史消息数量
    /// </summary>
    public int MaxHistoryCount { get; set; } = 50;

    /// <summary>
    /// 设备分组名称
    /// </summary>
    public string? DeviceGroup { get; set; }

    /// <summary>
    /// 消息质量等级
    /// </summary>
    public int QoS { get; set; } = 0;
}