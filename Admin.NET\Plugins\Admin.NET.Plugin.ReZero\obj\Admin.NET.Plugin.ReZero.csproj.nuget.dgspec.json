{"format": 1, "restore": {"D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Plugins\\Admin.NET.Plugin.ReZero\\Admin.NET.Plugin.ReZero.csproj": {}}, "projects": {"D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Core\\Admin.NET.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Core\\Admin.NET.Core.csproj", "projectName": "Admin.NET.Core", "projectPath": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Core\\Admin.NET.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Core\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0", "net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}, "net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AlibabaCloud.SDK.Dysmsapi20170525": {"target": "Package", "version": "[4.0.0, )"}, "AlipaySDKNet.Standard": {"target": "Package", "version": "[4.9.627, )"}, "AngleSharp": {"target": "Package", "version": "[1.3.0, )"}, "AspNet.Security.OAuth.Gitee": {"target": "Package", "version": "[8.3.0, )"}, "AspNet.Security.OAuth.Weixin": {"target": "Package", "version": "[8.3.0, )"}, "AspNetCoreRateLimit": {"target": "Package", "version": "[5.0.0, )"}, "AspectCore.Extensions.Reflection": {"target": "Package", "version": "[2.4.0, )"}, "BouncyCastle.Cryptography": {"target": "Package", "version": "[2.6.1, )", "aliases": "BouncyCastleV2"}, "Elastic.Clients.Elasticsearch": {"target": "Package", "version": "[9.0.7, )"}, "Furion.Extras.Authentication.JwtBearer": {"target": "Package", "version": "[*********, )"}, "Furion.Extras.ObjectMapper.Mapster": {"target": "Package", "version": "[*********, )"}, "Furion.Pure": {"target": "Package", "version": "[*********, )"}, "Hardware.Info": {"target": "Package", "version": "[*********, )"}, "Hashids.net": {"target": "Package", "version": "[1.7.0, )"}, "IPTools.China": {"target": "Package", "version": "[1.6.0, )"}, "IPTools.International": {"target": "Package", "version": "[1.6.0, )"}, "Lazy.Captcha.Core": {"target": "Package", "version": "[2.2.0, )"}, "Magicodes.IE.Excel": {"target": "Package", "version": "[2.7.6, )"}, "Magicodes.IE.Pdf": {"target": "Package", "version": "[2.7.6, )"}, "Magicodes.IE.Word": {"target": "Package", "version": "[2.7.6, )"}, "MailKit": {"target": "Package", "version": "[4.13.0, )"}, "Microsoft.AspNetCore.DataProtection.StackExchangeRedis": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.AspNetCore.SignalR.StackExchangeRedis": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.PowerShell.SDK": {"target": "Package", "version": "[7.4.11, )"}, "MiniExcel": {"target": "Package", "version": "[1.41.3, )"}, "MiniWord": {"target": "Package", "version": "[0.9.2, )"}, "NewLife.Redis": {"target": "Package", "version": "[6.3.2025.701, )"}, "Novell.Directory.Ldap.NETStandard": {"target": "Package", "version": "[4.0.0, )"}, "OnceMi.AspNetCore.OSS": {"target": "Package", "version": "[1.2.0, )"}, "QRCoder": {"target": "Package", "version": "[1.6.0, )"}, "RabbitMQ.Client": {"target": "Package", "version": "[7.1.2, )"}, "SKIT.FlurlHttpClient.Wechat.Api": {"target": "Package", "version": "[3.11.0, )"}, "SKIT.FlurlHttpClient.Wechat.TenpayV3": {"target": "Package", "version": "[3.13.0, )"}, "SSH.NET": {"target": "Package", "version": "[2025.0.0, )"}, "SixLabors.ImageSharp.Web": {"target": "Package", "version": "[3.1.5, )"}, "SqlSugar.MongoDbCore": {"target": "Package", "version": "[5.1.4.232, )"}, "SqlSugarCore": {"target": "Package", "version": "[5.1.4.198, )"}, "System.Linq.Dynamic.Core": {"target": "Package", "version": "[1.6.6, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}, "System.Private.Uri": {"target": "Package", "version": "[4.3.2, )"}, "TencentCloudSDK.Sms": {"target": "Package", "version": "[3.0.1273, )"}, "UAParser": {"target": "Package", "version": "[3.1.47, )"}, "Yitter.IdGenerator": {"target": "Package", "version": "[1.0.14, )"}, "log4net": {"target": "Package", "version": "[3.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0": {"targetAlias": "net9.0", "dependencies": {"AlibabaCloud.SDK.Dysmsapi20170525": {"target": "Package", "version": "[4.0.0, )"}, "AlipaySDKNet.Standard": {"target": "Package", "version": "[4.9.627, )"}, "AngleSharp": {"target": "Package", "version": "[1.3.0, )"}, "AspNet.Security.OAuth.Gitee": {"target": "Package", "version": "[9.4.0, )"}, "AspNet.Security.OAuth.Weixin": {"target": "Package", "version": "[9.4.0, )"}, "AspNetCoreRateLimit": {"target": "Package", "version": "[5.0.0, )"}, "AspectCore.Extensions.Reflection": {"target": "Package", "version": "[2.4.0, )"}, "BouncyCastle.Cryptography": {"target": "Package", "version": "[2.6.1, )", "aliases": "BouncyCastleV2"}, "Elastic.Clients.Elasticsearch": {"target": "Package", "version": "[9.0.7, )"}, "Furion.Extras.Authentication.JwtBearer": {"target": "Package", "version": "[*********, )"}, "Furion.Extras.ObjectMapper.Mapster": {"target": "Package", "version": "[*********, )"}, "Furion.Pure": {"target": "Package", "version": "[*********, )"}, "Hardware.Info": {"target": "Package", "version": "[*********, )"}, "Hashids.net": {"target": "Package", "version": "[1.7.0, )"}, "IPTools.China": {"target": "Package", "version": "[1.6.0, )"}, "IPTools.International": {"target": "Package", "version": "[1.6.0, )"}, "Lazy.Captcha.Core": {"target": "Package", "version": "[2.1.0, )"}, "Magicodes.IE.Excel": {"target": "Package", "version": "[2.7.6, )"}, "Magicodes.IE.Pdf": {"target": "Package", "version": "[2.7.6, )"}, "Magicodes.IE.Word": {"target": "Package", "version": "[2.7.6, )"}, "MailKit": {"target": "Package", "version": "[4.13.0, )"}, "Microsoft.AspNetCore.DataProtection.StackExchangeRedis": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.AspNetCore.SignalR.StackExchangeRedis": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.PowerShell.SDK": {"target": "Package", "version": "[7.5.2, )"}, "MiniExcel": {"target": "Package", "version": "[1.41.3, )"}, "MiniWord": {"target": "Package", "version": "[0.9.2, )"}, "NewLife.Redis": {"target": "Package", "version": "[6.3.2025.701, )"}, "Novell.Directory.Ldap.NETStandard": {"target": "Package", "version": "[4.0.0, )"}, "OnceMi.AspNetCore.OSS": {"target": "Package", "version": "[1.2.0, )"}, "QRCoder": {"target": "Package", "version": "[1.6.0, )"}, "RabbitMQ.Client": {"target": "Package", "version": "[7.1.2, )"}, "SKIT.FlurlHttpClient.Wechat.Api": {"target": "Package", "version": "[3.11.0, )"}, "SKIT.FlurlHttpClient.Wechat.TenpayV3": {"target": "Package", "version": "[3.13.0, )"}, "SSH.NET": {"target": "Package", "version": "[2025.0.0, )"}, "SixLabors.ImageSharp.Web": {"target": "Package", "version": "[3.1.5, )"}, "SqlSugar.MongoDbCore": {"target": "Package", "version": "[5.1.4.232, )"}, "SqlSugarCore": {"target": "Package", "version": "[5.1.4.198, )"}, "System.Linq.Dynamic.Core": {"target": "Package", "version": "[1.6.6, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}, "System.Private.Uri": {"target": "Package", "version": "[4.3.2, )"}, "TencentCloudSDK.Sms": {"target": "Package", "version": "[3.0.1273, )"}, "UAParser": {"target": "Package", "version": "[3.1.47, )"}, "XiHan.Framework.Utils": {"target": "Package", "version": "[0.11.6, )"}, "Yitter.IdGenerator": {"target": "Package", "version": "[1.0.14, )"}, "log4net": {"target": "Package", "version": "[3.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Plugins\\Admin.NET.Plugin.ReZero\\Admin.NET.Plugin.ReZero.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Plugins\\Admin.NET.Plugin.ReZero\\Admin.NET.Plugin.ReZero.csproj", "projectName": "Admin.NET.Plugin.ReZero", "projectPath": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Plugins\\Admin.NET.Plugin.ReZero\\Admin.NET.Plugin.ReZero.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Plugins\\Admin.NET.Plugin.ReZero\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0", "net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Core\\Admin.NET.Core.csproj": {"projectPath": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Core\\Admin.NET.Core.csproj"}}}, "net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Core\\Admin.NET.Core.csproj": {"projectPath": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Core\\Admin.NET.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"DocumentFormat.OpenXml": {"target": "Package", "version": "[3.3.0, )"}, "Microsoft.CodeAnalysis.CSharp.Scripting": {"target": "Package", "version": "[4.14.0, )"}, "Rezero.Api": {"target": "Package", "version": "[1.8.24, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0": {"targetAlias": "net9.0", "dependencies": {"DocumentFormat.OpenXml": {"target": "Package", "version": "[3.3.0, )"}, "Microsoft.CodeAnalysis.CSharp.Scripting": {"target": "Package", "version": "[4.14.0, )"}, "Rezero.Api": {"target": "Package", "version": "[1.8.24, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}