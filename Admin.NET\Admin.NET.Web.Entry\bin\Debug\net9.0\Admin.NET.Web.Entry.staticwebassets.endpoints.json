{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "images/logo.3fy106m53y.png", "AssetFile": "images/logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8024"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3fy106m53y"}, {"Name": "integrity", "Value": "sha256-gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0="}, {"Name": "label", "Value": "images/logo.png"}]}, {"Route": "images/logo.png", "AssetFile": "images/logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8024"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0="}]}, {"Route": "template/Dto.cs.o6ujo32hiy.vm", "AssetFile": "template/Dto.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1149"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"cHywhBuRRTLnl3VxKeyVH2uWeIBPHaJpLogvcm5l+us=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o6ujo32hiy"}, {"Name": "integrity", "Value": "sha256-cHywhBuRRTLnl3VxKeyVH2uWeIBPHaJpLogvcm5l+us="}, {"Name": "label", "Value": "template/Dto.cs.vm"}]}, {"Route": "template/Dto.cs.vm", "AssetFile": "template/Dto.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1149"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"cHywhBuRRTLnl3VxKeyVH2uWeIBPHaJpLogvcm5l+us=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cHywhBuRRTLnl3VxKeyVH2uWeIBPHaJpLogvcm5l+us="}]}, {"Route": "template/Entity.cs.rytambt81f.vm", "AssetFile": "template/Entity.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1944"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"M2g02yCDi6ZIWCXEH0PiCc7TswLvXXZXmSVet03Ys0U=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rytambt81f"}, {"Name": "integrity", "Value": "sha256-M2g02yCDi6ZIWCXEH0PiCc7TswLvXXZXmSVet03Ys0U="}, {"Name": "label", "Value": "template/Entity.cs.vm"}]}, {"Route": "template/Entity.cs.vm", "AssetFile": "template/Entity.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1944"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"M2g02yCDi6ZIWCXEH0PiCc7TswLvXXZXmSVet03Ys0U=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M2g02yCDi6ZIWCXEH0PiCc7TswLvXXZXmSVet03Ys0U="}]}, {"Route": "template/Input.cs.nago0bencd.vm", "AssetFile": "template/Input.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7236"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"geueeXb2OfaZ3pGxdpESDplZblZwHdReJ+ud6bOWT68=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nago0bencd"}, {"Name": "integrity", "Value": "sha256-geueeXb2OfaZ3pGxdpESDplZblZwHdReJ+ud6bOWT68="}, {"Name": "label", "Value": "template/Input.cs.vm"}]}, {"Route": "template/Input.cs.vm", "AssetFile": "template/Input.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7236"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"geueeXb2OfaZ3pGxdpESDplZblZwHdReJ+ud6bOWT68=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geueeXb2OfaZ3pGxdpESDplZblZwHdReJ+ud6bOWT68="}]}, {"Route": "template/Output.cs.t5kbegls1c.vm", "AssetFile": "template/Output.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2453"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0Zh1wgX4oeQvYoG9a8vc5BwDzoSln6XeSSVHntj0w/A=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t5kbegls1c"}, {"Name": "integrity", "Value": "sha256-0Zh1wgX4oeQvYoG9a8vc5BwDzoSln6XeSSVHntj0w/A="}, {"Name": "label", "Value": "template/Output.cs.vm"}]}, {"Route": "template/Output.cs.vm", "AssetFile": "template/Output.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2453"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0Zh1wgX4oeQvYoG9a8vc5BwDzoSln6XeSSVHntj0w/A=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0Zh1wgX4oeQvYoG9a8vc5BwDzoSln6XeSSVHntj0w/A="}]}, {"Route": "template/SeedData.cs.us9yshxka1.vm", "AssetFile": "template/SeedData.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1149"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"4gmpwHIGRA3OXE7RGaIKLOHJnJxIPC/B8EHYXQLrdQ8=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "us9yshxka1"}, {"Name": "integrity", "Value": "sha256-4gmpwHIGRA3OXE7RGaIKLOHJnJxIPC/B8EHYXQLrdQ8="}, {"Name": "label", "Value": "template/SeedData.cs.vm"}]}, {"Route": "template/SeedData.cs.vm", "AssetFile": "template/SeedData.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1149"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"4gmpwHIGRA3OXE7RGaIKLOHJnJxIPC/B8EHYXQLrdQ8=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4gmpwHIGRA3OXE7RGaIKLOHJnJxIPC/B8EHYXQLrdQ8="}]}, {"Route": "template/Service.cs.4lrwgxpcio.vm", "AssetFile": "template/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "21326"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"WOh559bzcZKpxasrX1fjZqARWH5Spa34aK2JgE3Dch4=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4lrwgxpcio"}, {"Name": "integrity", "Value": "sha256-WOh559bzcZKpxasrX1fjZqARWH5Spa34aK2JgE3Dch4="}, {"Name": "label", "Value": "template/Service.cs.vm"}]}, {"Route": "template/Service.cs.vm", "AssetFile": "template/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "21326"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"WOh559bzcZKpxasrX1fjZqARWH5Spa34aK2JgE3Dch4=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WOh559bzcZKpxasrX1fjZqARWH5Spa34aK2JgE3Dch4="}]}, {"Route": "template/api.ts.7xhqoadx3i.vm", "AssetFile": "template/api.ts.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1947"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"O20JdNr7irQ0VBm6Gx0733b+cGL2pPNPopmb0gHMMsc=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7xhqoadx3i"}, {"Name": "integrity", "Value": "sha256-O20JdNr7irQ0VBm6Gx0733b+cGL2pPNPopmb0gHMMsc="}, {"Name": "label", "Value": "template/api.ts.vm"}]}, {"Route": "template/api.ts.vm", "AssetFile": "template/api.ts.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1947"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"O20JdNr7irQ0VBm6Gx0733b+cGL2pPNPopmb0gHMMsc=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O20JdNr7irQ0VBm6Gx0733b+cGL2pPNPopmb0gHMMsc="}]}, {"Route": "template/data.data.ts.1ls0y0ljjz.vm", "AssetFile": "template/data.data.ts.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4782"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"tQIyjWgbguBvPICyajItesFiG9LOiOkctu+bfNpxa3E=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1ls0y0ljjz"}, {"Name": "integrity", "Value": "sha256-tQIyjWgbguBvPICyajItesFiG9LOiOkctu+bfNpxa3E="}, {"Name": "label", "Value": "template/data.data.ts.vm"}]}, {"Route": "template/data.data.ts.vm", "AssetFile": "template/data.data.ts.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4782"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"tQIyjWgbguBvPICyajItesFiG9LOiOkctu+bfNpxa3E=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tQIyjWgbguBvPICyajItesFiG9LOiOkctu+bfNpxa3E="}]}, {"Route": "template/dataModal.vue.js73fnatoh.vm", "AssetFile": "template/dataModal.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2607"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"M1VXyaFY9uYEVbKzX00ZKbwmC//cSoBmXFRli9uRjeE=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "js73fnatoh"}, {"Name": "integrity", "Value": "sha256-M1VXyaFY9uYEVbKzX00ZKbwmC//cSoBmXFRli9uRjeE="}, {"Name": "label", "Value": "template/dataModal.vue.vm"}]}, {"Route": "template/dataModal.vue.vm", "AssetFile": "template/dataModal.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2607"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"M1VXyaFY9uYEVbKzX00ZKbwmC//cSoBmXFRli9uRjeE=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M1VXyaFY9uYEVbKzX00ZKbwmC//cSoBmXFRli9uRjeE="}]}, {"Route": "template/editDialog.vue.4q9uqurpsl.vm", "AssetFile": "template/editDialog.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8301"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"vA7a3gJ5X75eafIDqgLZq00QBkvlKNUWA8r/Zhm/SGQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4q9uqurpsl"}, {"Name": "integrity", "Value": "sha256-vA7a3gJ5X75eafIDqgLZq00QBkvlKNUWA8r/Zhm/SGQ="}, {"Name": "label", "Value": "template/editDialog.vue.vm"}]}, {"Route": "template/editDialog.vue.vm", "AssetFile": "template/editDialog.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8301"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"vA7a3gJ5X75eafIDqgLZq00QBkvlKNUWA8r/Zhm/SGQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vA7a3gJ5X75eafIDqgLZq00QBkvlKNUWA8r/Zhm/SGQ="}]}, {"Route": "template/index.vue.vm", "AssetFile": "template/index.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "18294"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"4Ezik7dAEHUFzJp+pUhCaLS52kTNrC4y10Jk2MYX9Ig=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4Ezik7dAEHUFzJp+pUhCaLS52kTNrC4y10Jk2MYX9Ig="}]}, {"Route": "template/index.vue.w0skud15m3.vm", "AssetFile": "template/index.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "18294"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"4Ezik7dAEHUFzJp+pUhCaLS52kTNrC4y10Jk2MYX9Ig=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w0skud15m3"}, {"Name": "integrity", "Value": "sha256-4Ezik7dAEHUFzJp+pUhCaLS52kTNrC4y10Jk2MYX9Ig="}, {"Name": "label", "Value": "template/index.vue.vm"}]}, {"Route": "upload/logo.3fy106m53y.png", "AssetFile": "upload/logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8024"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3fy106m53y"}, {"Name": "integrity", "Value": "sha256-gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0="}, {"Name": "label", "Value": "upload/logo.png"}]}, {"Route": "upload/logo.png", "AssetFile": "upload/logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8024"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0="}]}]}