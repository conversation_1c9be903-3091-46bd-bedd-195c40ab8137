namespace Admin.NET.Application.Service.EmqxRealtime.DTOs;

/// <summary>
/// 订阅信息
/// </summary>
public class SubscriptionInfo
{
    /// <summary>
    /// 连接ID
    /// </summary>
    public string ConnectionId { get; set; } = string.Empty;

    /// <summary>
    /// MQTT实例ID
    /// </summary>
    public int InstanceId { get; set; }

    /// <summary>
    /// 订阅的主题列表
    /// </summary>
    public List<string> Topics { get; set; } = new();

    /// <summary>
    /// 是否订阅所有主题
    /// </summary>
    public bool SubscribeToAllTopics { get; set; }

    /// <summary>
    /// 设备分组
    /// </summary>
    public string? DeviceGroup { get; set; }

    /// <summary>
    /// 订阅时间
    /// </summary>
    public DateTime SubscribeTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后活跃时间
    /// </summary>
    public DateTime LastActiveTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 接收消息数量
    /// </summary>
    public long ReceivedMessageCount { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// 客户端IP地址
    /// </summary>
    public string? ClientIpAddress { get; set; }

    /// <summary>
    /// 用户代理
    /// </summary>
    public string? UserAgent { get; set; }

    /// <summary>
    /// 是否包含历史消息
    /// </summary>
    public bool IncludeHistory { get; set; }

    /// <summary>
    /// 最大历史消息数
    /// </summary>
    public int MaxHistoryCount { get; set; } = 100;
}