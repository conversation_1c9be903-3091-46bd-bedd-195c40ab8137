using Admin.NET.Application.Service.EmqxRealtime.DTOs;
using Admin.NET.Application.Service.EmqxRealtime.Interfaces;
using Admin.NET.Application.Entity;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Admin.NET.Application.Service.EmqxRealtime.Controllers;

/// <summary>
/// 设备分组管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class DeviceGroupController : ControllerBase
{
    private readonly ILogger<DeviceGroupController> _logger;
    private readonly IDeviceGroupService _deviceGroupService;

    public DeviceGroupController(
        ILogger<DeviceGroupController> logger,
        IDeviceGroupService deviceGroupService)
    {
        _logger = logger;
        _deviceGroupService = deviceGroupService;
    }

    /// <summary>
    /// 获取所有设备分组
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> GetAllGroups()
    {
        try
        {
            var groups = await _deviceGroupService.GetAllGroupsAsync();
            return Ok(new { success = true, data = groups });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备分组列表失败");
            return StatusCode(500, new { success = false, message = "获取设备分组列表失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 根据ID获取设备分组
    /// </summary>
    /// <param name="id">分组ID</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetGroupById(long id)
    {
        try
        {
            if (id <= 0)
            {
                return BadRequest(new { success = false, message = "无效的分组ID" });
            }

            var group = await _deviceGroupService.GetGroupByIdAsync(id);
            if (group == null)
            {
                return NotFound(new { success = false, message = "设备分组不存在" });
            }

            return Ok(new { success = true, data = group });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备分组失败: {GroupId}", id);
            return StatusCode(500, new { success = false, message = "获取设备分组失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 根据名称获取设备分组
    /// </summary>
    /// <param name="name">分组名称</param>
    /// <returns></returns>
    [HttpGet("by-name/{name}")]
    public async Task<IActionResult> GetGroupByName(string name)
    {
        try
        {
            if (string.IsNullOrEmpty(name))
            {
                return BadRequest(new { success = false, message = "分组名称不能为空" });
            }

            var group = await _deviceGroupService.GetGroupByNameAsync(name);
            if (group == null)
            {
                return NotFound(new { success = false, message = "设备分组不存在" });
            }

            return Ok(new { success = true, data = group });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据名称获取设备分组失败: {GroupName}", name);
            return StatusCode(500, new { success = false, message = "获取设备分组失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 创建设备分组
    /// </summary>
    /// <param name="dto">设备分组信息</param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> CreateGroup([FromBody] DeviceGroupDto dto)
    {
        try
        {
            if (dto == null)
            {
                return BadRequest(new { success = false, message = "请求数据不能为空" });
            }

            if (string.IsNullOrEmpty(dto.Name))
            {
                return BadRequest(new { success = false, message = "分组名称不能为空" });
            }

            // 检查名称是否已存在
            var existingGroup = await _deviceGroupService.GetGroupByNameAsync(dto.Name);
            if (existingGroup != null)
            {
                return Conflict(new { success = false, message = "分组名称已存在" });
            }

            var deviceGroup = new MqttDeviceGroup
            {
                GroupName = dto.Name,
                Description = dto.Description,
                DeviceIdPattern = dto.DeviceIdPattern,
                TopicPrefix = dto.TopicPrefix,
                IsEnabled = dto.IsEnabled,
                Sort = dto.Sort,
                CreateTime = DateTime.Now
            };

            var createdGroupDto = new DeviceGroupDto
            {
                Name = deviceGroup.GroupName,
                Description = deviceGroup.Description,
                DeviceIdPattern = deviceGroup.DeviceIdPattern,
                TopicPrefix = deviceGroup.TopicPrefix,
                IsEnabled = deviceGroup.IsEnabled,
                Sort = deviceGroup.Sort
            };
            
            var createdGroupId = await _deviceGroupService.CreateGroupAsync(createdGroupDto);
            return CreatedAtAction(nameof(GetGroupById), new { id = createdGroupId }, 
                new { success = true, data = new { Id = createdGroupId } });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建设备分组失败");
            return StatusCode(500, new { success = false, message = "创建设备分组失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 更新设备分组
    /// </summary>
    /// <param name="id">分组ID</param>
    /// <param name="dto">设备分组信息</param>
    /// <returns></returns>
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateGroup(long id, [FromBody] DeviceGroupDto dto)
    {
        try
        {
            if (id <= 0)
            {
                return BadRequest(new { success = false, message = "无效的分组ID" });
            }

            if (dto == null)
            {
                return BadRequest(new { success = false, message = "请求数据不能为空" });
            }

            if (string.IsNullOrEmpty(dto.Name))
            {
                return BadRequest(new { success = false, message = "分组名称不能为空" });
            }

            var existingGroup = await _deviceGroupService.GetGroupByIdAsync(id);
            if (existingGroup == null)
            {
                return NotFound(new { success = false, message = "设备分组不存在" });
            }

            // 检查名称是否与其他分组冲突
            var groupWithSameName = await _deviceGroupService.GetGroupByNameAsync(dto.Name);
            if (groupWithSameName != null && groupWithSameName.Id != id)
            {
                return Conflict(new { success = false, message = "分组名称已存在" });
            }

            var updatedDto = new DeviceGroupDto
            {
                Id = id,
                Name = dto.Name,
                Description = dto.Description,
                DeviceIdPattern = dto.DeviceIdPattern,
                TopicPrefix = dto.TopicPrefix,
                IsEnabled = dto.IsEnabled,
                Sort = dto.Sort
            };

            var success = await _deviceGroupService.UpdateGroupAsync(id, updatedDto);
            if (success)
            {
                return Ok(new { success = true, message = "更新成功" });
            }
            else
            {
                return StatusCode(500, new { success = false, message = "更新失败" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新设备分组失败: {GroupId}", id);
            return StatusCode(500, new { success = false, message = "更新设备分组失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 删除设备分组
    /// </summary>
    /// <param name="id">分组ID</param>
    /// <returns></returns>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteGroup(long id)
    {
        try
        {
            if (id <= 0)
            {
                return BadRequest(new { success = false, message = "无效的分组ID" });
            }

            var existingGroup = await _deviceGroupService.GetGroupByIdAsync(id);
            if (existingGroup == null)
            {
                return NotFound(new { success = false, message = "设备分组不存在" });
            }

            var success = await _deviceGroupService.DeleteGroupAsync(id);
            if (success)
            {
                return Ok(new { success = true, message = "设备分组删除成功" });
            }
            else
            {
                return StatusCode(500, new { success = false, message = "删除设备分组失败" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除设备分组失败: {GroupId}", id);
            return StatusCode(500, new { success = false, message = "删除设备分组失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 根据设备ID匹配分组
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns></returns>
    [HttpGet("match-by-device/{deviceId}")]
    public async Task<IActionResult> GetGroupByDeviceId(string deviceId)
    {
        try
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                return BadRequest(new { success = false, message = "设备ID不能为空" });
            }

            var group = await _deviceGroupService.GetGroupByDeviceIdAsync(deviceId);
            if (group == null)
            {
                return NotFound(new { success = false, message = "未找到匹配的设备分组" });
            }

            return Ok(new { success = true, data = group });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据设备ID匹配分组失败: {DeviceId}", deviceId);
            return StatusCode(500, new { success = false, message = "匹配设备分组失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 根据主题匹配分组
    /// </summary>
    /// <param name="topic">主题</param>
    /// <returns></returns>
    [HttpGet("match-by-topic")]
    public async Task<IActionResult> GetGroupByTopic([FromQuery] string topic)
    {
        try
        {
            if (string.IsNullOrEmpty(topic))
            {
                return BadRequest(new { success = false, message = "主题不能为空" });
            }

            var group = await _deviceGroupService.GetGroupByTopicAsync(topic);
            if (group == null)
            {
                return NotFound(new { success = false, message = "未找到匹配的设备分组" });
            }

            return Ok(new { success = true, data = group });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据主题匹配分组失败: {Topic}", topic);
            return StatusCode(500, new { success = false, message = "匹配设备分组失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 获取分组内的设备列表
    /// </summary>
    /// <param name="groupId">分组ID</param>
    /// <returns></returns>
    [HttpGet("{groupId}/devices")]
    public async Task<IActionResult> GetDevicesInGroup(long groupId)
    {
        try
        {
            if (groupId <= 0)
            {
                return BadRequest(new { success = false, message = "无效的分组ID" });
            }

            var devices = await _deviceGroupService.GetDevicesInGroupAsync(groupId);
            return Ok(new { success = true, data = devices });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取分组设备列表失败: {GroupId}", groupId);
            return StatusCode(500, new { success = false, message = "获取分组设备列表失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 添加设备到分组
    /// </summary>
    /// <param name="groupId">分组ID</param>
    /// <param name="deviceId">设备ID</param>
    /// <returns></returns>
    [HttpPost("{groupId}/devices/{deviceId}")]
    public async Task<IActionResult> AddDeviceToGroup(long groupId, string deviceId)
    {
        try
        {
            if (groupId <= 0)
            {
                return BadRequest(new { success = false, message = "无效的分组ID" });
            }

            if (string.IsNullOrEmpty(deviceId))
            {
                return BadRequest(new { success = false, message = "设备ID不能为空" });
            }

            var success = await _deviceGroupService.AddDevicesToGroupAsync(groupId, new List<string> { deviceId });
            if (success)
            {
                return Ok(new { success = true, message = "设备添加到分组成功" });
            }
            else
            {
                return StatusCode(500, new { success = false, message = "添加设备到分组失败" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加设备到分组失败: GroupId={GroupId}, DeviceId={DeviceId}", groupId, deviceId);
            return StatusCode(500, new { success = false, message = "添加设备到分组失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 从分组中移除设备
    /// </summary>
    /// <param name="groupId">分组ID</param>
    /// <param name="deviceId">设备ID</param>
    /// <returns></returns>
    [HttpDelete("{groupId}/devices/{deviceId}")]
    public async Task<IActionResult> RemoveDeviceFromGroup(long groupId, string deviceId)
    {
        try
        {
            if (groupId <= 0)
            {
                return BadRequest(new { success = false, message = "无效的分组ID" });
            }

            if (string.IsNullOrEmpty(deviceId))
            {
                return BadRequest(new { success = false, message = "设备ID不能为空" });
            }

            var success = await _deviceGroupService.RemoveDevicesFromGroupAsync(groupId, new List<string> { deviceId });
            if (success)
            {
                return Ok(new { success = true, message = "设备从分组中移除成功" });
            }
            else
            {
                return StatusCode(500, new { success = false, message = "从分组中移除设备失败" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从分组中移除设备失败: GroupId={GroupId}, DeviceId={DeviceId}", groupId, deviceId);
            return StatusCode(500, new { success = false, message = "从分组中移除设备失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 启用/禁用设备分组
    /// </summary>
    /// <param name="id">分组ID</param>
    /// <param name="enabled">是否启用</param>
    /// <returns></returns>
    [HttpPatch("{id}/status")]
    public async Task<IActionResult> UpdateGroupStatus(long id, [FromQuery] bool enabled)
    {
        try
        {
            if (id <= 0)
            {
                return BadRequest(new { success = false, message = "无效的分组ID" });
            }

            var existingGroup = await _deviceGroupService.GetGroupByIdAsync(id);
            if (existingGroup == null)
            {
                return NotFound(new { success = false, message = "设备分组不存在" });
            }

            var updatedDto = new DeviceGroupDto
            {
                Id = id,
                Name = existingGroup.Name,
                Description = existingGroup.Description,
                DeviceIdPattern = existingGroup.DeviceIdPattern,
                TopicPrefix = existingGroup.TopicPrefix,
                IsEnabled = enabled,
                Sort = existingGroup.Sort
            };

            var success = await _deviceGroupService.UpdateGroupAsync(id, updatedDto);
            if (success)
            {
                return Ok(new { success = true, message = $"设备分组已{(enabled ? "启用" : "禁用")}" });
            }
            else
            {
                return StatusCode(500, new { success = false, message = "更新状态失败" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新设备分组状态失败: {GroupId}", id);
            return StatusCode(500, new { success = false, message = "更新设备分组状态失败", details = ex.Message });
        }
    }

    #region 私有方法

    private long? GetCurrentUserId()
    {
        var userIdClaim = User?.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
        return long.TryParse(userIdClaim, out var userId) ? userId : null;
    }

    #endregion
}