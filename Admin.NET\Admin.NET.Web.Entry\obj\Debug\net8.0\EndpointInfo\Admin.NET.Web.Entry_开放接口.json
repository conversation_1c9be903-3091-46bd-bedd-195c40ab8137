{"openapi": "3.0.4", "info": {"title": "开放接口", "description": "<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>", "version": "1.0.0"}, "paths": {"/api/demo/helloWord": {"get": {"tags": ["demo"], "operationId": "api-demo-helloWord-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_String"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_String"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_String"}}}}}}}}, "components": {"schemas": {"AdminResult_String": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"type": "string", "description": "数据", "nullable": true}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}], "tags": [{"name": "demo", "description": "示例开放接口"}]}