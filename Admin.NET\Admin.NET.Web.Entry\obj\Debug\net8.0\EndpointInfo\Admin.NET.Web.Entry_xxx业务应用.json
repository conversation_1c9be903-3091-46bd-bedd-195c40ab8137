{"openapi": "3.0.4", "info": {"title": "xxx业务应用", "description": "<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>", "version": "1.0.0"}, "paths": {"/api/emqxRealtime/connect": {"post": {"tags": ["emqxRealtime"], "summary": "连接到EMQX服务器 🔗", "operationId": "api-emqxRealtime-connect-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/EmqxConnectInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EmqxConnectInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmqxConnectInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmqxConnectInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/EmqxConnectInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}}}}}}, "/api/emqxRealtime/disconnect": {"post": {"tags": ["emqxRealtime"], "summary": "断开EMQX连接 ❌", "operationId": "api-emqxRealtime-disconnect-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/EmqxDisconnectInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EmqxDisconnectInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmqxDisconnectInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmqxDisconnectInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/EmqxDisconnectInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}}}}}}, "/api/emqxRealtime/publish": {"post": {"tags": ["emqxRealtime"], "summary": "发布消息到EMQX 📤", "operationId": "api-emqxRealtime-publish-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/EmqxPublishInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EmqxPublishInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmqxPublishInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmqxPublishInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/EmqxPublishInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}}}}}}, "/api/emqxRealtime/subscribe": {"post": {"tags": ["emqxRealtime"], "summary": "订阅EMQX主题 📥", "operationId": "api-emqxRealtime-subscribe-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/EmqxSubscribeInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EmqxSubscribeInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmqxSubscribeInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmqxSubscribeInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/EmqxSubscribeInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}}}}}}, "/api/emqxRealtime/unsubscribe": {"post": {"tags": ["emqxRealtime"], "summary": "取消订阅EMQX主题 📤", "operationId": "api-emqxRealtime-unsubscribe-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/EmqxUnsubscribeInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EmqxUnsubscribeInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmqxUnsubscribeInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmqxUnsubscribeInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/EmqxUnsubscribeInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}}}}}}, "/api/emqxRealtime/getConnectionStatus": {"get": {"tags": ["emqxRealtime"], "summary": "获取连接状态 📊", "operationId": "api-emqxRealtime-getConnectionStatus-Get", "parameters": [{"name": "InstanceId", "in": "query", "description": "MQTT实例ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_EmqxConnectionStatusOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_EmqxConnectionStatusOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_EmqxConnectionStatusOutput"}}}}}}}, "/api/emqxRealtime/getActiveConnections": {"get": {"tags": ["emqxRealtime"], "summary": "获取所有活跃连接 📋", "operationId": "api-emqxRealtime-getActiveConnections-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_EmqxActiveConnectionsOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_EmqxActiveConnectionsOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_EmqxActiveConnectionsOutput"}}}}}}}, "/api/emqxRealtime/batchConnect": {"post": {"tags": ["emqxRealtime"], "summary": "批量连接EMQX实例 🔗", "operationId": "api-emqxRealtime-batchConnect-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/EmqxBatchConnectInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EmqxBatchConnectInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmqxBatchConnectInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmqxBatchConnectInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/EmqxBatchConnectInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_EmqxBatchOperationOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_EmqxBatchOperationOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_EmqxBatchOperationOutput"}}}}}}}, "/api/emqxRealtime/batchDisconnect": {"post": {"tags": ["emqxRealtime"], "summary": "批量断开EMQX连接 ❌", "operationId": "api-emqxRealtime-batchDisconnect-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/EmqxBatchDisconnectInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EmqxBatchDisconnectInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmqxBatchDisconnectInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmqxBatchDisconnectInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/EmqxBatchDisconnectInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_EmqxBatchOperationOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_EmqxBatchOperationOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_EmqxBatchOperationOutput"}}}}}}}, "/api/emqxRealtime/autoGroupDevice": {"post": {"tags": ["emqxRealtime"], "summary": "自动分组设备 🏷️", "operationId": "api-emqxRealtime-autoGroupDevice-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AutoGroupDeviceInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AutoGroupDeviceInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AutoGroupDeviceInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AutoGroupDeviceInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/AutoGroupDeviceInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_AutoGroupDeviceOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_AutoGroupDeviceOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_AutoGroupDeviceOutput"}}}}}}}, "/api/emqxRealtime/generateClientId": {"post": {"tags": ["emqxRealtime"], "summary": "生成标准ClientId 🆔", "operationId": "api-emqxRealtime-generateClientId-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GenerateClientIdInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GenerateClientIdInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GenerateClientIdInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GenerateClientIdInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/GenerateClientIdInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_GenerateClientIdOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_GenerateClientIdOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_GenerateClientIdOutput"}}}}}}}, "/api/emqxRealtime/validateClientId": {"post": {"tags": ["emqxRealtime"], "summary": "验证ClientId格式 ✅", "operationId": "api-emqxRealtime-validateClientId-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ValidateClientIdInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ValidateClientIdInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ValidateClientIdInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ValidateClientIdInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/ValidateClientIdInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_ValidateClientIdOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_ValidateClientIdOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_ValidateClientIdOutput"}}}}}}}, "/api/emqxRealtime/getDeviceGroupRealtimeStats": {"get": {"tags": ["emqxRealtime"], "summary": "获取设备组实时统计 📊", "operationId": "api-emqxRealtime-getDeviceGroupRealtimeStats-Get", "parameters": [{"name": "InstanceId", "in": "query", "description": "MQTT实例ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_List_DeviceGroupRealtimeStats"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_List_DeviceGroupRealtimeStats"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_List_DeviceGroupRealtimeStats"}}}}}}}, "/api/emqxRealtime/getInstanceDeviceOverview": {"get": {"tags": ["emqxRealtime"], "summary": "获取实例设备分布概览 🗺️", "operationId": "api-emqxRealtime-getInstanceDeviceOverview-Get", "parameters": [{"name": "InstanceId", "in": "query", "description": "MQTT实例ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_InstanceDeviceOverviewOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_InstanceDeviceOverviewOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_InstanceDeviceOverviewOutput"}}}}}}}, "/api/emqxRealtime/getClientIdSuggestions": {"get": {"tags": ["emqxRealtime"], "summary": "获取ClientId建议 💡", "operationId": "api-emqxRealtime-getClientIdSuggestions-Get", "parameters": [{"name": "DeviceType", "in": "query", "description": "设备类型", "required": true, "schema": {"type": "string"}}, {"name": "Location", "in": "query", "description": "设备位置", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_ClientIdSuggestionsOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_ClientIdSuggestionsOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_ClientIdSuggestionsOutput"}}}}}}}, "/api/mqttMessageHistory/page": {"post": {"tags": ["mqttMessageHistory"], "summary": "分页查询MQTT消息历史记录 🔖", "operationId": "api-mqttMessageHistory-page-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/PageMqttMessageHistoryInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PageMqttMessageHistoryInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PageMqttMessageHistoryInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PageMqttMessageHistoryInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/PageMqttMessageHistoryInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttMessageHistoryOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttMessageHistoryOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttMessageHistoryOutput"}}}}}}}, "/api/mqttMessageHistory/detail": {"get": {"tags": ["mqttMessageHistory"], "summary": "获取MQTT消息历史记录详情 ℹ️", "operationId": "api-mqttMessageHistory-detail-Get", "parameters": [{"name": "Id", "in": "query", "description": "主键Id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttMessageHistory"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttMessageHistory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttMessageHistory"}}}}}}}, "/api/mqttMessageHistory/record": {"post": {"tags": ["mqttMessageHistory"], "summary": "记录MQTT消息 ➕", "operationId": "api-mqttMessageHistory-record-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RecordMqttMessageInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RecordMqttMessageInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RecordMqttMessageInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RecordMqttMessageInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/RecordMqttMessageInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}}}}}}, "/api/mqttMessageHistory/batchRecord": {"post": {"tags": ["mqttMessageHistory"], "summary": "批量记录MQTT消息 ➕", "operationId": "api-mqttMessageHistory-batchRecord-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/BatchRecordMqttMessageInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BatchRecordMqttMessageInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BatchRecordMqttMessageInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BatchRecordMqttMessageInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/BatchRecordMqttMessageInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_BatchRecordMqttMessageOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_BatchRecordMqttMessageOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_BatchRecordMqttMessageOutput"}}}}}}}, "/api/mqttMessageHistory/updateProcessStatus": {"post": {"tags": ["mqttMessageHistory"], "summary": "更新消息处理状态 ✏️", "operationId": "api-mqttMessageHistory-updateProcessStatus-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateMessageProcessStatusInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateMessageProcessStatusInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateMessageProcessStatusInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateMessageProcessStatusInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/UpdateMessageProcessStatusInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Boolean"}}}}}}}, "/api/mqttMessageHistory/delete": {"post": {"tags": ["mqttMessageHistory"], "summary": "删除MQTT消息历史记录 🗑️", "operationId": "api-mqttMessageHistory-delete-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DeleteMqttMessageHistoryInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeleteMqttMessageHistoryInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteMqttMessageHistoryInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteMqttMessageHistoryInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/DeleteMqttMessageHistoryInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttMessageHistory/batchDelete": {"post": {"tags": ["mqttMessageHistory"], "summary": "批量删除MQTT消息历史记录 🗑️", "operationId": "api-mqttMessageHistory-batchDelete-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/BatchDeleteMqttMessageHistoryInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BatchDeleteMqttMessageHistoryInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BatchDeleteMqttMessageHistoryInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BatchDeleteMqttMessageHistoryInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/BatchDeleteMqttMessageHistoryInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttMessageHistory/cleanHistory": {"post": {"tags": ["mqttMessageHistory"], "summary": "清理历史消息 🧹", "operationId": "api-mqttMessageHistory-cleanHistory-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CleanHistoryInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CleanHistoryInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CleanHistoryInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CleanHistoryInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/CleanHistoryInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_CleanHistoryOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_CleanHistoryOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_CleanHistoryOutput"}}}}}}}, "/api/mqttMessageHistory/getStatistics": {"get": {"tags": ["mqttMessageHistory"], "summary": "获取消息统计信息 📊", "operationId": "api-mqttMessageHistory-getStatistics-Get", "parameters": [{"name": "InstanceId", "in": "query", "description": "MQTT实例ID（可选）", "schema": {"type": "integer", "format": "int64"}}, {"name": "StartDate", "in": "query", "description": "开始日期", "schema": {"type": "string", "format": "date-time", "example": "2025-08-05 14:17:44"}}, {"name": "EndDate", "in": "query", "description": "结束日期", "schema": {"type": "string", "format": "date-time", "example": "2025-08-05 14:17:44"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttMessageStatisticsOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttMessageStatisticsOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttMessageStatisticsOutput"}}}}}}}, "/api/mqttClientAuth/page": {"post": {"tags": ["mqttClientAuth"], "summary": "分页查询MQTT 客户端认证信息表 🔖", "operationId": "api-mqttClientAuth-page-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/PageMqttClientAuthInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PageMqttClientAuthInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PageMqttClientAuthInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PageMqttClientAuthInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/PageMqttClientAuthInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttClientAuthOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttClientAuthOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttClientAuthOutput"}}}}}}}, "/api/mqttClientAuth/detail": {"get": {"tags": ["mqttClientAuth"], "summary": "获取MQTT 客户端认证信息表详情 ℹ️", "operationId": "api-mqttClientAuth-detail-Get", "parameters": [{"name": "Id", "in": "query", "description": "主键Id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttClientAuth"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttClientAuth"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttClientAuth"}}}}}}}, "/api/mqttClientAuth/add": {"post": {"tags": ["mqttClientAuth"], "summary": "增加MQTT 客户端认证信息表 ➕", "operationId": "api-mqttClientAuth-add-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AddMqttClientAuthInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddMqttClientAuthInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddMqttClientAuthInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddMqttClientAuthInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/AddMqttClientAuthInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}}}}}}, "/api/mqttClientAuth/update": {"post": {"tags": ["mqttClientAuth"], "summary": "更新MQTT 客户端认证信息表 ✏️", "operationId": "api-mqttClientAuth-update-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateMqttClientAuthInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateMqttClientAuthInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateMqttClientAuthInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateMqttClientAuthInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/UpdateMqttClientAuthInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttClientAuth/delete": {"post": {"tags": ["mqttClientAuth"], "summary": "删除MQTT 客户端认证信息表 ❌", "operationId": "api-mqttClientAuth-delete-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DeleteMqttClientAuthInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeleteMqttClientAuthInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteMqttClientAuthInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteMqttClientAuthInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/DeleteMqttClientAuthInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttClientAuth/batchDelete": {"post": {"tags": ["mqttClientAuth"], "summary": "批量删除MQTT 客户端认证信息表 ❌", "operationId": "api-mqttClientAuth-batchDelete-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttClientAuthInput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttClientAuthInput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttClientAuthInput"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttClientAuthInput"}}}, "text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttClientAuthInput"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Int32"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int32"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int32"}}}}}}}, "/api/mqttClient/page": {"post": {"tags": ["mqttClient"], "summary": "分页查询MQTT 客户端连接信息表 🔖", "operationId": "api-mqttClient-page-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/PageMqttClientInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PageMqttClientInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PageMqttClientInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PageMqttClientInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/PageMqttClientInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttClientOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttClientOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttClientOutput"}}}}}}}, "/api/mqttClient/detail": {"get": {"tags": ["mqttClient"], "summary": "获取MQTT 客户端连接信息表详情 ℹ️", "operationId": "api-mqttClient-detail-Get", "parameters": [{"name": "Id", "in": "query", "description": "主键Id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttClient"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttClient"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttClient"}}}}}}}, "/api/mqttClient/add": {"post": {"tags": ["mqttClient"], "summary": "增加MQTT 客户端连接信息表 ➕", "operationId": "api-mqttClient-add-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AddMqttClientInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddMqttClientInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddMqttClientInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddMqttClientInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/AddMqttClientInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}}}}}}, "/api/mqttClient/update": {"post": {"tags": ["mqttClient"], "summary": "更新MQTT 客户端连接信息表 ✏️", "operationId": "api-mqttClient-update-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateMqttClientInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateMqttClientInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateMqttClientInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateMqttClientInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/UpdateMqttClientInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttClient/delete": {"post": {"tags": ["mqttClient"], "summary": "删除MQTT 客户端连接信息表 ❌", "operationId": "api-mqttClient-delete-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DeleteMqttClientInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeleteMqttClientInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteMqttClientInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteMqttClientInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/DeleteMqttClientInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttClient/batchDelete": {"post": {"tags": ["mqttClient"], "summary": "批量删除MQTT 客户端连接信息表 ❌", "operationId": "api-mqttClient-batchDelete-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttClientInput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttClientInput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttClientInput"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttClientInput"}}}, "text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttClientInput"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Int32"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int32"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int32"}}}}}}}, "/api/mqttDeviceGroup/page": {"post": {"tags": ["mqttDeviceGroup"], "summary": "分页查询MQTT设备组管理表 🔖", "operationId": "api-mqttDeviceGroup-page-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/PageMqttDeviceGroupInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PageMqttDeviceGroupInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PageMqttDeviceGroupInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PageMqttDeviceGroupInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/PageMqttDeviceGroupInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttDeviceGroupOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttDeviceGroupOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttDeviceGroupOutput"}}}}}}}, "/api/mqttDeviceGroup/detail": {"get": {"tags": ["mqttDeviceGroup"], "summary": "获取MQTT设备组管理表详情 ℹ️", "operationId": "api-mqttDeviceGroup-detail-Get", "parameters": [{"name": "Id", "in": "query", "description": "主键Id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttDeviceGroup"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttDeviceGroup"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttDeviceGroup"}}}}}}}, "/api/mqttDeviceGroup/add": {"post": {"tags": ["mqttDeviceGroup"], "summary": "增加MQTT设备组管理表 ➕", "operationId": "api-mqttDeviceGroup-add-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AddMqttDeviceGroupInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddMqttDeviceGroupInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddMqttDeviceGroupInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddMqttDeviceGroupInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/AddMqttDeviceGroupInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}}}}}}, "/api/mqttDeviceGroup/update": {"post": {"tags": ["mqttDeviceGroup"], "summary": "更新MQTT设备组管理表 ✏️", "operationId": "api-mqttDeviceGroup-update-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateMqttDeviceGroupInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateMqttDeviceGroupInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateMqttDeviceGroupInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateMqttDeviceGroupInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/UpdateMqttDeviceGroupInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttDeviceGroup/delete": {"post": {"tags": ["mqttDeviceGroup"], "summary": "删除MQTT设备组管理表 ❌", "operationId": "api-mqttDeviceGroup-delete-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DeleteMqttDeviceGroupInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeleteMqttDeviceGroupInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteMqttDeviceGroupInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteMqttDeviceGroupInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/DeleteMqttDeviceGroupInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttDeviceGroup/batchDelete": {"post": {"tags": ["mqttDeviceGroup"], "summary": "批量删除MQTT设备组管理表 ❌", "operationId": "api-mqttDeviceGroup-batchDelete-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttDeviceGroupInput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttDeviceGroupInput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttDeviceGroupInput"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttDeviceGroupInput"}}}, "text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttDeviceGroupInput"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Int32"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int32"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int32"}}}}}}}, "/api/mqttDeviceGroup/export": {"post": {"tags": ["mqttDeviceGroup"], "summary": "导出MQTT设备组管理表记录 🔖", "operationId": "api-mqttDeviceGroup-export-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/PageMqttDeviceGroupInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PageMqttDeviceGroupInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PageMqttDeviceGroupInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PageMqttDeviceGroupInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/PageMqttDeviceGroupInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttDeviceGroup/import": {"get": {"tags": ["mqttDeviceGroup"], "summary": "下载MQTT设备组管理表数据导入模板 ⬇️", "operationId": "api-mqttDeviceGroup-import-Get", "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["mqttDeviceGroup"], "summary": "导入MQTT设备组管理表记录 💾", "operationId": "api-mqttDeviceGroup-import-Post", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttInstance/page": {"post": {"tags": ["mqttInstance"], "summary": "分页查询MQTT 实例信息表 🔖", "operationId": "api-mqttInstance-page-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/PageMqttInstanceInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PageMqttInstanceInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PageMqttInstanceInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PageMqttInstanceInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/PageMqttInstanceInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttInstanceOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttInstanceOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttInstanceOutput"}}}}}}}, "/api/mqttInstance/detail": {"get": {"tags": ["mqttInstance"], "summary": "获取MQTT 实例信息表详情 ℹ️", "operationId": "api-mqttInstance-detail-Get", "parameters": [{"name": "Id", "in": "query", "description": "主键Id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttInstance"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttInstance"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttInstance"}}}}}}}, "/api/mqttInstance/add": {"post": {"tags": ["mqttInstance"], "summary": "增加MQTT 实例信息表 ➕", "operationId": "api-mqttInstance-add-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AddMqttInstanceInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddMqttInstanceInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddMqttInstanceInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddMqttInstanceInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/AddMqttInstanceInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}}}}}}, "/api/mqttInstance/update": {"post": {"tags": ["mqttInstance"], "summary": "更新MQTT 实例信息表 ✏️", "operationId": "api-mqttInstance-update-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateMqttInstanceInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateMqttInstanceInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateMqttInstanceInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateMqttInstanceInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/UpdateMqttInstanceInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttInstance/delete": {"post": {"tags": ["mqttInstance"], "summary": "删除MQTT 实例信息表 ❌", "operationId": "api-mqttInstance-delete-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DeleteMqttInstanceInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeleteMqttInstanceInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteMqttInstanceInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteMqttInstanceInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/DeleteMqttInstanceInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttInstance/batchDelete": {"post": {"tags": ["mqttInstance"], "summary": "批量删除MQTT 实例信息表 ❌", "operationId": "api-mqttInstance-batchDelete-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttInstanceInput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttInstanceInput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttInstanceInput"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttInstanceInput"}}}, "text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttInstanceInput"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Int32"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int32"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int32"}}}}}}}, "/api/mqttSubscription/page": {"post": {"tags": ["mqttSubscription"], "summary": "分页查询MQTT订阅关系", "operationId": "api-mqttSubscription-page-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/PageMqttSubscriptionInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PageMqttSubscriptionInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PageMqttSubscriptionInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PageMqttSubscriptionInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/PageMqttSubscriptionInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttSubscriptionOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttSubscriptionOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttSubscriptionOutput"}}}}}}}, "/api/mqttSubscription/add": {"post": {"tags": ["mqttSubscription"], "summary": "增加MQTT订阅关系", "operationId": "api-mqttSubscription-add-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AddMqttSubscriptionInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddMqttSubscriptionInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddMqttSubscriptionInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddMqttSubscriptionInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/AddMqttSubscriptionInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}}}}}}, "/api/mqttSubscription/delete": {"post": {"tags": ["mqttSubscription"], "summary": "删除MQTT订阅关系", "operationId": "api-mqttSubscription-delete-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DeleteMqttSubscriptionInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeleteMqttSubscriptionInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteMqttSubscriptionInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteMqttSubscriptionInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/DeleteMqttSubscriptionInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttSubscription/update": {"post": {"tags": ["mqttSubscription"], "summary": "更新MQTT订阅关系", "operationId": "api-mqttSubscription-update-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateMqttSubscriptionInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateMqttSubscriptionInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateMqttSubscriptionInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateMqttSubscriptionInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/UpdateMqttSubscriptionInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttSubscription/detail": {"get": {"tags": ["mqttSubscription"], "summary": "获取MQTT订阅关系详情", "operationId": "api-mqttSubscription-detail-Get", "parameters": [{"name": "Id", "in": "query", "description": "主键Id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttSubscriptionOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttSubscriptionOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttSubscriptionOutput"}}}}}}}, "/api/mqttSubscription/list": {"get": {"tags": ["mqttSubscription"], "summary": "获取MQTT订阅关系列表", "operationId": "api-mqttSubscription-list-Get", "parameters": [{"name": "InstanceId", "in": "query", "description": "实例ID", "schema": {"type": "integer", "format": "int64"}}, {"name": "ClientId", "in": "query", "description": "客户端ID", "schema": {"type": "string"}}, {"name": "TopicName", "in": "query", "description": "主题名称", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "description": "订阅状态", "schema": {"type": "string"}}, {"name": "IsEnabled", "in": "query", "description": "是否启用", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_List_MqttSubscriptionOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_List_MqttSubscriptionOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_List_MqttSubscriptionOutput"}}}}}}}, "/api/mqttSubscription/batchDelete": {"post": {"tags": ["mqttSubscription"], "summary": "批量删除MQTT订阅关系", "operationId": "api-mqttSubscription-batchDelete-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/BatchDeleteMqttSubscriptionInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BatchDeleteMqttSubscriptionInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BatchDeleteMqttSubscriptionInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BatchDeleteMqttSubscriptionInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/BatchDeleteMqttSubscriptionInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_BatchSubscriptionOperationOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_BatchSubscriptionOperationOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_BatchSubscriptionOperationOutput"}}}}}}}, "/api/mqttSubscription/subscribe": {"post": {"tags": ["mqttSubscription"], "summary": "订阅主题", "operationId": "api-mqttSubscription-subscribe-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SubscribeTopicInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SubscribeTopicInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubscribeTopicInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SubscribeTopicInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/SubscribeTopicInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_SubscriptionOperationOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SubscriptionOperationOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SubscriptionOperationOutput"}}}}}}}, "/api/mqttSubscription/unsubscribe": {"post": {"tags": ["mqttSubscription"], "summary": "取消订阅主题", "operationId": "api-mqttSubscription-unsubscribe-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UnsubscribeTopicInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UnsubscribeTopicInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UnsubscribeTopicInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UnsubscribeTopicInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/UnsubscribeTopicInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_SubscriptionOperationOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SubscriptionOperationOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SubscriptionOperationOutput"}}}}}}}, "/api/mqttSubscription/batchSubscribe": {"post": {"tags": ["mqttSubscription"], "summary": "批量订阅主题", "operationId": "api-mqttSubscription-batchSubscribe-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/BatchSubscribeInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BatchSubscribeInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BatchSubscribeInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BatchSubscribeInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/BatchSubscribeInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_BatchSubscriptionOperationOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_BatchSubscriptionOperationOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_BatchSubscriptionOperationOutput"}}}}}}}, "/api/mqttSubscription/batchUnsubscribe": {"post": {"tags": ["mqttSubscription"], "summary": "批量取消订阅主题", "operationId": "api-mqttSubscription-batchUnsubscribe-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/BatchUnsubscribeInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BatchUnsubscribeInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BatchUnsubscribeInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BatchUnsubscribeInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/BatchUnsubscribeInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_BatchSubscriptionOperationOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_BatchSubscriptionOperationOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_BatchSubscriptionOperationOutput"}}}}}}}, "/api/mqttSubscription/getClientSubscriptions": {"get": {"tags": ["mqttSubscription"], "summary": "获取客户端订阅列表", "operationId": "api-mqttSubscription-getClientSubscriptions-Get", "parameters": [{"name": "InstanceId", "in": "query", "description": "实例ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "ClientId", "in": "query", "description": "客户端ID", "required": true, "schema": {"maxLength": 128, "type": "string"}}, {"name": "IncludeStatistics", "in": "query", "description": "是否包含统计信息", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_ClientSubscriptionsOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_ClientSubscriptionsOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_ClientSubscriptionsOutput"}}}}}}}, "/api/mqttSubscription/getTopicSubscribers": {"get": {"tags": ["mqttSubscription"], "summary": "获取主题订阅者列表", "operationId": "api-mqttSubscription-getTopicSubscribers-Get", "parameters": [{"name": "InstanceId", "in": "query", "description": "实例ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "TopicName", "in": "query", "description": "主题名称", "required": true, "schema": {"maxLength": 255, "type": "string"}}, {"name": "IncludeStatistics", "in": "query", "description": "是否包含统计信息", "schema": {"type": "boolean"}}, {"name": "ActiveOnly", "in": "query", "description": "是否只显示活跃订阅", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_TopicSubscribersOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_TopicSubscribersOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_TopicSubscribersOutput"}}}}}}}, "/api/mqttSubscription/resetStatistics": {"post": {"tags": ["mqttSubscription"], "summary": "重置订阅统计信息", "operationId": "api-mqttSubscription-resetStatistics-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ResetSubscriptionStatisticsInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResetSubscriptionStatisticsInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetSubscriptionStatisticsInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetSubscriptionStatisticsInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/ResetSubscriptionStatisticsInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttSubscription/getStatistics": {"get": {"tags": ["mqttSubscription"], "summary": "获取订阅统计信息", "operationId": "api-mqttSubscription-getStatistics-Get", "parameters": [{"name": "InstanceId", "in": "query", "description": "实例ID", "schema": {"type": "integer", "format": "int64"}}, {"name": "ClientId", "in": "query", "description": "客户端ID", "schema": {"type": "string"}}, {"name": "TopicName", "in": "query", "description": "主题名称", "schema": {"type": "string"}}, {"name": "TimeRangeHours", "in": "query", "description": "统计时间范围（小时）", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_SubscriptionStatisticsOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SubscriptionStatisticsOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SubscriptionStatisticsOutput"}}}}}}}, "/api/mqttSubscription/export": {"post": {"tags": ["mqttSubscription"], "summary": "导出MQTT订阅关系", "operationId": "api-mqttSubscription-export-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ExportMqttSubscriptionInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ExportMqttSubscriptionInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExportMqttSubscriptionInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExportMqttSubscriptionInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/ExportMqttSubscriptionInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_FileResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_FileResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_FileResult"}}}}}}}, "/api/mqttTopic/page": {"post": {"tags": ["mqttTopic"], "summary": "分页查询MQTT 主题信息表 🔖", "operationId": "api-mqttTopic-page-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/PageMqttTopicInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PageMqttTopicInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PageMqttTopicInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PageMqttTopicInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/PageMqttTopicInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttTopicOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttTopicOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_MqttTopicOutput"}}}}}}}, "/api/mqttTopic/detail": {"get": {"tags": ["mqttTopic"], "summary": "获取MQTT 主题信息表详情 ℹ️", "operationId": "api-mqttTopic-detail-Get", "parameters": [{"name": "Id", "in": "query", "description": "主键Id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttTopic"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttTopic"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_MqttTopic"}}}}}}}, "/api/mqttTopic/add": {"post": {"tags": ["mqttTopic"], "summary": "增加MQTT 主题信息表 ➕", "operationId": "api-mqttTopic-add-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AddMqttTopicInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddMqttTopicInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddMqttTopicInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddMqttTopicInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/AddMqttTopicInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}}}}}}, "/api/mqttTopic/update": {"post": {"tags": ["mqttTopic"], "summary": "更新MQTT 主题信息表 ✏️", "operationId": "api-mqttTopic-update-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateMqttTopicInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateMqttTopicInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateMqttTopicInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateMqttTopicInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/UpdateMqttTopicInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttTopic/delete": {"post": {"tags": ["mqttTopic"], "summary": "删除MQTT 主题信息表 ❌", "operationId": "api-mqttTopic-delete-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DeleteMqttTopicInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeleteMqttTopicInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteMqttTopicInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteMqttTopicInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/DeleteMqttTopicInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/mqttTopic/batchDelete": {"post": {"tags": ["mqttTopic"], "summary": "批量删除MQTT 主题信息表 ❌", "operationId": "api-mqttTopic-batchDelete-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttTopicInput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttTopicInput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttTopicInput"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttTopicInput"}}}, "text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeleteMqttTopicInput"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Int32"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int32"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int32"}}}}}}}}, "components": {"schemas": {"AddMqttClientAuthInput": {"type": "object", "properties": {"instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "clientId": {"maxLength": 128, "type": "string", "description": "客户端ID", "nullable": true}, "clientRefId": {"type": "integer", "description": "客户端引用ID", "format": "int64", "nullable": true}, "authMode": {"maxLength": 32, "type": "string", "description": "认证模式（Username、AliyunSignature、JWT等）", "nullable": true}, "username": {"maxLength": 128, "type": "string", "description": "用户名", "nullable": true}, "password": {"maxLength": 128, "type": "string", "description": "密码", "nullable": true}, "passwordHash": {"maxLength": 256, "type": "string", "description": "密码哈希", "nullable": true}, "passwordSalt": {"maxLength": 64, "type": "string", "description": "密码盐值", "nullable": true}, "accessKeyId": {"maxLength": 64, "type": "string", "description": "AccessKey ID（阿里云认证）", "nullable": true}, "accessKeySecret": {"maxLength": 128, "type": "string", "description": "AccessKey Secret（加密存储）", "nullable": true}, "signMethod": {"maxLength": 32, "type": "string", "description": "签名算法（hmacmd5、hmacsha1、hmacsha256）", "nullable": true}, "jwtSecret": {"maxLength": 256, "type": "string", "description": "JWT密钥", "nullable": true}, "jwtExpiry": {"type": "integer", "description": "JWT过期时间（秒）", "format": "int32", "nullable": true}, "jwtToken": {"maxLength": 255, "type": "string", "description": "JWT令牌", "nullable": true}, "certificate": {"maxLength": 255, "type": "string", "description": "客户端证书", "nullable": true}, "privateKey": {"maxLength": 255, "type": "string", "description": "私钥", "nullable": true}, "caCertificate": {"maxLength": 255, "type": "string", "description": "CA证书", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "allowedIPs": {"maxLength": 255, "type": "string", "description": "允许的IP地址列表", "nullable": true}, "deniedIPs": {"maxLength": 255, "type": "string", "description": "拒绝的IP地址列表", "nullable": true}, "rateLimit": {"type": "integer", "description": "速率限制（消息/秒）", "format": "int32", "nullable": true}, "loginAttempts": {"type": "integer", "description": "登录尝试次数", "format": "int32", "nullable": true}, "expireTime": {"type": "string", "description": "过期时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastAuthTime": {"type": "string", "description": "最后认证时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastLoginTime": {"type": "string", "description": "最后登录时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastLoginIP": {"maxLength": 45, "type": "string", "description": "最后登录IP", "nullable": true}, "successCount": {"type": "integer", "description": "认证成功次数", "format": "int32", "nullable": true}, "authSuccessCount": {"type": "integer", "description": "认证成功总数", "format": "int32", "nullable": true}, "failedCount": {"type": "integer", "description": "认证失败次数", "format": "int32", "nullable": true}, "isLocked": {"type": "boolean", "description": "是否锁定（0:未锁定，1:已锁定）", "nullable": true}, "lockTime": {"type": "string", "description": "锁定时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "unlockTime": {"type": "string", "description": "解锁时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lockReason": {"maxLength": 256, "type": "string", "description": "锁定原因", "nullable": true}, "maxFailedCount": {"type": "integer", "description": "最大失败次数", "format": "int32", "nullable": true}, "lockDuration": {"type": "integer", "description": "锁定持续时间（分钟）", "format": "int32", "nullable": true}, "ipWhitelist": {"maxLength": 255, "type": "string", "description": "IP白名单", "nullable": true}, "ipBlacklist": {"maxLength": 255, "type": "string", "description": "IP黑名单", "nullable": true}, "allowedTopics": {"maxLength": 255, "type": "string", "description": "允许的主题列表", "nullable": true}, "deniedTopics": {"maxLength": 255, "type": "string", "description": "拒绝的主题列表", "nullable": true}, "maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32", "nullable": true}, "currentConnections": {"type": "integer", "description": "当前连接数", "format": "int32", "nullable": true}, "connectionTimeout": {"type": "integer", "description": "连接超时时间（秒）", "format": "int32", "nullable": true}, "maxMessageSize": {"type": "integer", "description": "最大消息大小", "format": "int32", "nullable": true}, "maxSubscriptions": {"type": "integer", "description": "最大订阅数", "format": "int32", "nullable": true}, "messageRateLimit": {"type": "integer", "description": "消息速率限制", "format": "int32", "nullable": true}, "byteRateLimit": {"type": "integer", "description": "字节速率限制", "format": "int32", "nullable": true}, "extendedProperties": {"maxLength": 255, "type": "string", "description": "扩展属性（JSON）", "nullable": true}, "key": {"maxLength": 255, "type": "string", "description": "", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "MQTT 客户端认证信息表增加输入参数"}, "AddMqttClientInput": {"type": "object", "properties": {"instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "clientId": {"maxLength": 128, "type": "string", "description": "客户端ID", "nullable": true}, "deviceName": {"maxLength": 64, "type": "string", "description": "设备名称", "nullable": true}, "groupId": {"maxLength": 64, "type": "string", "description": "设备组ID", "nullable": true}, "productKey": {"maxLength": 64, "type": "string", "description": "产品Key", "nullable": true}, "deviceSecret": {"maxLength": 128, "type": "string", "description": "设备密钥（加密存储）", "nullable": true}, "ipAddress": {"maxLength": 45, "type": "string", "description": "IP地址", "nullable": true}, "port": {"type": "integer", "description": "端口", "format": "int32", "nullable": true}, "status": {"maxLength": 32, "type": "string", "description": "连接状态", "nullable": true}, "protocolVersion": {"maxLength": 16, "type": "string", "description": "协议版本", "nullable": true}, "keepAlive": {"type": "integer", "description": "心跳间隔（秒）", "format": "int32", "nullable": true}, "cleanSession": {"type": "boolean", "description": "清除会话（0:否，1:是）", "nullable": true}, "connectedAt": {"type": "string", "description": "连接时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "disconnectedAt": {"type": "string", "description": "断开时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "messagesSent": {"type": "integer", "description": "发送消息数", "format": "int64", "nullable": true}, "messagesReceived": {"type": "integer", "description": "接收消息数", "format": "int64", "nullable": true}, "bytesSent": {"type": "integer", "description": "发送字节数", "format": "int64", "nullable": true}, "bytesReceived": {"type": "integer", "description": "接收字节数", "format": "int64", "nullable": true}, "subscriptionCount": {"type": "integer", "description": "订阅数量", "format": "int32", "nullable": true}, "maxSubscriptions": {"type": "integer", "description": "最大订阅数", "format": "int32", "nullable": true}, "deviceType": {"maxLength": 32, "type": "string", "description": "设备类型", "nullable": true}, "deviceVersion": {"maxLength": 32, "type": "string", "description": "设备版本", "nullable": true}, "deviceTags": {"maxLength": 255, "type": "string", "description": "设备标签（JSON）", "nullable": true}, "locationInfo": {"maxLength": 255, "type": "string", "description": "位置信息（JSON）", "nullable": true}, "isOnline": {"type": "boolean", "description": "是否在线（0:离线，1:在线）", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "authMode": {"maxLength": 32, "type": "string", "description": "认证模式", "nullable": true}, "clientType": {"maxLength": 32, "type": "string", "description": "客户端类型", "nullable": true}, "lastError": {"maxLength": 500, "type": "string", "description": "最后错误信息", "nullable": true}, "extendedProperties": {"maxLength": 255, "type": "string", "description": "扩展属性（JSON）", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "MQTT 客户端连接信息表增加输入参数"}, "AddMqttDeviceGroupInput": {"required": ["accessKeyId", "accessKeySecret", "authMode", "byteRateLimit", "certificateRequired", "connectionTimeout", "createdDeviceCount", "currentDeviceCount", "defaultQosLevel", "enableRetainMessage", "enableSharedSubscription", "enableWildcardSubscription", "encryptionRequired", "groupId", "groupName", "groupType", "instanceId", "isEnabled", "keepAliveTimeout", "maxConnections", "maxDevice<PERSON><PERSON>nt", "maxMessageSize", "messageRateLimit", "onlineDeviceCount", "productKey", "securityLevel", "sign<PERSON>ethod", "totalByteCount", "totalMessageCount"], "type": "object", "properties": {"instanceId": {"type": "integer", "description": "实例ID", "format": "int64"}, "groupId": {"maxLength": 64, "minLength": 1, "type": "string", "description": "设备组标识（如：GID_TestGroup）"}, "groupName": {"maxLength": 128, "minLength": 1, "type": "string", "description": "设备组名称"}, "groupType": {"maxLength": 32, "minLength": 1, "type": "string", "description": "设备组类型（Production、Test、Development等）"}, "productKey": {"maxLength": 64, "minLength": 1, "type": "string", "description": "产品Key（阿里云IoT产品标识）"}, "description": {"maxLength": 500, "type": "string", "description": "设备组描述", "nullable": true}, "maxDeviceCount": {"type": "integer", "description": "最大设备数量", "format": "int32"}, "currentDeviceCount": {"type": "integer", "description": "当前设备数量", "format": "int32"}, "allowedTopicPatterns": {"maxLength": 255, "type": "string", "description": "允许的主题模式（JSON数组）", "nullable": true}, "deniedTopicPatterns": {"maxLength": 255, "type": "string", "description": "禁止的主题模式（JSON数组）", "nullable": true}, "defaultQosLevel": {"type": "boolean", "description": "默认QoS等级（0,1,2）"}, "maxMessageSize": {"type": "integer", "description": "最大消息大小（字节）", "format": "int32"}, "messageRateLimit": {"type": "integer", "description": "消息速率限制（消息/秒）", "format": "int32"}, "byteRateLimit": {"type": "integer", "description": "字节速率限制（字节/秒）", "format": "int32"}, "maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32"}, "connectionTimeout": {"type": "integer", "description": "连接超时时间（秒）", "format": "int32"}, "keepAliveTimeout": {"type": "integer", "description": "心跳超时时间（秒）", "format": "int32"}, "enableRetainMessage": {"type": "boolean", "description": "是否允许保留消息"}, "enableWildcardSubscription": {"type": "boolean", "description": "是否允许通配符订阅"}, "enableSharedSubscription": {"type": "boolean", "description": "是否允许共享订阅"}, "ipWhitelist": {"maxLength": 255, "type": "string", "description": "IP白名单（JSON数组）", "nullable": true}, "ipBlacklist": {"maxLength": 255, "type": "string", "description": "IP黑名单（JSON数组）", "nullable": true}, "allowedTimeRanges": {"maxLength": 255, "type": "string", "description": "允许连接的时间范围（JSON）", "nullable": true}, "securityLevel": {"maxLength": 32, "minLength": 1, "type": "string", "description": "安全级别（Low、Medium、High）"}, "encryptionRequired": {"type": "boolean", "description": "是否要求加密连接"}, "certificateRequired": {"type": "boolean", "description": "是否要求客户端证书"}, "authMode": {"maxLength": 32, "minLength": 1, "type": "string", "description": "认证模式（Signature、Username、JWT、Certificate）"}, "signMethod": {"maxLength": 32, "minLength": 1, "type": "string", "description": "签名算法（hmacsha1、hmacsha256、hmacmd5）"}, "accessKeyId": {"maxLength": 64, "minLength": 1, "type": "string", "description": "AccessKey ID（阿里云认证）"}, "accessKeySecret": {"maxLength": 128, "minLength": 1, "type": "string", "description": "AccessKey Secret（加密存储）"}, "jwtSecret": {"maxLength": 256, "type": "string", "description": "JWT密钥", "nullable": true}, "jwtExpiry": {"type": "integer", "description": "JWT过期时间（秒）", "format": "int32", "nullable": true}, "caCertificate": {"maxLength": 255, "type": "string", "description": "CA证书", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用"}, "expireTime": {"type": "string", "description": "过期时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createdDeviceCount": {"type": "integer", "description": "已创建设备数", "format": "int32"}, "onlineDeviceCount": {"type": "integer", "description": "在线设备数", "format": "int32"}, "totalMessageCount": {"type": "integer", "description": "总消息数", "format": "int64"}, "totalByteCount": {"type": "integer", "description": "总字节数", "format": "int64"}, "lastMessageTime": {"type": "string", "description": "最后消息时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "alertRules": {"maxLength": 255, "type": "string", "description": "告警规则（JSON）", "nullable": true}, "monitorConfig": {"maxLength": 255, "type": "string", "description": "监控配置（JSON）", "nullable": true}, "extendedProperties": {"maxLength": 255, "type": "string", "description": "扩展属性（JSON）", "nullable": true}, "tags": {"maxLength": 255, "type": "string", "description": "标签（JSON）", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "MQTT设备组管理表增加输入参数"}, "AddMqttInstanceInput": {"type": "object", "properties": {"instanceName": {"maxLength": 128, "type": "string", "description": "实例名称", "nullable": true}, "instanceCode": {"maxLength": 64, "type": "string", "description": "实例编码", "nullable": true}, "instanceType": {"maxLength": 32, "type": "string", "description": "实例类型（EMQX、阿里云等）", "nullable": true}, "serverHost": {"maxLength": 128, "type": "string", "description": "服务器地址", "nullable": true}, "serverPort": {"type": "integer", "description": "服务器端口", "format": "int32", "nullable": true}, "apiHost": {"maxLength": 128, "type": "string", "description": "API地址", "nullable": true}, "apiPort": {"type": "integer", "description": "API端口", "format": "int32", "nullable": true}, "apiUsername": {"maxLength": 64, "type": "string", "description": "API用户名", "nullable": true}, "apiPassword": {"maxLength": 128, "type": "string", "description": "API密码（加密存储）", "nullable": true}, "status": {"maxLength": 32, "type": "string", "description": "实例状态", "nullable": true}, "enableSsl": {"type": "boolean", "description": "是否启用SSL（0:否，1:是）", "nullable": true}, "sslPort": {"type": "integer", "description": "SSL端口", "format": "int32", "nullable": true}, "enableWebSocket": {"type": "boolean", "description": "是否启用WebSocket（0:否，1:是）", "nullable": true}, "wsPort": {"type": "integer", "description": "WebSocket端口", "format": "int32", "nullable": true}, "wssPort": {"type": "integer", "description": "WebSocket SSL端口", "format": "int32", "nullable": true}, "maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32", "nullable": true}, "currentConnections": {"type": "integer", "description": "当前连接数", "format": "int32", "nullable": true}, "aliyunProductKey": {"maxLength": 64, "type": "string", "description": "阿里云产品Key", "nullable": true}, "aliyunRegionId": {"maxLength": 32, "type": "string", "description": "阿里云区域ID", "nullable": true}, "aliyunInstanceId": {"maxLength": 64, "type": "string", "description": "阿里云实例ID", "nullable": true}, "deviceIdPrefix": {"maxLength": 32, "type": "string", "description": "设备ID前缀", "nullable": true}, "groupIdPrefix": {"maxLength": 32, "type": "string", "description": "组ID前缀", "nullable": true}, "aliyunCompatible": {"type": "boolean", "description": "是否兼容阿里云（0:否，1:是）", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "configJson": {"maxLength": 255, "type": "string", "description": "配置JSON", "nullable": true}, "lastHeartbeat": {"type": "string", "description": "最后心跳时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "totalMessages": {"type": "integer", "description": "总消息数", "format": "int64", "nullable": true}, "totalBytes": {"type": "integer", "description": "总字节数", "format": "int64", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "description": "备注", "nullable": true}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "MQTT 实例信息表增加输入参数"}, "AddMqttSubscriptionInput": {"type": "object", "properties": {"instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "clientId": {"maxLength": 128, "type": "string", "description": "客户端ID", "nullable": true}, "clientRefId": {"type": "integer", "description": "客户端引用ID", "format": "int64", "nullable": true}, "topicName": {"maxLength": 255, "type": "string", "description": "主题名称", "nullable": true}, "topicId": {"type": "integer", "description": "主题ID", "format": "int64", "nullable": true}, "qosLevel": {"maximum": 2, "minimum": 0, "type": "integer", "description": "QoS等级（0、1、2）", "format": "int32", "nullable": true}, "status": {"maxLength": 32, "type": "string", "description": "订阅状态（Active、Inactive、Suspended）", "nullable": true}, "subscribedAt": {"type": "string", "description": "订阅时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "unsubscribedAt": {"type": "string", "description": "取消订阅时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "messagesReceived": {"type": "integer", "description": "接收消息数", "format": "int64", "nullable": true}, "bytesReceived": {"type": "integer", "description": "接收字节数", "format": "int64", "nullable": true}, "messagesDropped": {"type": "integer", "description": "丢弃消息数", "format": "int64", "nullable": true}, "lastMessageTime": {"type": "string", "description": "最后消息时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastMessageId": {"maxLength": 64, "type": "string", "description": "最后消息ID", "nullable": true}, "isSharedSubscription": {"type": "boolean", "description": "是否共享订阅", "nullable": true}, "sharedGroupName": {"maxLength": 64, "type": "string", "description": "共享组名", "nullable": true}, "subscriptionOptions": {"type": "string", "description": "订阅选项（JSON）", "nullable": true}, "configJson": {"type": "string", "description": "配置JSON", "nullable": true}, "messageFilter": {"maxLength": 255, "type": "string", "description": "消息过滤器", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用", "nullable": true}, "priority": {"maximum": 10, "minimum": 0, "type": "integer", "description": "优先级", "format": "int32", "nullable": true}, "maxQueueLength": {"type": "integer", "description": "最大队列长度", "format": "int32", "nullable": true}, "currentQueueLength": {"type": "integer", "description": "当前队列长度", "format": "int32", "nullable": true}, "handlingStrategy": {"maxLength": 32, "type": "string", "description": "处理策略（FIFO、LIFO、Priority）", "nullable": true}, "retryCount": {"type": "integer", "description": "重试次数", "format": "int32", "nullable": true}, "maxRetryCount": {"type": "integer", "description": "最大重试次数", "format": "int32", "nullable": true}, "lastError": {"maxLength": 500, "type": "string", "description": "最后错误信息", "nullable": true}, "statistics": {"type": "string", "description": "统计信息（JSON）", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON）", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "增加MQTT订阅关系表参数"}, "AddMqttTopicInput": {"type": "object", "properties": {"instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "topicName": {"maxLength": 256, "type": "string", "description": "主题名称", "nullable": true}, "topicType": {"maxLength": 32, "type": "string", "description": "主题类型", "nullable": true}, "qosLevel": {"type": "boolean", "description": "QoS等级（0,1,2）", "nullable": true}, "retainMessage": {"type": "boolean", "description": "是否保留消息（0:否，1:是）", "nullable": true}, "subscriptionCount": {"type": "integer", "description": "订阅数量", "format": "int32", "nullable": true}, "messageCount": {"type": "integer", "description": "消息数量", "format": "int64", "nullable": true}, "byteCount": {"type": "integer", "description": "字节数量", "format": "int64", "nullable": true}, "lastMessageTime": {"type": "string", "description": "最后消息时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastMessageContent": {"maxLength": 255, "type": "string", "description": "最后消息内容", "nullable": true}, "maxMessageSize": {"type": "integer", "description": "最大消息大小", "format": "int32", "nullable": true}, "messageExpiry": {"type": "integer", "description": "消息过期时间（秒）", "format": "int32", "nullable": true}, "allowPublish": {"type": "boolean", "description": "允许发布（0:否，1:是）", "nullable": true}, "allowSubscribe": {"type": "boolean", "description": "允许订阅（0:否，1:是）", "nullable": true}, "publishPermissions": {"maxLength": 255, "type": "string", "description": "发布权限（JSON）", "nullable": true}, "subscribePermissions": {"maxLength": 255, "type": "string", "description": "订阅权限（JSON）", "nullable": true}, "isRetained": {"type": "boolean", "description": "是否保留（0:否，1:是）", "nullable": true}, "description": {"maxLength": 500, "type": "string", "description": "描述", "nullable": true}, "topicTags": {"maxLength": 255, "type": "string", "description": "主题标签（JSON）", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "isSystemTopic": {"type": "boolean", "description": "是否系统主题（0:否，1:是）", "nullable": true}, "monitorConfig": {"maxLength": 255, "type": "string", "description": "监控配置（JSON）", "nullable": true}, "alertRules": {"maxLength": 255, "type": "string", "description": "告警规则（JSON）", "nullable": true}, "forwardRules": {"maxLength": 255, "type": "string", "description": "转发规则（JSON）", "nullable": true}, "extendedProperties": {"maxLength": 255, "type": "string", "description": "扩展属性（JSON）", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "MQTT 主题信息表增加输入参数"}, "AdminResult_AutoGroupDeviceOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/AutoGroupDeviceOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_BatchRecordMqttMessageOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/BatchRecordMqttMessageOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_BatchSubscriptionOperationOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/BatchSubscriptionOperationOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_Boolean": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"type": "boolean", "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_CleanHistoryOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/CleanHistoryOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_ClientIdSuggestionsOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/ClientIdSuggestionsOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_ClientSubscriptionsOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/ClientSubscriptionsOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_EmqxActiveConnectionsOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/EmqxActiveConnectionsOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_EmqxBatchOperationOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/EmqxBatchOperationOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_EmqxConnectionStatusOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/EmqxConnectionStatusOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_FileResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"type": "string", "description": "数据", "format": "binary", "nullable": true}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_GenerateClientIdOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/GenerateClientIdOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_InstanceDeviceOverviewOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/InstanceDeviceOverviewOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_Int32": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"type": "integer", "description": "数据", "format": "int32"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_Int64": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"type": "integer", "description": "数据", "format": "int64"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_List_DeviceGroupRealtimeStats": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceGroupRealtimeStats"}, "description": "数据", "nullable": true}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_List_MqttSubscriptionOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/MqttSubscriptionOutput"}, "description": "数据", "nullable": true}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_MqttClient": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/MqttClient"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_MqttClientAuth": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/MqttClientAuth"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_MqttDeviceGroup": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/MqttDeviceGroup"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_MqttInstance": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/MqttInstance"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_MqttMessageHistory": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/MqttMessageHistory"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_MqttMessageStatisticsOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/MqttMessageStatisticsOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_MqttSubscriptionOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/MqttSubscriptionOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_MqttTopic": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/MqttTopic"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_SqlSugarPagedList_MqttClientAuthOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/SqlSugarPagedList_MqttClientAuthOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_SqlSugarPagedList_MqttClientOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/SqlSugarPagedList_MqttClientOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_SqlSugarPagedList_MqttDeviceGroupOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/SqlSugarPagedList_MqttDeviceGroupOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_SqlSugarPagedList_MqttInstanceOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/SqlSugarPagedList_MqttInstanceOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_SqlSugarPagedList_MqttMessageHistoryOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/SqlSugarPagedList_MqttMessageHistoryOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_SqlSugarPagedList_MqttSubscriptionOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/SqlSugarPagedList_MqttSubscriptionOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_SqlSugarPagedList_MqttTopicOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/SqlSugarPagedList_MqttTopicOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_SubscriptionOperationOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/SubscriptionOperationOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_SubscriptionStatisticsOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/SubscriptionStatisticsOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_TopicSubscribersOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/TopicSubscribersOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_ValidateClientIdOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/ValidateClientIdOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AutoGroupDeviceInput": {"required": ["clientId", "instanceId"], "type": "object", "properties": {"clientId": {"minLength": 1, "type": "string", "description": "客户端ID"}, "instanceId": {"type": "integer", "description": "MQTT实例ID", "format": "int64"}}, "additionalProperties": false, "description": "自动分组设备输入"}, "AutoGroupDeviceOutput": {"type": "object", "properties": {"success": {"type": "boolean", "description": "是否成功"}, "deviceGroup": {"$ref": "#/components/schemas/MqttDeviceGroupInfo"}, "message": {"type": "string", "description": "消息", "nullable": true}, "groupTime": {"type": "string", "description": "分组时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "自动分组设备输出"}, "BatchDeleteMqttMessageHistoryInput": {"required": ["ids"], "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "ID列表"}}, "additionalProperties": false, "description": "批量删除MQTT消息历史记录输入"}, "BatchDeleteMqttSubscriptionInput": {"required": ["ids"], "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "主键Id集合"}}, "additionalProperties": false, "description": "批量删除MQTT订阅关系输入参数"}, "BatchRecordMqttMessageInput": {"required": ["messages"], "type": "object", "properties": {"messages": {"type": "array", "items": {"$ref": "#/components/schemas/RecordMqttMessageInput"}, "description": "消息列表"}}, "additionalProperties": false, "description": "批量记录MQTT消息输入"}, "BatchRecordMqttMessageOutput": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/components/schemas/MqttMessageRecordResult"}, "description": "记录结果列表", "nullable": true}, "totalCount": {"type": "integer", "description": "总数量", "format": "int32"}, "successCount": {"type": "integer", "description": "成功数量", "format": "int32"}, "failureCount": {"type": "integer", "description": "失败数量", "format": "int32"}, "recordTime": {"type": "string", "description": "记录时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "批量记录MQTT消息输出"}, "BatchSubscribeInput": {"required": ["clientId", "instanceId", "topics"], "type": "object", "properties": {"instanceId": {"type": "integer", "description": "实例ID", "format": "int64"}, "clientId": {"maxLength": 128, "minLength": 1, "type": "string", "description": "客户端ID"}, "topics": {"type": "array", "items": {"$ref": "#/components/schemas/BatchSubscribeTopicItem"}, "description": "订阅主题列表"}}, "additionalProperties": false, "description": "批量订阅主题参数"}, "BatchSubscribeTopicItem": {"required": ["topicName"], "type": "object", "properties": {"topicName": {"maxLength": 255, "minLength": 1, "type": "string", "description": "主题名称"}, "qosLevel": {"maximum": 2, "minimum": 0, "type": "integer", "description": "QoS等级（0、1、2）", "format": "int32"}, "messageFilter": {"maxLength": 255, "type": "string", "description": "消息过滤器", "nullable": true}, "priority": {"maximum": 10, "minimum": 0, "type": "integer", "description": "优先级", "format": "int32"}}, "additionalProperties": false, "description": "批量订阅主题项"}, "BatchSubscriptionOperationOutput": {"type": "object", "properties": {"totalCount": {"type": "integer", "description": "总数", "format": "int32"}, "successCount": {"type": "integer", "description": "成功数", "format": "int32"}, "failedCount": {"type": "integer", "description": "失败数", "format": "int32"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionOperationOutput"}, "description": "操作结果详情", "nullable": true}, "operationTime": {"type": "string", "description": "操作时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "批量订阅操作结果输出"}, "BatchUnsubscribeInput": {"required": ["clientId", "instanceId", "topicNames"], "type": "object", "properties": {"instanceId": {"type": "integer", "description": "实例ID", "format": "int64"}, "clientId": {"maxLength": 128, "minLength": 1, "type": "string", "description": "客户端ID"}, "topicNames": {"type": "array", "items": {"type": "string"}, "description": "主题名称列表"}, "unsubscribeReason": {"maxLength": 255, "type": "string", "description": "取消订阅原因", "nullable": true}}, "additionalProperties": false, "description": "批量取消订阅参数"}, "CleanHistoryInput": {"type": "object", "properties": {"instanceId": {"type": "integer", "description": "MQTT实例ID（可选，为0表示所有实例）", "format": "int64"}, "beforeDate": {"type": "string", "description": "删除此日期之前的消息", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "daysToKeep": {"type": "integer", "description": "保留天数（与BeforeDate二选一）", "format": "int32"}, "processStatus": {"type": "string", "description": "处理状态过滤", "nullable": true}}, "additionalProperties": false, "description": "清理历史消息输入"}, "CleanHistoryOutput": {"type": "object", "properties": {"deletedCount": {"type": "integer", "description": "删除数量", "format": "int32"}, "cleanTime": {"type": "string", "description": "清理时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "清理历史消息输出"}, "ClientIdSuggestionsOutput": {"type": "object", "properties": {"deviceType": {"type": "string", "description": "设备类型", "nullable": true}, "location": {"type": "string", "description": "设备位置", "nullable": true}, "suggestions": {"type": "array", "items": {"type": "string"}, "description": "建议的ClientId列表", "nullable": true}, "generatedAt": {"type": "string", "description": "生成时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "ClientId建议输出"}, "ClientSubscriptionCount": {"type": "object", "properties": {"clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "subscriptionCount": {"type": "integer", "description": "订阅数", "format": "int32"}, "activeCount": {"type": "integer", "description": "活跃订阅数", "format": "int32"}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "客户端订阅数统计"}, "ClientSubscriptionStatistics": {"type": "object", "properties": {"totalMessagesReceived": {"type": "integer", "description": "总接收消息数", "format": "int64"}, "totalBytesReceived": {"type": "integer", "description": "总接收字节数", "format": "int64"}, "totalMessagesDropped": {"type": "integer", "description": "总丢弃消息数", "format": "int64"}, "averageQosLevel": {"type": "number", "description": "平均QoS等级", "format": "double"}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "qosDistribution": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "description": "QoS分布", "nullable": true}}, "additionalProperties": false, "description": "客户端订阅统计信息"}, "ClientSubscriptionsOutput": {"type": "object", "properties": {"clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64"}, "totalSubscriptions": {"type": "integer", "description": "总订阅数", "format": "int32"}, "activeSubscriptions": {"type": "integer", "description": "活跃订阅数", "format": "int32"}, "suspendedSubscriptions": {"type": "integer", "description": "暂停订阅数", "format": "int32"}, "subscriptions": {"type": "array", "items": {"$ref": "#/components/schemas/MqttSubscriptionOutput"}, "description": "订阅列表", "nullable": true}, "statistics": {"$ref": "#/components/schemas/ClientSubscriptionStatistics"}}, "additionalProperties": false, "description": "客户端订阅列表输出"}, "DeleteMqttClientAuthInput": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "description": "MQTT 客户端认证信息表删除输入参数"}, "DeleteMqttClientInput": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "description": "MQTT 客户端连接信息表删除输入参数"}, "DeleteMqttDeviceGroupInput": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "description": "MQTT设备组管理表删除输入参数"}, "DeleteMqttInstanceInput": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "description": "MQTT 实例信息表删除输入参数"}, "DeleteMqttMessageHistoryInput": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "description": "删除MQTT消息历史记录输入"}, "DeleteMqttSubscriptionInput": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "description": "删除MQTT订阅关系表参数"}, "DeleteMqttTopicInput": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "description": "MQTT 主题信息表删除输入参数"}, "DeviceGroupRealtimeStats": {"type": "object", "properties": {"groupId": {"type": "integer", "description": "设备组ID", "format": "int64"}, "groupName": {"type": "string", "description": "设备组名称", "nullable": true}, "deviceType": {"type": "string", "description": "设备类型", "nullable": true}, "location": {"type": "string", "description": "设备位置", "nullable": true}, "onlineDeviceCount": {"type": "integer", "description": "在线设备数量", "format": "int32"}, "totalDeviceCount": {"type": "integer", "description": "设备总数量", "format": "int32"}, "recentMessageCount": {"type": "integer", "description": "最近消息数量", "format": "int32"}, "lastUpdateTime": {"type": "string", "description": "最后更新时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "设备组实时统计"}, "DeviceTypeStats": {"type": "object", "properties": {"deviceType": {"type": "string", "description": "设备类型", "nullable": true}, "onlineCount": {"type": "integer", "description": "在线数量", "format": "int32"}, "totalCount": {"type": "integer", "description": "总数量", "format": "int32"}}, "additionalProperties": false, "description": "设备类型统计"}, "EmqxActiveConnectionsOutput": {"type": "object", "properties": {"activeInstanceIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "活跃的实例ID列表", "nullable": true}, "totalCount": {"type": "integer", "description": "总数量", "format": "int32"}, "checkTime": {"type": "string", "description": "检查时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "EMQX活跃连接输出"}, "EmqxBatchConnectInput": {"required": ["instanceIds"], "type": "object", "properties": {"instanceIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "MQTT实例ID列表"}}, "additionalProperties": false, "description": "EMQX批量连接输入"}, "EmqxBatchDisconnectInput": {"required": ["instanceIds"], "type": "object", "properties": {"instanceIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "MQTT实例ID列表"}}, "additionalProperties": false, "description": "EMQX批量断开连接输入"}, "EmqxBatchOperationOutput": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/components/schemas/EmqxOperationResult"}, "description": "操作结果列表", "nullable": true}, "totalCount": {"type": "integer", "description": "总数量", "format": "int32"}, "successCount": {"type": "integer", "description": "成功数量", "format": "int32"}, "failureCount": {"type": "integer", "description": "失败数量", "format": "int32"}, "operationTime": {"type": "string", "description": "操作时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "EMQX批量操作输出"}, "EmqxConnectInput": {"required": ["instanceId"], "type": "object", "properties": {"instanceId": {"type": "integer", "description": "MQTT实例ID", "format": "int64"}}, "additionalProperties": false, "description": "EMQX连接输入"}, "EmqxConnectionStatusOutput": {"type": "object", "properties": {"instanceId": {"type": "integer", "description": "MQTT实例ID", "format": "int64"}, "isConnected": {"type": "boolean", "description": "是否已连接"}, "status": {"type": "string", "description": "连接状态", "nullable": true}, "checkTime": {"type": "string", "description": "检查时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "EMQX连接状态输出"}, "EmqxDisconnectInput": {"required": ["instanceId"], "type": "object", "properties": {"instanceId": {"type": "integer", "description": "MQTT实例ID", "format": "int64"}}, "additionalProperties": false, "description": "EMQX断开连接输入"}, "EmqxOperationResult": {"type": "object", "properties": {"instanceId": {"type": "integer", "description": "MQTT实例ID", "format": "int64"}, "success": {"type": "boolean", "description": "是否成功"}, "message": {"type": "string", "description": "消息", "nullable": true}}, "additionalProperties": false, "description": "EMQX操作结果"}, "EmqxPublishInput": {"required": ["instanceId", "payload", "topic"], "type": "object", "properties": {"instanceId": {"type": "integer", "description": "MQTT实例ID", "format": "int64"}, "topic": {"minLength": 1, "type": "string", "description": "主题"}, "payload": {"minLength": 1, "type": "string", "description": "消息内容"}, "qos": {"maximum": 2, "minimum": 0, "type": "integer", "description": "QoS等级", "format": "int32"}, "retain": {"type": "boolean", "description": "是否保留消息"}}, "additionalProperties": false, "description": "EMQX发布消息输入"}, "EmqxSubscribeInput": {"required": ["instanceId", "topic"], "type": "object", "properties": {"instanceId": {"type": "integer", "description": "MQTT实例ID", "format": "int64"}, "topic": {"minLength": 1, "type": "string", "description": "主题"}, "qos": {"maximum": 2, "minimum": 0, "type": "integer", "description": "QoS等级", "format": "int32"}}, "additionalProperties": false, "description": "EMQX订阅主题输入"}, "EmqxUnsubscribeInput": {"required": ["instanceId", "topic"], "type": "object", "properties": {"instanceId": {"type": "integer", "description": "MQTT实例ID", "format": "int64"}, "topic": {"minLength": 1, "type": "string", "description": "主题"}}, "additionalProperties": false, "description": "EMQX取消订阅输入"}, "ExportMqttSubscriptionInput": {"type": "object", "properties": {"instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "topicName": {"type": "string", "description": "主题名称", "nullable": true}, "status": {"type": "string", "description": "订阅状态", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用", "nullable": true}, "isSharedSubscription": {"type": "boolean", "description": "是否共享订阅", "nullable": true}}, "additionalProperties": false, "description": "导出MQTT订阅关系参数"}, "Filter": {"type": "object", "properties": {"logic": {"$ref": "#/components/schemas/FilterLogicEnum"}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}, "description": "筛选过滤条件子项", "nullable": true}, "field": {"type": "string", "description": "字段名称", "nullable": true}, "operator": {"$ref": "#/components/schemas/FilterOperatorEnum"}, "value": {"additionalProperties": false, "description": "字段值", "nullable": true}}, "additionalProperties": false, "description": "筛选过滤条件"}, "FilterLogicEnum": {"enum": [0, 1, 2], "type": "integer", "description": "过滤条件<br />&nbsp;并且 And = 0<br />&nbsp;或者 Or = 1<br />&nbsp;异或 Xor = 2<br />", "format": "int32"}, "FilterOperatorEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8], "type": "integer", "description": "过滤逻辑运算符<br />&nbsp;等于 EQ = 0<br />&nbsp;不等于 NEQ = 1<br />&nbsp;小于 LT = 2<br />&nbsp;小于等于 LTE = 3<br />&nbsp;大于 GT = 4<br />&nbsp;大于等于 GTE = 5<br />&nbsp;开始包含 StartsWith = 6<br />&nbsp;末尾包含 EndsWith = 7<br />&nbsp;包含 Contains = 8<br />", "format": "int32"}, "GenerateClientIdInput": {"required": ["deviceType", "instancePrefix", "location", "uniqueId"], "type": "object", "properties": {"instancePrefix": {"minLength": 1, "type": "string", "description": "实例前缀"}, "deviceType": {"minLength": 1, "type": "string", "description": "设备类型"}, "location": {"minLength": 1, "type": "string", "description": "位置"}, "uniqueId": {"minLength": 1, "type": "string", "description": "唯一标识"}}, "additionalProperties": false, "description": "生成ClientId输入"}, "GenerateClientIdOutput": {"type": "object", "properties": {"clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "isValid": {"type": "boolean", "description": "是否有效"}, "generateTime": {"type": "string", "description": "生成时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "生成ClientId输出"}, "InstanceDeviceOverviewOutput": {"type": "object", "properties": {"instanceId": {"type": "integer", "description": "MQTT实例ID", "format": "int64"}, "totalDeviceCount": {"type": "integer", "description": "设备总数", "format": "int32"}, "onlineDeviceCount": {"type": "integer", "description": "在线设备数", "format": "int32"}, "deviceTypeStats": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceTypeStats"}, "description": "设备类型统计", "nullable": true}, "locationStats": {"type": "array", "items": {"$ref": "#/components/schemas/LocationStats"}, "description": "位置统计", "nullable": true}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "实例设备分布概览输出"}, "LocationStats": {"type": "object", "properties": {"location": {"type": "string", "description": "位置", "nullable": true}, "onlineCount": {"type": "integer", "description": "在线数量", "format": "int32"}, "totalCount": {"type": "integer", "description": "总数量", "format": "int32"}}, "additionalProperties": false, "description": "位置统计"}, "MqttClient": {"type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "example": "2025-08-05 14:17:44"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "createUserName": {"type": "string", "description": "创建者姓名", "nullable": true}, "updateUserId": {"type": "integer", "description": "修改者Id", "format": "int64", "nullable": true}, "updateUserName": {"type": "string", "description": "修改者姓名", "nullable": true}, "isDelete": {"type": "boolean", "description": "软删除"}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "deviceName": {"type": "string", "description": "设备名称", "nullable": true}, "groupId": {"type": "string", "description": "设备组ID", "nullable": true}, "productKey": {"type": "string", "description": "产品Key", "nullable": true}, "deviceSecret": {"type": "string", "description": "设备密钥（加密存储）", "nullable": true}, "ipAddress": {"type": "string", "description": "IP地址", "nullable": true}, "port": {"type": "integer", "description": "端口", "format": "int32", "nullable": true}, "status": {"type": "string", "description": "连接状态", "nullable": true}, "protocolVersion": {"type": "string", "description": "协议版本", "nullable": true}, "keepAlive": {"type": "integer", "description": "心跳间隔（秒）", "format": "int32", "nullable": true}, "cleanSession": {"type": "boolean", "description": "清除会话（0:否，1:是）", "nullable": true}, "connectedAt": {"type": "string", "description": "连接时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "disconnectedAt": {"type": "string", "description": "断开时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "messagesSent": {"type": "integer", "description": "发送消息数", "format": "int64", "nullable": true}, "messagesReceived": {"type": "integer", "description": "接收消息数", "format": "int64", "nullable": true}, "bytesSent": {"type": "integer", "description": "发送字节数", "format": "int64", "nullable": true}, "bytesReceived": {"type": "integer", "description": "接收字节数", "format": "int64", "nullable": true}, "subscriptionCount": {"type": "integer", "description": "订阅数量", "format": "int32", "nullable": true}, "maxSubscriptions": {"type": "integer", "description": "最大订阅数", "format": "int32", "nullable": true}, "deviceType": {"type": "string", "description": "设备类型", "nullable": true}, "deviceVersion": {"type": "string", "description": "设备版本", "nullable": true}, "deviceTags": {"type": "string", "description": "设备标签（JSON）", "nullable": true}, "locationInfo": {"type": "string", "description": "位置信息（JSON）", "nullable": true}, "isOnline": {"type": "boolean", "description": "是否在线（0:离线，1:在线）", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "authMode": {"type": "string", "description": "认证模式", "nullable": true}, "clientType": {"type": "string", "description": "客户端类型", "nullable": true}, "lastError": {"type": "string", "description": "最后错误信息", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON）", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "MQTT 客户端连接信息表"}, "MqttClientAuth": {"type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "example": "2025-08-05 14:17:44"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "createUserName": {"type": "string", "description": "创建者姓名", "nullable": true}, "updateUserId": {"type": "integer", "description": "修改者Id", "format": "int64", "nullable": true}, "updateUserName": {"type": "string", "description": "修改者姓名", "nullable": true}, "isDelete": {"type": "boolean", "description": "软删除"}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "clientRefId": {"type": "integer", "description": "客户端引用ID", "format": "int64", "nullable": true}, "authMode": {"type": "string", "description": "认证模式（Username、AliyunSignature、JWT等）", "nullable": true}, "username": {"type": "string", "description": "用户名", "nullable": true}, "password": {"type": "string", "description": "密码", "nullable": true}, "passwordHash": {"type": "string", "description": "密码哈希", "nullable": true}, "passwordSalt": {"type": "string", "description": "密码盐值", "nullable": true}, "accessKeyId": {"type": "string", "description": "AccessKey ID（阿里云认证）", "nullable": true}, "accessKeySecret": {"type": "string", "description": "AccessKey Secret（加密存储）", "nullable": true}, "signMethod": {"type": "string", "description": "签名算法（hmacmd5、hmacsha1、hmacsha256）", "nullable": true}, "jwtSecret": {"type": "string", "description": "JWT密钥", "nullable": true}, "jwtExpiry": {"type": "integer", "description": "JWT过期时间（秒）", "format": "int32", "nullable": true}, "jwtToken": {"type": "string", "description": "JWT令牌", "nullable": true}, "certificate": {"type": "string", "description": "客户端证书", "nullable": true}, "privateKey": {"type": "string", "description": "私钥", "nullable": true}, "caCertificate": {"type": "string", "description": "CA证书", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "allowedIPs": {"type": "string", "description": "允许的IP地址列表", "nullable": true}, "deniedIPs": {"type": "string", "description": "拒绝的IP地址列表", "nullable": true}, "rateLimit": {"type": "integer", "description": "速率限制（消息/秒）", "format": "int32", "nullable": true}, "loginAttempts": {"type": "integer", "description": "登录尝试次数", "format": "int32", "nullable": true}, "expireTime": {"type": "string", "description": "过期时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastAuthTime": {"type": "string", "description": "最后认证时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastLoginTime": {"type": "string", "description": "最后登录时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastLoginIP": {"type": "string", "description": "最后登录IP", "nullable": true}, "successCount": {"type": "integer", "description": "认证成功次数", "format": "int32", "nullable": true}, "authSuccessCount": {"type": "integer", "description": "认证成功总数", "format": "int32", "nullable": true}, "failedCount": {"type": "integer", "description": "认证失败次数", "format": "int32", "nullable": true}, "isLocked": {"type": "boolean", "description": "是否锁定（0:未锁定，1:已锁定）", "nullable": true}, "lockTime": {"type": "string", "description": "锁定时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "unlockTime": {"type": "string", "description": "解锁时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lockReason": {"type": "string", "description": "锁定原因", "nullable": true}, "maxFailedCount": {"type": "integer", "description": "最大失败次数", "format": "int32", "nullable": true}, "lockDuration": {"type": "integer", "description": "锁定持续时间（分钟）", "format": "int32", "nullable": true}, "ipWhitelist": {"type": "string", "description": "IP白名单", "nullable": true}, "ipBlacklist": {"type": "string", "description": "IP黑名单", "nullable": true}, "allowedTopics": {"type": "string", "description": "允许的主题列表", "nullable": true}, "deniedTopics": {"type": "string", "description": "拒绝的主题列表", "nullable": true}, "maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32", "nullable": true}, "currentConnections": {"type": "integer", "description": "当前连接数", "format": "int32", "nullable": true}, "connectionTimeout": {"type": "integer", "description": "连接超时时间（秒）", "format": "int32", "nullable": true}, "maxMessageSize": {"type": "integer", "description": "最大消息大小", "format": "int32", "nullable": true}, "maxSubscriptions": {"type": "integer", "description": "最大订阅数", "format": "int32", "nullable": true}, "messageRateLimit": {"type": "integer", "description": "消息速率限制", "format": "int32", "nullable": true}, "byteRateLimit": {"type": "integer", "description": "字节速率限制", "format": "int32", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON）", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "key": {"type": "string", "description": "键值（用于缓存等场景）", "nullable": true, "readOnly": true}}, "additionalProperties": false, "description": "MQTT 客户端认证信息表"}, "MqttClientAuthOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "clientRefId": {"type": "integer", "description": "客户端引用ID", "format": "int64", "nullable": true}, "authMode": {"type": "string", "description": "认证模式（Username、AliyunSignature、JWT等）", "nullable": true}, "username": {"type": "string", "description": "用户名", "nullable": true}, "password": {"type": "string", "description": "密码", "nullable": true}, "passwordHash": {"type": "string", "description": "密码哈希", "nullable": true}, "passwordSalt": {"type": "string", "description": "密码盐值", "nullable": true}, "accessKeyId": {"type": "string", "description": "AccessKey ID（阿里云认证）", "nullable": true}, "accessKeySecret": {"type": "string", "description": "AccessKey Secret（加密存储）", "nullable": true}, "signMethod": {"type": "string", "description": "签名算法（hmacmd5、hmacsha1、hmacsha256）", "nullable": true}, "jwtSecret": {"type": "string", "description": "JWT密钥", "nullable": true}, "jwtExpiry": {"type": "integer", "description": "JWT过期时间（秒）", "format": "int32", "nullable": true}, "jwtToken": {"type": "string", "description": "JWT令牌", "nullable": true}, "certificate": {"type": "string", "description": "客户端证书", "nullable": true}, "privateKey": {"type": "string", "description": "私钥", "nullable": true}, "caCertificate": {"type": "string", "description": "CA证书", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "allowedIPs": {"type": "string", "description": "允许的IP地址列表", "nullable": true}, "deniedIPs": {"type": "string", "description": "拒绝的IP地址列表", "nullable": true}, "rateLimit": {"type": "integer", "description": "速率限制（消息/秒）", "format": "int32", "nullable": true}, "loginAttempts": {"type": "integer", "description": "登录尝试次数", "format": "int32", "nullable": true}, "expireTime": {"type": "string", "description": "过期时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastAuthTime": {"type": "string", "description": "最后认证时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastLoginTime": {"type": "string", "description": "最后登录时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastLoginIP": {"type": "string", "description": "最后登录IP", "nullable": true}, "successCount": {"type": "integer", "description": "认证成功次数", "format": "int32", "nullable": true}, "authSuccessCount": {"type": "integer", "description": "认证成功总数", "format": "int32", "nullable": true}, "failedCount": {"type": "integer", "description": "认证失败次数", "format": "int32", "nullable": true}, "isLocked": {"type": "boolean", "description": "是否锁定（0:未锁定，1:已锁定）", "nullable": true}, "lockTime": {"type": "string", "description": "锁定时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "unlockTime": {"type": "string", "description": "解锁时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lockReason": {"type": "string", "description": "锁定原因", "nullable": true}, "maxFailedCount": {"type": "integer", "description": "最大失败次数", "format": "int32", "nullable": true}, "lockDuration": {"type": "integer", "description": "锁定持续时间（分钟）", "format": "int32", "nullable": true}, "ipWhitelist": {"type": "string", "description": "IP白名单", "nullable": true}, "ipBlacklist": {"type": "string", "description": "IP黑名单", "nullable": true}, "allowedTopics": {"type": "string", "description": "允许的主题列表", "nullable": true}, "deniedTopics": {"type": "string", "description": "拒绝的主题列表", "nullable": true}, "maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32", "nullable": true}, "currentConnections": {"type": "integer", "description": "当前连接数", "format": "int32", "nullable": true}, "connectionTimeout": {"type": "integer", "description": "连接超时时间（秒）", "format": "int32", "nullable": true}, "maxMessageSize": {"type": "integer", "description": "最大消息大小", "format": "int32", "nullable": true}, "maxSubscriptions": {"type": "integer", "description": "最大订阅数", "format": "int32", "nullable": true}, "messageRateLimit": {"type": "integer", "description": "消息速率限制", "format": "int32", "nullable": true}, "byteRateLimit": {"type": "integer", "description": "字节速率限制", "format": "int32", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON）", "nullable": true}, "key": {"type": "string", "description": "", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "updateUserId": {"type": "integer", "description": "修改者Id", "format": "int64", "nullable": true}, "isDelete": {"type": "boolean", "description": "软删除"}, "createUserName": {"type": "string", "description": "创建者姓名", "nullable": true}, "updateUserName": {"type": "string", "description": "修改者姓名", "nullable": true}}, "additionalProperties": false, "description": "MQTT 客户端认证信息表输出参数"}, "MqttClientOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "deviceName": {"type": "string", "description": "设备名称", "nullable": true}, "groupId": {"type": "string", "description": "设备组ID", "nullable": true}, "productKey": {"type": "string", "description": "产品Key", "nullable": true}, "deviceSecret": {"type": "string", "description": "设备密钥（加密存储）", "nullable": true}, "ipAddress": {"type": "string", "description": "IP地址", "nullable": true}, "port": {"type": "integer", "description": "端口", "format": "int32", "nullable": true}, "status": {"type": "string", "description": "连接状态", "nullable": true}, "protocolVersion": {"type": "string", "description": "协议版本", "nullable": true}, "keepAlive": {"type": "integer", "description": "心跳间隔（秒）", "format": "int32", "nullable": true}, "cleanSession": {"type": "boolean", "description": "清除会话（0:否，1:是）", "nullable": true}, "connectedAt": {"type": "string", "description": "连接时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "disconnectedAt": {"type": "string", "description": "断开时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "messagesSent": {"type": "integer", "description": "发送消息数", "format": "int64", "nullable": true}, "messagesReceived": {"type": "integer", "description": "接收消息数", "format": "int64", "nullable": true}, "bytesSent": {"type": "integer", "description": "发送字节数", "format": "int64", "nullable": true}, "bytesReceived": {"type": "integer", "description": "接收字节数", "format": "int64", "nullable": true}, "subscriptionCount": {"type": "integer", "description": "订阅数量", "format": "int32", "nullable": true}, "maxSubscriptions": {"type": "integer", "description": "最大订阅数", "format": "int32", "nullable": true}, "deviceType": {"type": "string", "description": "设备类型", "nullable": true}, "deviceVersion": {"type": "string", "description": "设备版本", "nullable": true}, "deviceTags": {"type": "string", "description": "设备标签（JSON）", "nullable": true}, "locationInfo": {"type": "string", "description": "位置信息（JSON）", "nullable": true}, "isOnline": {"type": "boolean", "description": "是否在线（0:离线，1:在线）", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "authMode": {"type": "string", "description": "认证模式", "nullable": true}, "clientType": {"type": "string", "description": "客户端类型", "nullable": true}, "lastError": {"type": "string", "description": "最后错误信息", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON）", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "updateUserId": {"type": "integer", "description": "修改者Id", "format": "int64", "nullable": true}, "isDelete": {"type": "boolean", "description": "软删除"}, "createUserName": {"type": "string", "description": "创建者姓名", "nullable": true}, "updateUserName": {"type": "string", "description": "修改者姓名", "nullable": true}}, "additionalProperties": false, "description": "MQTT 客户端连接信息表输出参数"}, "MqttDeviceGroup": {"required": ["accessKeyId", "accessKeySecret", "authMode", "byteRateLimit", "certificateRequired", "connectionTimeout", "createdDeviceCount", "currentDeviceCount", "defaultQosLevel", "enableRetainMessage", "enableSharedSubscription", "enableWildcardSubscription", "encryptionRequired", "groupId", "groupName", "groupType", "instanceId", "isEnabled", "keepAliveTimeout", "maxConnections", "maxDevice<PERSON><PERSON>nt", "maxMessageSize", "messageRateLimit", "onlineDeviceCount", "productKey", "securityLevel", "sign<PERSON>ethod", "totalByteCount", "totalMessageCount"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "example": "2025-08-05 14:17:44"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "createUserName": {"type": "string", "description": "创建者姓名", "nullable": true}, "updateUserId": {"type": "integer", "description": "修改者Id", "format": "int64", "nullable": true}, "updateUserName": {"type": "string", "description": "修改者姓名", "nullable": true}, "isDelete": {"type": "boolean", "description": "软删除"}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64"}, "groupId": {"minLength": 1, "type": "string", "description": "设备组标识（如：GID_TestGroup）"}, "groupName": {"minLength": 1, "type": "string", "description": "设备组名称"}, "groupType": {"minLength": 1, "type": "string", "description": "设备组类型（Production、Test、Development等）"}, "productKey": {"minLength": 1, "type": "string", "description": "产品Key（阿里云IoT产品标识）"}, "description": {"type": "string", "description": "设备组描述", "nullable": true}, "maxDeviceCount": {"type": "integer", "description": "最大设备数量", "format": "int32"}, "currentDeviceCount": {"type": "integer", "description": "当前设备数量", "format": "int32"}, "allowedTopicPatterns": {"type": "string", "description": "允许的主题模式（JSON数组）", "nullable": true}, "deniedTopicPatterns": {"type": "string", "description": "禁止的主题模式（JSON数组）", "nullable": true}, "defaultQosLevel": {"type": "boolean", "description": "默认QoS等级（0,1,2）"}, "maxMessageSize": {"type": "integer", "description": "最大消息大小（字节）", "format": "int32"}, "messageRateLimit": {"type": "integer", "description": "消息速率限制（消息/秒）", "format": "int32"}, "byteRateLimit": {"type": "integer", "description": "字节速率限制（字节/秒）", "format": "int32"}, "maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32"}, "connectionTimeout": {"type": "integer", "description": "连接超时时间（秒）", "format": "int32"}, "keepAliveTimeout": {"type": "integer", "description": "心跳超时时间（秒）", "format": "int32"}, "enableRetainMessage": {"type": "boolean", "description": "是否允许保留消息"}, "enableWildcardSubscription": {"type": "boolean", "description": "是否允许通配符订阅"}, "enableSharedSubscription": {"type": "boolean", "description": "是否允许共享订阅"}, "ipWhitelist": {"type": "string", "description": "IP白名单（JSON数组）", "nullable": true}, "ipBlacklist": {"type": "string", "description": "IP黑名单（JSON数组）", "nullable": true}, "allowedTimeRanges": {"type": "string", "description": "允许连接的时间范围（JSON）", "nullable": true}, "securityLevel": {"minLength": 1, "type": "string", "description": "安全级别（Low、Medium、High）"}, "encryptionRequired": {"type": "boolean", "description": "是否要求加密连接"}, "certificateRequired": {"type": "boolean", "description": "是否要求客户端证书"}, "authMode": {"minLength": 1, "type": "string", "description": "认证模式（Signature、Username、JWT、Certificate）"}, "signMethod": {"minLength": 1, "type": "string", "description": "签名算法（hmacsha1、hmacsha256、hmacmd5）"}, "accessKeyId": {"minLength": 1, "type": "string", "description": "AccessKey ID（阿里云认证）"}, "accessKeySecret": {"minLength": 1, "type": "string", "description": "AccessKey Secret（加密存储）"}, "jwtSecret": {"type": "string", "description": "JWT密钥", "nullable": true}, "jwtExpiry": {"type": "integer", "description": "JWT过期时间（秒）", "format": "int32", "nullable": true}, "caCertificate": {"type": "string", "description": "CA证书", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用"}, "expireTime": {"type": "string", "description": "过期时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createdDeviceCount": {"type": "integer", "description": "已创建设备数", "format": "int32"}, "onlineDeviceCount": {"type": "integer", "description": "在线设备数", "format": "int32"}, "totalMessageCount": {"type": "integer", "description": "总消息数", "format": "int64"}, "totalByteCount": {"type": "integer", "description": "总字节数", "format": "int64"}, "lastMessageTime": {"type": "string", "description": "最后消息时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "alertRules": {"type": "string", "description": "告警规则（JSON）", "nullable": true}, "monitorConfig": {"type": "string", "description": "监控配置（JSON）", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON）", "nullable": true}, "tags": {"type": "string", "description": "标签（JSON）", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "MQTT设备组管理表"}, "MqttDeviceGroupInfo": {"type": "object", "properties": {"groupId": {"type": "integer", "description": "设备组ID", "format": "int64"}, "groupName": {"type": "string", "description": "设备组名称", "nullable": true}, "deviceType": {"type": "string", "description": "设备类型", "nullable": true}, "deviceLocation": {"type": "string", "description": "设备位置", "nullable": true}, "instancePrefix": {"type": "string", "description": "实例前缀", "nullable": true}, "uniqueId": {"type": "string", "description": "唯一标识", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "MQTT设备组信息"}, "MqttDeviceGroupOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64"}, "groupId": {"type": "string", "description": "设备组标识（如：GID_TestGroup）", "nullable": true}, "groupName": {"type": "string", "description": "设备组名称", "nullable": true}, "groupType": {"type": "string", "description": "设备组类型（Production、Test、Development等）", "nullable": true}, "productKey": {"type": "string", "description": "产品Key（阿里云IoT产品标识）", "nullable": true}, "description": {"type": "string", "description": "设备组描述", "nullable": true}, "maxDeviceCount": {"type": "integer", "description": "最大设备数量", "format": "int32"}, "currentDeviceCount": {"type": "integer", "description": "当前设备数量", "format": "int32"}, "allowedTopicPatterns": {"type": "string", "description": "允许的主题模式（JSON数组）", "nullable": true}, "deniedTopicPatterns": {"type": "string", "description": "禁止的主题模式（JSON数组）", "nullable": true}, "defaultQosLevel": {"type": "boolean", "description": "默认QoS等级（0,1,2）"}, "maxMessageSize": {"type": "integer", "description": "最大消息大小（字节）", "format": "int32"}, "messageRateLimit": {"type": "integer", "description": "消息速率限制（消息/秒）", "format": "int32"}, "byteRateLimit": {"type": "integer", "description": "字节速率限制（字节/秒）", "format": "int32"}, "maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32"}, "connectionTimeout": {"type": "integer", "description": "连接超时时间（秒）", "format": "int32"}, "keepAliveTimeout": {"type": "integer", "description": "心跳超时时间（秒）", "format": "int32"}, "enableRetainMessage": {"type": "boolean", "description": "是否允许保留消息"}, "enableWildcardSubscription": {"type": "boolean", "description": "是否允许通配符订阅"}, "enableSharedSubscription": {"type": "boolean", "description": "是否允许共享订阅"}, "ipWhitelist": {"type": "string", "description": "IP白名单（JSON数组）", "nullable": true}, "ipBlacklist": {"type": "string", "description": "IP黑名单（JSON数组）", "nullable": true}, "allowedTimeRanges": {"type": "string", "description": "允许连接的时间范围（JSON）", "nullable": true}, "securityLevel": {"type": "string", "description": "安全级别（Low、Medium、High）", "nullable": true}, "encryptionRequired": {"type": "boolean", "description": "是否要求加密连接"}, "certificateRequired": {"type": "boolean", "description": "是否要求客户端证书"}, "authMode": {"type": "string", "description": "认证模式（Signature、Username、JWT、Certificate）", "nullable": true}, "signMethod": {"type": "string", "description": "签名算法（hmacsha1、hmacsha256、hmacmd5）", "nullable": true}, "accessKeyId": {"type": "string", "description": "AccessKey ID（阿里云认证）", "nullable": true}, "accessKeySecret": {"type": "string", "description": "AccessKey Secret（加密存储）", "nullable": true}, "jwtSecret": {"type": "string", "description": "JWT密钥", "nullable": true}, "jwtExpiry": {"type": "integer", "description": "JWT过期时间（秒）", "format": "int32", "nullable": true}, "caCertificate": {"type": "string", "description": "CA证书", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用"}, "expireTime": {"type": "string", "description": "过期时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createdDeviceCount": {"type": "integer", "description": "已创建设备数", "format": "int32"}, "onlineDeviceCount": {"type": "integer", "description": "在线设备数", "format": "int32"}, "totalMessageCount": {"type": "integer", "description": "总消息数", "format": "int64"}, "totalByteCount": {"type": "integer", "description": "总字节数", "format": "int64"}, "lastMessageTime": {"type": "string", "description": "最后消息时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "alertRules": {"type": "string", "description": "告警规则（JSON）", "nullable": true}, "monitorConfig": {"type": "string", "description": "监控配置（JSON）", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON）", "nullable": true}, "tags": {"type": "string", "description": "标签（JSON）", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "updateUserId": {"type": "integer", "description": "修改者Id", "format": "int64", "nullable": true}, "isDelete": {"type": "boolean", "description": "软删除"}, "createUserName": {"type": "string", "description": "创建者姓名", "nullable": true}, "updateUserName": {"type": "string", "description": "修改者姓名", "nullable": true}}, "additionalProperties": false, "description": "MQTT设备组管理表输出参数"}, "MqttInstance": {"type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "example": "2025-08-05 14:17:44"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "createUserName": {"type": "string", "description": "创建者姓名", "nullable": true}, "updateUserId": {"type": "integer", "description": "修改者Id", "format": "int64", "nullable": true}, "updateUserName": {"type": "string", "description": "修改者姓名", "nullable": true}, "isDelete": {"type": "boolean", "description": "软删除"}, "instanceName": {"type": "string", "description": "实例名称", "nullable": true}, "instanceCode": {"type": "string", "description": "实例编码", "nullable": true}, "instanceType": {"type": "string", "description": "实例类型（EMQX、阿里云等）", "nullable": true}, "serverHost": {"type": "string", "description": "服务器地址", "nullable": true}, "serverPort": {"type": "integer", "description": "服务器端口", "format": "int32", "nullable": true}, "apiHost": {"type": "string", "description": "API地址", "nullable": true}, "apiPort": {"type": "integer", "description": "API端口", "format": "int32", "nullable": true}, "apiUsername": {"type": "string", "description": "API用户名", "nullable": true}, "apiPassword": {"type": "string", "description": "API密码（加密存储）", "nullable": true}, "status": {"type": "string", "description": "实例状态", "nullable": true}, "enableSsl": {"type": "boolean", "description": "是否启用SSL（0:否，1:是）", "nullable": true}, "sslPort": {"type": "integer", "description": "SSL端口", "format": "int32", "nullable": true}, "enableWebSocket": {"type": "boolean", "description": "是否启用WebSocket（0:否，1:是）", "nullable": true}, "wsPort": {"type": "integer", "description": "WebSocket端口", "format": "int32", "nullable": true}, "wssPort": {"type": "integer", "description": "WebSocket SSL端口", "format": "int32", "nullable": true}, "maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32", "nullable": true}, "currentConnections": {"type": "integer", "description": "当前连接数", "format": "int32", "nullable": true}, "aliyunProductKey": {"type": "string", "description": "阿里云产品Key", "nullable": true}, "aliyunRegionId": {"type": "string", "description": "阿里云区域ID", "nullable": true}, "aliyunInstanceId": {"type": "string", "description": "阿里云实例ID", "nullable": true}, "deviceIdPrefix": {"type": "string", "description": "设备ID前缀", "nullable": true}, "groupIdPrefix": {"type": "string", "description": "组ID前缀", "nullable": true}, "aliyunCompatible": {"type": "boolean", "description": "是否兼容阿里云（0:否，1:是）", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "configJson": {"type": "string", "description": "配置JSON", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "host": {"type": "string", "description": "主机地址（别名）", "nullable": true, "readOnly": true}, "port": {"type": "integer", "description": "端口（别名）", "format": "int32", "nullable": true, "readOnly": true}, "lastHeartbeat": {"type": "string", "description": "最后心跳时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "totalMessages": {"type": "integer", "description": "总消息数", "format": "int64", "nullable": true}, "totalBytes": {"type": "integer", "description": "总字节数", "format": "int64", "nullable": true}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "MQTT 实例信息表"}, "MqttInstanceOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "instanceName": {"type": "string", "description": "实例名称", "nullable": true}, "instanceCode": {"type": "string", "description": "实例编码", "nullable": true}, "instanceType": {"type": "string", "description": "实例类型（EMQX、阿里云等）", "nullable": true}, "serverHost": {"type": "string", "description": "服务器地址", "nullable": true}, "serverPort": {"type": "integer", "description": "服务器端口", "format": "int32", "nullable": true}, "apiHost": {"type": "string", "description": "API地址", "nullable": true}, "apiPort": {"type": "integer", "description": "API端口", "format": "int32", "nullable": true}, "apiUsername": {"type": "string", "description": "API用户名", "nullable": true}, "apiPassword": {"type": "string", "description": "API密码（加密存储）", "nullable": true}, "status": {"type": "string", "description": "实例状态", "nullable": true}, "enableSsl": {"type": "boolean", "description": "是否启用SSL（0:否，1:是）", "nullable": true}, "sslPort": {"type": "integer", "description": "SSL端口", "format": "int32", "nullable": true}, "enableWebSocket": {"type": "boolean", "description": "是否启用WebSocket（0:否，1:是）", "nullable": true}, "wsPort": {"type": "integer", "description": "WebSocket端口", "format": "int32", "nullable": true}, "wssPort": {"type": "integer", "description": "WebSocket SSL端口", "format": "int32", "nullable": true}, "maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32", "nullable": true}, "currentConnections": {"type": "integer", "description": "当前连接数", "format": "int32", "nullable": true}, "aliyunProductKey": {"type": "string", "description": "阿里云产品Key", "nullable": true}, "aliyunRegionId": {"type": "string", "description": "阿里云区域ID", "nullable": true}, "aliyunInstanceId": {"type": "string", "description": "阿里云实例ID", "nullable": true}, "deviceIdPrefix": {"type": "string", "description": "设备ID前缀", "nullable": true}, "groupIdPrefix": {"type": "string", "description": "组ID前缀", "nullable": true}, "aliyunCompatible": {"type": "boolean", "description": "是否兼容阿里云（0:否，1:是）", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "configJson": {"type": "string", "description": "配置JSON", "nullable": true}, "lastHeartbeat": {"type": "string", "description": "最后心跳时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "totalMessages": {"type": "integer", "description": "总消息数", "format": "int64", "nullable": true}, "totalBytes": {"type": "integer", "description": "总字节数", "format": "int64", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "updateUserId": {"type": "integer", "description": "修改者Id", "format": "int64", "nullable": true}, "isDelete": {"type": "boolean", "description": "软删除"}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserName": {"type": "string", "description": "创建者姓名", "nullable": true}, "updateUserName": {"type": "string", "description": "修改者姓名", "nullable": true}}, "additionalProperties": false, "description": "MQTT 实例信息表输出参数"}, "MqttMessageHistory": {"required": ["instanceId", "topicName"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "example": "2025-08-05 14:17:44"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "createUserName": {"type": "string", "description": "创建者姓名", "nullable": true}, "updateUserId": {"type": "integer", "description": "修改者Id", "format": "int64", "nullable": true}, "updateUserName": {"type": "string", "description": "修改者姓名", "nullable": true}, "instanceId": {"type": "integer", "description": "MQTT实例ID", "format": "int64"}, "clientId": {"maxLength": 128, "type": "string", "description": "客户端ID", "nullable": true}, "topicName": {"maxLength": 512, "minLength": 1, "type": "string", "description": "主题名称"}, "payload": {"type": "string", "description": "消息内容", "nullable": true}, "qosLevel": {"maximum": 2, "minimum": 0, "type": "integer", "description": "QoS等级", "format": "int32", "nullable": true}, "isRetained": {"type": "boolean", "description": "是否保留消息", "nullable": true}, "direction": {"maxLength": 20, "type": "string", "description": "消息方向（Publish/Subscribe）", "nullable": true}, "messageSize": {"type": "integer", "description": "消息大小（字节）", "format": "int64", "nullable": true}, "senderIpAddress": {"maxLength": 45, "type": "string", "description": "发送者IP地址", "nullable": true}, "receivedAt": {"type": "string", "description": "接收时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "processStatus": {"maxLength": 20, "type": "string", "description": "处理状态", "nullable": true}, "processedAt": {"type": "string", "description": "处理时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "errorMessage": {"maxLength": 1000, "type": "string", "description": "错误信息", "nullable": true}, "retryCount": {"type": "integer", "description": "重试次数", "format": "int32", "nullable": true}, "messageTags": {"type": "string", "description": "消息标签（JSON格式）", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON格式）", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "description": "备注", "nullable": true}, "key": {"type": "string", "description": "键值（用于缓存等场景）", "nullable": true, "readOnly": true}}, "additionalProperties": false, "description": "MQTT消息历史记录表"}, "MqttMessageHistoryOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID", "format": "int64"}, "instanceId": {"type": "integer", "description": "MQTT实例ID", "format": "int64"}, "clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "topicName": {"type": "string", "description": "主题名称", "nullable": true}, "payload": {"type": "string", "description": "消息内容", "nullable": true}, "qosLevel": {"type": "integer", "description": "QoS等级", "format": "int32", "nullable": true}, "isRetained": {"type": "boolean", "description": "是否保留消息", "nullable": true}, "direction": {"type": "string", "description": "消息方向", "nullable": true}, "messageSize": {"type": "integer", "description": "消息大小", "format": "int64", "nullable": true}, "senderIpAddress": {"type": "string", "description": "发送者IP地址", "nullable": true}, "receivedAt": {"type": "string", "description": "接收时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "processStatus": {"type": "string", "description": "处理状态", "nullable": true}, "processedAt": {"type": "string", "description": "处理时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "errorMessage": {"type": "string", "description": "错误信息", "nullable": true}, "retryCount": {"type": "integer", "description": "重试次数", "format": "int32", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "MQTT消息历史记录输出"}, "MqttMessageRecordResult": {"type": "object", "properties": {"topicName": {"type": "string", "description": "主题名称", "nullable": true}, "success": {"type": "boolean", "description": "是否成功"}, "message": {"type": "string", "description": "消息", "nullable": true}}, "additionalProperties": false, "description": "MQTT消息记录结果"}, "MqttMessageStatisticsOutput": {"type": "object", "properties": {"totalCount": {"type": "integer", "description": "总消息数", "format": "int32"}, "publishCount": {"type": "integer", "description": "发布消息数", "format": "int32"}, "subscribeCount": {"type": "integer", "description": "订阅消息数", "format": "int32"}, "processedCount": {"type": "integer", "description": "已处理消息数", "format": "int32"}, "failedCount": {"type": "integer", "description": "失败消息数", "format": "int32"}, "pendingCount": {"type": "integer", "description": "待处理消息数", "format": "int32"}, "totalSize": {"type": "integer", "description": "总消息大小", "format": "int64"}, "averageSize": {"type": "integer", "description": "平均消息大小", "format": "int64"}, "topTopics": {"type": "array", "items": {"$ref": "#/components/schemas/TopicStatistics"}, "description": "热门主题", "nullable": true}, "statisticsTime": {"type": "string", "description": "统计时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "MQTT消息统计信息输出"}, "MqttSubscriptionOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "clientRefId": {"type": "integer", "description": "客户端引用ID", "format": "int64", "nullable": true}, "topicName": {"type": "string", "description": "主题名称", "nullable": true}, "topicId": {"type": "integer", "description": "主题ID", "format": "int64", "nullable": true}, "qosLevel": {"type": "integer", "description": "QoS等级（0、1、2）", "format": "int32", "nullable": true}, "status": {"type": "string", "description": "订阅状态（Active、Inactive、Suspended）", "nullable": true}, "subscribedAt": {"type": "string", "description": "订阅时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "unsubscribedAt": {"type": "string", "description": "取消订阅时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "messagesReceived": {"type": "integer", "description": "接收消息数", "format": "int64", "nullable": true}, "bytesReceived": {"type": "integer", "description": "接收字节数", "format": "int64", "nullable": true}, "messagesDropped": {"type": "integer", "description": "丢弃消息数", "format": "int64", "nullable": true}, "lastMessageTime": {"type": "string", "description": "最后消息时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastMessageId": {"type": "string", "description": "最后消息ID", "nullable": true}, "isSharedSubscription": {"type": "boolean", "description": "是否共享订阅", "nullable": true}, "sharedGroupName": {"type": "string", "description": "共享组名", "nullable": true}, "subscriptionOptions": {"type": "string", "description": "订阅选项（JSON）", "nullable": true}, "messageFilter": {"type": "string", "description": "消息过滤器", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用", "nullable": true}, "priority": {"type": "integer", "description": "优先级", "format": "int32", "nullable": true}, "maxQueueLength": {"type": "integer", "description": "最大队列长度", "format": "int32", "nullable": true}, "currentQueueLength": {"type": "integer", "description": "当前队列长度", "format": "int32", "nullable": true}, "handlingStrategy": {"type": "string", "description": "处理策略（FIFO、LIFO、Priority）", "nullable": true}, "retryCount": {"type": "integer", "description": "重试次数", "format": "int32", "nullable": true}, "maxRetryCount": {"type": "integer", "description": "最大重试次数", "format": "int32", "nullable": true}, "lastError": {"type": "string", "description": "最后错误信息", "nullable": true}, "statistics": {"type": "string", "description": "统计信息（JSON）", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON）", "nullable": true}, "configJson": {"type": "string", "description": "配置信息（JSON）", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建用户ID", "format": "int64", "nullable": true}, "updateUserId": {"type": "integer", "description": "更新用户ID", "format": "int64", "nullable": true}}, "additionalProperties": false, "description": "MQTT订阅关系表输出参数"}, "MqttTopic": {"type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "example": "2025-08-05 14:17:44"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "createUserName": {"type": "string", "description": "创建者姓名", "nullable": true}, "updateUserId": {"type": "integer", "description": "修改者Id", "format": "int64", "nullable": true}, "updateUserName": {"type": "string", "description": "修改者姓名", "nullable": true}, "isDelete": {"type": "boolean", "description": "软删除"}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "topicName": {"type": "string", "description": "主题名称", "nullable": true}, "topicType": {"type": "string", "description": "主题类型", "nullable": true}, "qosLevel": {"type": "boolean", "description": "QoS等级（0,1,2）", "nullable": true}, "retainMessage": {"type": "boolean", "description": "是否保留消息（0:否，1:是）", "nullable": true}, "subscriptionCount": {"type": "integer", "description": "订阅数量", "format": "int32", "nullable": true}, "messageCount": {"type": "integer", "description": "消息数量", "format": "int64", "nullable": true}, "byteCount": {"type": "integer", "description": "字节数量", "format": "int64", "nullable": true}, "lastMessageTime": {"type": "string", "description": "最后消息时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastMessageContent": {"type": "string", "description": "最后消息内容", "nullable": true}, "maxMessageSize": {"type": "integer", "description": "最大消息大小", "format": "int32", "nullable": true}, "messageExpiry": {"type": "integer", "description": "消息过期时间（秒）", "format": "int32", "nullable": true}, "allowPublish": {"type": "boolean", "description": "允许发布（0:否，1:是）", "nullable": true}, "allowSubscribe": {"type": "boolean", "description": "允许订阅（0:否，1:是）", "nullable": true}, "publishPermissions": {"type": "string", "description": "发布权限（JSON）", "nullable": true}, "subscribePermissions": {"type": "string", "description": "订阅权限（JSON）", "nullable": true}, "isRetained": {"type": "boolean", "description": "是否保留（0:否，1:是）", "nullable": true}, "description": {"type": "string", "description": "描述", "nullable": true}, "topicTags": {"type": "string", "description": "主题标签（JSON）", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "isSystemTopic": {"type": "boolean", "description": "是否系统主题（0:否，1:是）", "nullable": true}, "monitorConfig": {"type": "string", "description": "监控配置（JSON）", "nullable": true}, "alertRules": {"type": "string", "description": "告警规则（JSON）", "nullable": true}, "forwardRules": {"type": "string", "description": "转发规则（JSON）", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON）", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "MQTT 主题信息表"}, "MqttTopicOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "topicName": {"type": "string", "description": "主题名称", "nullable": true}, "topicType": {"type": "string", "description": "主题类型", "nullable": true}, "qosLevel": {"type": "boolean", "description": "QoS等级（0,1,2）", "nullable": true}, "retainMessage": {"type": "boolean", "description": "是否保留消息（0:否，1:是）", "nullable": true}, "subscriptionCount": {"type": "integer", "description": "订阅数量", "format": "int32", "nullable": true}, "messageCount": {"type": "integer", "description": "消息数量", "format": "int64", "nullable": true}, "byteCount": {"type": "integer", "description": "字节数量", "format": "int64", "nullable": true}, "lastMessageTime": {"type": "string", "description": "最后消息时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastMessageContent": {"type": "string", "description": "最后消息内容", "nullable": true}, "maxMessageSize": {"type": "integer", "description": "最大消息大小", "format": "int32", "nullable": true}, "messageExpiry": {"type": "integer", "description": "消息过期时间（秒）", "format": "int32", "nullable": true}, "allowPublish": {"type": "boolean", "description": "允许发布（0:否，1:是）", "nullable": true}, "allowSubscribe": {"type": "boolean", "description": "允许订阅（0:否，1:是）", "nullable": true}, "publishPermissions": {"type": "string", "description": "发布权限（JSON）", "nullable": true}, "subscribePermissions": {"type": "string", "description": "订阅权限（JSON）", "nullable": true}, "isRetained": {"type": "boolean", "description": "是否保留（0:否，1:是）", "nullable": true}, "description": {"type": "string", "description": "描述", "nullable": true}, "topicTags": {"type": "string", "description": "主题标签（JSON）", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "isSystemTopic": {"type": "boolean", "description": "是否系统主题（0:否，1:是）", "nullable": true}, "monitorConfig": {"type": "string", "description": "监控配置（JSON）", "nullable": true}, "alertRules": {"type": "string", "description": "告警规则（JSON）", "nullable": true}, "forwardRules": {"type": "string", "description": "转发规则（JSON）", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON）", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "updateUserId": {"type": "integer", "description": "修改者Id", "format": "int64", "nullable": true}, "isDelete": {"type": "boolean", "description": "软删除"}, "createUserName": {"type": "string", "description": "创建者姓名", "nullable": true}, "updateUserName": {"type": "string", "description": "修改者姓名", "nullable": true}}, "additionalProperties": false, "description": "MQTT 主题信息表输出参数"}, "PageMqttClientAuthInput": {"type": "object", "properties": {"search": {"$ref": "#/components/schemas/Search"}, "keyword": {"type": "string", "description": "模糊查询关键字", "nullable": true}, "filter": {"$ref": "#/components/schemas/Filter"}, "page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": "string", "description": "排序字段", "nullable": true}, "order": {"type": "string", "description": "排序方向", "nullable": true}, "descStr": {"type": "string", "description": "降序排序", "nullable": true}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "clientRefId": {"type": "integer", "description": "客户端引用ID", "format": "int64", "nullable": true}, "authMode": {"type": "string", "description": "认证模式（Username、AliyunSignature、JWT等）", "nullable": true}, "username": {"type": "string", "description": "用户名", "nullable": true}, "password": {"type": "string", "description": "密码", "nullable": true}, "passwordHash": {"type": "string", "description": "密码哈希", "nullable": true}, "passwordSalt": {"type": "string", "description": "密码盐值", "nullable": true}, "accessKeyId": {"type": "string", "description": "AccessKey ID（阿里云认证）", "nullable": true}, "accessKeySecret": {"type": "string", "description": "AccessKey Secret（加密存储）", "nullable": true}, "signMethod": {"type": "string", "description": "签名算法（hmacmd5、hmacsha1、hmacsha256）", "nullable": true}, "jwtSecret": {"type": "string", "description": "JWT密钥", "nullable": true}, "jwtExpiry": {"type": "integer", "description": "JWT过期时间（秒）", "format": "int32", "nullable": true}, "jwtToken": {"type": "string", "description": "JWT令牌", "nullable": true}, "certificate": {"type": "string", "description": "客户端证书", "nullable": true}, "privateKey": {"type": "string", "description": "私钥", "nullable": true}, "caCertificate": {"type": "string", "description": "CA证书", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "allowedIPs": {"type": "string", "description": "允许的IP地址列表", "nullable": true}, "deniedIPs": {"type": "string", "description": "拒绝的IP地址列表", "nullable": true}, "rateLimit": {"type": "integer", "description": "速率限制（消息/秒）", "format": "int32", "nullable": true}, "loginAttempts": {"type": "integer", "description": "登录尝试次数", "format": "int32", "nullable": true}, "expireTimeRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "过期时间范围", "nullable": true}, "lastAuthTimeRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "最后认证时间范围", "nullable": true}, "lastLoginTimeRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "最后登录时间范围", "nullable": true}, "lastLoginIP": {"type": "string", "description": "最后登录IP", "nullable": true}, "successCount": {"type": "integer", "description": "认证成功次数", "format": "int32", "nullable": true}, "authSuccessCount": {"type": "integer", "description": "认证成功总数", "format": "int32", "nullable": true}, "failedCount": {"type": "integer", "description": "认证失败次数", "format": "int32", "nullable": true}, "isLocked": {"type": "boolean", "description": "是否锁定（0:未锁定，1:已锁定）", "nullable": true}, "lockTimeRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "锁定时间范围", "nullable": true}, "unlockTimeRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "解锁时间范围", "nullable": true}, "lockReason": {"type": "string", "description": "锁定原因", "nullable": true}, "maxFailedCount": {"type": "integer", "description": "最大失败次数", "format": "int32", "nullable": true}, "lockDuration": {"type": "integer", "description": "锁定持续时间（分钟）", "format": "int32", "nullable": true}, "ipWhitelist": {"type": "string", "description": "IP白名单", "nullable": true}, "ipBlacklist": {"type": "string", "description": "IP黑名单", "nullable": true}, "allowedTopics": {"type": "string", "description": "允许的主题列表", "nullable": true}, "deniedTopics": {"type": "string", "description": "拒绝的主题列表", "nullable": true}, "maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32", "nullable": true}, "currentConnections": {"type": "integer", "description": "当前连接数", "format": "int32", "nullable": true}, "connectionTimeout": {"type": "integer", "description": "连接超时时间（秒）", "format": "int32", "nullable": true}, "maxMessageSize": {"type": "integer", "description": "最大消息大小", "format": "int32", "nullable": true}, "maxSubscriptions": {"type": "integer", "description": "最大订阅数", "format": "int32", "nullable": true}, "messageRateLimit": {"type": "integer", "description": "消息速率限制", "format": "int32", "nullable": true}, "byteRateLimit": {"type": "integer", "description": "字节速率限制", "format": "int32", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON）", "nullable": true}, "key": {"type": "string", "description": "", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "MQTT 客户端认证信息表分页查询输入参数"}, "PageMqttClientInput": {"type": "object", "properties": {"search": {"$ref": "#/components/schemas/Search"}, "keyword": {"type": "string", "description": "模糊查询关键字", "nullable": true}, "filter": {"$ref": "#/components/schemas/Filter"}, "page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": "string", "description": "排序字段", "nullable": true}, "order": {"type": "string", "description": "排序方向", "nullable": true}, "descStr": {"type": "string", "description": "降序排序", "nullable": true}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "deviceName": {"type": "string", "description": "设备名称", "nullable": true}, "groupId": {"type": "string", "description": "设备组ID", "nullable": true}, "productKey": {"type": "string", "description": "产品Key", "nullable": true}, "deviceSecret": {"type": "string", "description": "设备密钥（加密存储）", "nullable": true}, "ipAddress": {"type": "string", "description": "IP地址", "nullable": true}, "port": {"type": "integer", "description": "端口", "format": "int32", "nullable": true}, "status": {"type": "string", "description": "连接状态", "nullable": true}, "protocolVersion": {"type": "string", "description": "协议版本", "nullable": true}, "keepAlive": {"type": "integer", "description": "心跳间隔（秒）", "format": "int32", "nullable": true}, "cleanSession": {"type": "boolean", "description": "清除会话（0:否，1:是）", "nullable": true}, "connectedAtRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "连接时间范围", "nullable": true}, "disconnectedAtRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "断开时间范围", "nullable": true}, "lastActivityRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "最后活动时间范围", "nullable": true}, "messagesSent": {"type": "integer", "description": "发送消息数", "format": "int64", "nullable": true}, "messagesReceived": {"type": "integer", "description": "接收消息数", "format": "int64", "nullable": true}, "bytesSent": {"type": "integer", "description": "发送字节数", "format": "int64", "nullable": true}, "bytesReceived": {"type": "integer", "description": "接收字节数", "format": "int64", "nullable": true}, "subscriptionCount": {"type": "integer", "description": "订阅数量", "format": "int32", "nullable": true}, "maxSubscriptions": {"type": "integer", "description": "最大订阅数", "format": "int32", "nullable": true}, "deviceType": {"type": "string", "description": "设备类型", "nullable": true}, "deviceVersion": {"type": "string", "description": "设备版本", "nullable": true}, "deviceTags": {"type": "string", "description": "设备标签（JSON）", "nullable": true}, "locationInfo": {"type": "string", "description": "位置信息（JSON）", "nullable": true}, "isOnline": {"type": "boolean", "description": "是否在线（0:离线，1:在线）", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "authMode": {"type": "string", "description": "认证模式", "nullable": true}, "clientType": {"type": "string", "description": "客户端类型", "nullable": true}, "lastError": {"type": "string", "description": "最后错误信息", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON）", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "MQTT 客户端连接信息表分页查询输入参数"}, "PageMqttDeviceGroupInput": {"type": "object", "properties": {"search": {"$ref": "#/components/schemas/Search"}, "keyword": {"type": "string", "description": "模糊查询关键字", "nullable": true}, "filter": {"$ref": "#/components/schemas/Filter"}, "page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": "string", "description": "排序字段", "nullable": true}, "order": {"type": "string", "description": "排序方向", "nullable": true}, "descStr": {"type": "string", "description": "降序排序", "nullable": true}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "groupId": {"type": "string", "description": "设备组标识（如：GID_TestGroup）", "nullable": true}, "groupName": {"type": "string", "description": "设备组名称", "nullable": true}, "groupType": {"type": "string", "description": "设备组类型（Production、Test、Development等）", "nullable": true}, "productKey": {"type": "string", "description": "产品Key（阿里云IoT产品标识）", "nullable": true}, "description": {"type": "string", "description": "设备组描述", "nullable": true}, "maxDeviceCount": {"type": "integer", "description": "最大设备数量", "format": "int32", "nullable": true}, "currentDeviceCount": {"type": "integer", "description": "当前设备数量", "format": "int32", "nullable": true}, "allowedTopicPatterns": {"type": "string", "description": "允许的主题模式（JSON数组）", "nullable": true}, "deniedTopicPatterns": {"type": "string", "description": "禁止的主题模式（JSON数组）", "nullable": true}, "defaultQosLevel": {"type": "boolean", "description": "默认QoS等级（0,1,2）", "nullable": true}, "maxMessageSize": {"type": "integer", "description": "最大消息大小（字节）", "format": "int32", "nullable": true}, "messageRateLimit": {"type": "integer", "description": "消息速率限制（消息/秒）", "format": "int32", "nullable": true}, "byteRateLimit": {"type": "integer", "description": "字节速率限制（字节/秒）", "format": "int32", "nullable": true}, "maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32", "nullable": true}, "connectionTimeout": {"type": "integer", "description": "连接超时时间（秒）", "format": "int32", "nullable": true}, "keepAliveTimeout": {"type": "integer", "description": "心跳超时时间（秒）", "format": "int32", "nullable": true}, "enableRetainMessage": {"type": "boolean", "description": "是否允许保留消息", "nullable": true}, "enableWildcardSubscription": {"type": "boolean", "description": "是否允许通配符订阅", "nullable": true}, "enableSharedSubscription": {"type": "boolean", "description": "是否允许共享订阅", "nullable": true}, "ipWhitelist": {"type": "string", "description": "IP白名单（JSON数组）", "nullable": true}, "ipBlacklist": {"type": "string", "description": "IP黑名单（JSON数组）", "nullable": true}, "allowedTimeRanges": {"type": "string", "description": "允许连接的时间范围（JSON）", "nullable": true}, "securityLevel": {"type": "string", "description": "安全级别（Low、Medium、High）", "nullable": true}, "encryptionRequired": {"type": "boolean", "description": "是否要求加密连接", "nullable": true}, "certificateRequired": {"type": "boolean", "description": "是否要求客户端证书", "nullable": true}, "authMode": {"type": "string", "description": "认证模式（Signature、Username、JWT、Certificate）", "nullable": true}, "signMethod": {"type": "string", "description": "签名算法（hmacsha1、hmacsha256、hmacmd5）", "nullable": true}, "accessKeyId": {"type": "string", "description": "AccessKey ID（阿里云认证）", "nullable": true}, "accessKeySecret": {"type": "string", "description": "AccessKey Secret（加密存储）", "nullable": true}, "jwtSecret": {"type": "string", "description": "JWT密钥", "nullable": true}, "jwtExpiry": {"type": "integer", "description": "JWT过期时间（秒）", "format": "int32", "nullable": true}, "caCertificate": {"type": "string", "description": "CA证书", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用", "nullable": true}, "expireTimeRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "过期时间范围", "nullable": true}, "lastActivityRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "最后活动时间范围", "nullable": true}, "createdDeviceCount": {"type": "integer", "description": "已创建设备数", "format": "int32", "nullable": true}, "onlineDeviceCount": {"type": "integer", "description": "在线设备数", "format": "int32", "nullable": true}, "totalMessageCount": {"type": "integer", "description": "总消息数", "format": "int64", "nullable": true}, "totalByteCount": {"type": "integer", "description": "总字节数", "format": "int64", "nullable": true}, "lastMessageTimeRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "最后消息时间范围", "nullable": true}, "alertRules": {"type": "string", "description": "告警规则（JSON）", "nullable": true}, "monitorConfig": {"type": "string", "description": "监控配置（JSON）", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON）", "nullable": true}, "tags": {"type": "string", "description": "标签（JSON）", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "selectKeyList": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "选中主键列表", "nullable": true}}, "additionalProperties": false, "description": "MQTT设备组管理表分页查询输入参数"}, "PageMqttInstanceInput": {"type": "object", "properties": {"search": {"$ref": "#/components/schemas/Search"}, "keyword": {"type": "string", "description": "模糊查询关键字", "nullable": true}, "filter": {"$ref": "#/components/schemas/Filter"}, "page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": "string", "description": "排序字段", "nullable": true}, "order": {"type": "string", "description": "排序方向", "nullable": true}, "descStr": {"type": "string", "description": "降序排序", "nullable": true}, "instanceName": {"type": "string", "description": "实例名称", "nullable": true}, "instanceCode": {"type": "string", "description": "实例编码", "nullable": true}, "instanceType": {"type": "string", "description": "实例类型（EMQX、阿里云等）", "nullable": true}, "serverHost": {"type": "string", "description": "服务器地址", "nullable": true}, "serverPort": {"type": "integer", "description": "服务器端口", "format": "int32", "nullable": true}, "apiHost": {"type": "string", "description": "API地址", "nullable": true}, "apiPort": {"type": "integer", "description": "API端口", "format": "int32", "nullable": true}, "apiUsername": {"type": "string", "description": "API用户名", "nullable": true}, "apiPassword": {"type": "string", "description": "API密码（加密存储）", "nullable": true}, "status": {"type": "string", "description": "实例状态", "nullable": true}, "enableSsl": {"type": "boolean", "description": "是否启用SSL（0:否，1:是）", "nullable": true}, "sslPort": {"type": "integer", "description": "SSL端口", "format": "int32", "nullable": true}, "enableWebSocket": {"type": "boolean", "description": "是否启用WebSocket（0:否，1:是）", "nullable": true}, "wsPort": {"type": "integer", "description": "WebSocket端口", "format": "int32", "nullable": true}, "wssPort": {"type": "integer", "description": "WebSocket SSL端口", "format": "int32", "nullable": true}, "maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32", "nullable": true}, "currentConnections": {"type": "integer", "description": "当前连接数", "format": "int32", "nullable": true}, "aliyunProductKey": {"type": "string", "description": "阿里云产品Key", "nullable": true}, "aliyunRegionId": {"type": "string", "description": "阿里云区域ID", "nullable": true}, "aliyunInstanceId": {"type": "string", "description": "阿里云实例ID", "nullable": true}, "deviceIdPrefix": {"type": "string", "description": "设备ID前缀", "nullable": true}, "groupIdPrefix": {"type": "string", "description": "组ID前缀", "nullable": true}, "aliyunCompatible": {"type": "boolean", "description": "是否兼容阿里云（0:否，1:是）", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "configJson": {"type": "string", "description": "配置JSON", "nullable": true}, "lastHeartbeatRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "最后心跳时间范围", "nullable": true}, "totalMessages": {"type": "integer", "description": "总消息数", "format": "int64", "nullable": true}, "totalBytes": {"type": "integer", "description": "总字节数", "format": "int64", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "lastActivityRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "最后活动时间范围", "nullable": true}}, "additionalProperties": false, "description": "MQTT 实例信息表分页查询输入参数"}, "PageMqttMessageHistoryInput": {"type": "object", "properties": {"search": {"$ref": "#/components/schemas/Search"}, "keyword": {"type": "string", "description": "模糊查询关键字", "nullable": true}, "filter": {"$ref": "#/components/schemas/Filter"}, "page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": "string", "description": "排序字段", "nullable": true}, "order": {"type": "string", "description": "排序方向", "nullable": true}, "descStr": {"type": "string", "description": "降序排序", "nullable": true}, "instanceId": {"type": "integer", "description": "MQTT实例ID", "format": "int64"}, "clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "topicName": {"type": "string", "description": "主题名称", "nullable": true}, "direction": {"type": "string", "description": "消息方向", "nullable": true}, "processStatus": {"type": "string", "description": "处理状态", "nullable": true}, "qosLevel": {"type": "integer", "description": "QoS等级", "format": "int32", "nullable": true}, "isRetained": {"type": "boolean", "description": "是否保留消息", "nullable": true}, "receivedAtStart": {"type": "string", "description": "接收时间开始", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "receivedAtEnd": {"type": "string", "description": "接收时间结束", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "processedAtStart": {"type": "string", "description": "处理时间开始", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "processedAtEnd": {"type": "string", "description": "处理时间结束", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "分页查询MQTT消息历史记录输入"}, "PageMqttSubscriptionInput": {"type": "object", "properties": {"search": {"$ref": "#/components/schemas/Search"}, "filter": {"$ref": "#/components/schemas/Filter"}, "page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": "string", "description": "排序字段", "nullable": true}, "order": {"type": "string", "description": "排序方向", "nullable": true}, "descStr": {"type": "string", "description": "降序排序", "nullable": true}, "keyword": {"type": "string", "description": "关键字查询", "nullable": true}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "clientRefId": {"type": "integer", "description": "客户端引用ID", "format": "int64", "nullable": true}, "topicName": {"type": "string", "description": "主题名称", "nullable": true}, "topicId": {"type": "integer", "description": "主题ID", "format": "int64", "nullable": true}, "qosLevel": {"type": "integer", "description": "QoS等级", "format": "int32", "nullable": true}, "status": {"type": "string", "description": "订阅状态", "nullable": true}, "isSharedSubscription": {"type": "boolean", "description": "是否共享订阅", "nullable": true}, "sharedGroupName": {"type": "string", "description": "共享组名", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用", "nullable": true}, "priority": {"type": "integer", "description": "优先级", "format": "int32", "nullable": true}, "subscribedAtStart": {"type": "string", "description": "订阅开始时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "subscribedAtEnd": {"type": "string", "description": "订阅结束时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastActivityStart": {"type": "string", "description": "最后活动开始时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastActivityEnd": {"type": "string", "description": "最后活动结束时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "handlingStrategy": {"type": "string", "description": "处理策略", "nullable": true}, "subscribedAtRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "订阅时间范围", "nullable": true}, "unsubscribedAtRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "取消订阅时间范围", "nullable": true}, "lastActivityRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "最后活动时间范围", "nullable": true}, "lastMessageTimeRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "最后消息时间范围", "nullable": true}}, "additionalProperties": false, "description": "分页查询MQTT订阅关系表参数"}, "PageMqttTopicInput": {"type": "object", "properties": {"search": {"$ref": "#/components/schemas/Search"}, "keyword": {"type": "string", "description": "模糊查询关键字", "nullable": true}, "filter": {"$ref": "#/components/schemas/Filter"}, "page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": "string", "description": "排序字段", "nullable": true}, "order": {"type": "string", "description": "排序方向", "nullable": true}, "descStr": {"type": "string", "description": "降序排序", "nullable": true}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "topicName": {"type": "string", "description": "主题名称", "nullable": true}, "topicType": {"type": "string", "description": "主题类型", "nullable": true}, "qosLevel": {"type": "boolean", "description": "QoS等级（0,1,2）", "nullable": true}, "retainMessage": {"type": "boolean", "description": "是否保留消息（0:否，1:是）", "nullable": true}, "subscriptionCount": {"type": "integer", "description": "订阅数量", "format": "int32", "nullable": true}, "messageCount": {"type": "integer", "description": "消息数量", "format": "int64", "nullable": true}, "byteCount": {"type": "integer", "description": "字节数量", "format": "int64", "nullable": true}, "lastMessageTimeRange": {"type": "array", "items": {"type": "string", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "description": "最后消息时间范围", "nullable": true}, "lastMessageContent": {"type": "string", "description": "最后消息内容", "nullable": true}, "maxMessageSize": {"type": "integer", "description": "最大消息大小", "format": "int32", "nullable": true}, "messageExpiry": {"type": "integer", "description": "消息过期时间（秒）", "format": "int32", "nullable": true}, "allowPublish": {"type": "boolean", "description": "允许发布（0:否，1:是）", "nullable": true}, "allowSubscribe": {"type": "boolean", "description": "允许订阅（0:否，1:是）", "nullable": true}, "publishPermissions": {"type": "string", "description": "发布权限（JSON）", "nullable": true}, "subscribePermissions": {"type": "string", "description": "订阅权限（JSON）", "nullable": true}, "isRetained": {"type": "boolean", "description": "是否保留（0:否，1:是）", "nullable": true}, "description": {"type": "string", "description": "描述", "nullable": true}, "topicTags": {"type": "string", "description": "主题标签（JSON）", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "isSystemTopic": {"type": "boolean", "description": "是否系统主题（0:否，1:是）", "nullable": true}, "monitorConfig": {"type": "string", "description": "监控配置（JSON）", "nullable": true}, "alertRules": {"type": "string", "description": "告警规则（JSON）", "nullable": true}, "forwardRules": {"type": "string", "description": "转发规则（JSON）", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON）", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "MQTT 主题信息表分页查询输入参数"}, "RecordMqttMessageInput": {"required": ["instanceId", "topicName"], "type": "object", "properties": {"instanceId": {"type": "integer", "description": "MQTT实例ID", "format": "int64"}, "clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "topicName": {"minLength": 1, "type": "string", "description": "主题名称"}, "payload": {"type": "string", "description": "消息内容", "nullable": true}, "qosLevel": {"type": "integer", "description": "QoS等级", "format": "int32", "nullable": true}, "isRetained": {"type": "boolean", "description": "是否保留消息", "nullable": true}, "direction": {"type": "string", "description": "消息方向", "nullable": true}, "senderIpAddress": {"type": "string", "description": "发送者IP地址", "nullable": true}, "messageTags": {"type": "string", "description": "消息标签", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "记录MQTT消息输入"}, "ResetSubscriptionStatisticsInput": {"type": "object", "properties": {"instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "topicName": {"type": "string", "description": "主题名称", "nullable": true}}, "additionalProperties": false, "description": "重置订阅统计信息输入参数"}, "Search": {"type": "object", "properties": {"fields": {"type": "array", "items": {"type": "string"}, "description": "字段名称集合", "nullable": true}, "keyword": {"type": "string", "description": "关键字", "nullable": true}}, "additionalProperties": false, "description": "模糊查询条件"}, "SqlSugarPagedList_MqttClientAuthOutput": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/MqttClientAuthOutput"}, "description": "当前页集合", "nullable": true}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合"}, "SqlSugarPagedList_MqttClientOutput": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/MqttClientOutput"}, "description": "当前页集合", "nullable": true}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合"}, "SqlSugarPagedList_MqttDeviceGroupOutput": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/MqttDeviceGroupOutput"}, "description": "当前页集合", "nullable": true}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合"}, "SqlSugarPagedList_MqttInstanceOutput": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/MqttInstanceOutput"}, "description": "当前页集合", "nullable": true}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合"}, "SqlSugarPagedList_MqttMessageHistoryOutput": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/MqttMessageHistoryOutput"}, "description": "当前页集合", "nullable": true}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合"}, "SqlSugarPagedList_MqttSubscriptionOutput": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/MqttSubscriptionOutput"}, "description": "当前页集合", "nullable": true}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合"}, "SqlSugarPagedList_MqttTopicOutput": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/MqttTopicOutput"}, "description": "当前页集合", "nullable": true}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合"}, "SubscribeTopicInput": {"required": ["clientId", "instanceId", "topicName"], "type": "object", "properties": {"instanceId": {"type": "integer", "description": "实例ID", "format": "int64"}, "clientId": {"maxLength": 128, "minLength": 1, "type": "string", "description": "客户端ID"}, "clientRefId": {"type": "integer", "description": "客户端引用ID", "format": "int64", "nullable": true}, "topicId": {"type": "integer", "description": "主题ID", "format": "int64", "nullable": true}, "topicName": {"maxLength": 255, "minLength": 1, "type": "string", "description": "主题名称"}, "qosLevel": {"maximum": 2, "minimum": 0, "type": "integer", "description": "QoS等级（0、1、2）", "format": "int32"}, "isSharedSubscription": {"type": "boolean", "description": "是否共享订阅"}, "sharedGroupName": {"maxLength": 64, "type": "string", "description": "共享组名", "nullable": true}, "subscriptionOptions": {"type": "string", "description": "订阅选项（JSON）", "nullable": true}, "messageFilter": {"maxLength": 255, "type": "string", "description": "消息过滤器", "nullable": true}, "priority": {"maximum": 10, "minimum": 0, "type": "integer", "description": "优先级", "format": "int32"}, "maxQueueLength": {"type": "integer", "description": "最大队列长度", "format": "int32", "nullable": true}, "handlingStrategy": {"maxLength": 32, "type": "string", "description": "处理策略（FIFO、LIFO、Priority）", "nullable": true}, "maxRetryCount": {"type": "integer", "description": "最大重试次数", "format": "int32", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON）", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "订阅主题参数"}, "SubscriptionOperationOutput": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "description": "操作是否成功"}, "errorMessage": {"type": "string", "description": "错误消息", "nullable": true}, "subscriptionId": {"type": "integer", "description": "订阅ID", "format": "int64", "nullable": true}, "clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "topicName": {"type": "string", "description": "主题名称", "nullable": true}, "qosLevel": {"type": "integer", "description": "QoS等级", "format": "int32", "nullable": true}, "operationTime": {"type": "string", "description": "操作时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "订阅操作结果输出"}, "SubscriptionStatisticsOutput": {"type": "object", "properties": {"totalSubscriptions": {"type": "integer", "description": "总订阅数", "format": "int32"}, "activeSubscriptions": {"type": "integer", "description": "活跃订阅数", "format": "int32"}, "suspendedSubscriptions": {"type": "integer", "description": "暂停订阅数", "format": "int32"}, "sharedSubscriptions": {"type": "integer", "description": "共享订阅数", "format": "int32"}, "todayNewSubscriptions": {"type": "integer", "description": "今日新增订阅数", "format": "int32"}, "todayUnsubscriptions": {"type": "integer", "description": "今日取消订阅数", "format": "int32"}, "qosDistribution": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "description": "QoS分布", "nullable": true}, "statusDistribution": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "description": "状态分布", "nullable": true}, "popularTopics": {"type": "array", "items": {"$ref": "#/components/schemas/TopicSubscriptionCount"}, "description": "热门主题（按订阅数排序）", "nullable": true}, "activeClients": {"type": "array", "items": {"$ref": "#/components/schemas/ClientSubscriptionCount"}, "description": "活跃客户端（按订阅数排序）", "nullable": true}}, "additionalProperties": false, "description": "订阅统计信息输出"}, "TopicStatistics": {"type": "object", "properties": {"topicName": {"type": "string", "description": "主题名称", "nullable": true}, "messageCount": {"type": "integer", "description": "消息数量", "format": "int32"}, "totalSize": {"type": "integer", "description": "总大小", "format": "int64"}}, "additionalProperties": false, "description": "主题统计信息"}, "TopicSubscriberInfo": {"type": "object", "properties": {"subscriptionId": {"type": "integer", "description": "订阅ID", "format": "int64"}, "clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "qosLevel": {"type": "integer", "description": "QoS等级", "format": "int32"}, "status": {"type": "string", "description": "订阅状态", "nullable": true}, "isSharedSubscription": {"type": "boolean", "description": "是否共享订阅"}, "sharedGroupName": {"type": "string", "description": "共享组名", "nullable": true}, "subscribedAt": {"type": "string", "description": "订阅时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "messagesReceived": {"type": "integer", "description": "接收消息数", "format": "int64", "nullable": true}, "currentQueueLength": {"type": "integer", "description": "当前队列长度", "format": "int32", "nullable": true}}, "additionalProperties": false, "description": "主题订阅者信息"}, "TopicSubscribersOutput": {"type": "object", "properties": {"topicName": {"type": "string", "description": "主题名称", "nullable": true}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64"}, "totalSubscribers": {"type": "integer", "description": "总订阅者数", "format": "int32"}, "activeSubscribers": {"type": "integer", "description": "活跃订阅者数", "format": "int32"}, "sharedGroupCount": {"type": "integer", "description": "共享订阅组数", "format": "int32"}, "subscribers": {"type": "array", "items": {"$ref": "#/components/schemas/TopicSubscriberInfo"}, "description": "订阅者列表", "nullable": true}, "statistics": {"$ref": "#/components/schemas/TopicSubscriptionStatistics"}}, "additionalProperties": false, "description": "主题订阅者列表输出"}, "TopicSubscriptionCount": {"type": "object", "properties": {"topicName": {"type": "string", "description": "主题名称", "nullable": true}, "subscriptionCount": {"type": "integer", "description": "订阅数", "format": "int32"}, "activeCount": {"type": "integer", "description": "活跃订阅数", "format": "int32"}}, "additionalProperties": false, "description": "主题订阅数统计"}, "TopicSubscriptionStatistics": {"type": "object", "properties": {"totalMessagesReceived": {"type": "integer", "description": "总接收消息数", "format": "int64"}, "totalBytesReceived": {"type": "integer", "description": "总接收字节数", "format": "int64"}, "totalMessagesDropped": {"type": "integer", "description": "总丢弃消息数", "format": "int64"}, "averageQosLevel": {"type": "number", "description": "平均QoS等级", "format": "double"}, "lastMessageTime": {"type": "string", "description": "最后消息时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "qosDistribution": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "description": "QoS分布", "nullable": true}, "sharedGroupDistribution": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "description": "共享组分布", "nullable": true}}, "additionalProperties": false, "description": "主题订阅统计信息"}, "UnsubscribeTopicInput": {"required": ["clientId", "instanceId", "topicName"], "type": "object", "properties": {"instanceId": {"type": "integer", "description": "实例ID", "format": "int64"}, "clientId": {"maxLength": 128, "minLength": 1, "type": "string", "description": "客户端ID"}, "topicName": {"maxLength": 255, "minLength": 1, "type": "string", "description": "主题名称"}, "unsubscribeReason": {"maxLength": 255, "type": "string", "description": "取消订阅原因", "nullable": true}}, "additionalProperties": false, "description": "取消订阅主题参数"}, "UpdateMessageProcessStatusInput": {"required": ["id", "processStatus"], "type": "object", "properties": {"id": {"type": "integer", "description": "消息ID", "format": "int64"}, "processStatus": {"minLength": 1, "type": "string", "description": "处理状态"}, "errorMessage": {"type": "string", "description": "错误信息", "nullable": true}, "retryCount": {"type": "integer", "description": "重试次数", "format": "int32", "nullable": true}}, "additionalProperties": false, "description": "更新消息处理状态输入"}, "UpdateMqttClientAuthInput": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "clientId": {"maxLength": 128, "type": "string", "description": "客户端ID", "nullable": true}, "clientRefId": {"type": "integer", "description": "客户端引用ID", "format": "int64", "nullable": true}, "authMode": {"maxLength": 32, "type": "string", "description": "认证模式（Username、AliyunSignature、JWT等）", "nullable": true}, "username": {"maxLength": 128, "type": "string", "description": "用户名", "nullable": true}, "password": {"maxLength": 128, "type": "string", "description": "密码", "nullable": true}, "passwordHash": {"maxLength": 256, "type": "string", "description": "密码哈希", "nullable": true}, "passwordSalt": {"maxLength": 64, "type": "string", "description": "密码盐值", "nullable": true}, "accessKeyId": {"maxLength": 64, "type": "string", "description": "AccessKey ID（阿里云认证）", "nullable": true}, "accessKeySecret": {"maxLength": 128, "type": "string", "description": "AccessKey Secret（加密存储）", "nullable": true}, "signMethod": {"maxLength": 32, "type": "string", "description": "签名算法（hmacmd5、hmacsha1、hmacsha256）", "nullable": true}, "jwtSecret": {"maxLength": 256, "type": "string", "description": "JWT密钥", "nullable": true}, "jwtExpiry": {"type": "integer", "description": "JWT过期时间（秒）", "format": "int32", "nullable": true}, "jwtToken": {"maxLength": 255, "type": "string", "description": "JWT令牌", "nullable": true}, "certificate": {"maxLength": 255, "type": "string", "description": "客户端证书", "nullable": true}, "privateKey": {"maxLength": 255, "type": "string", "description": "私钥", "nullable": true}, "caCertificate": {"maxLength": 255, "type": "string", "description": "CA证书", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "allowedIPs": {"maxLength": 255, "type": "string", "description": "允许的IP地址列表", "nullable": true}, "deniedIPs": {"maxLength": 255, "type": "string", "description": "拒绝的IP地址列表", "nullable": true}, "rateLimit": {"type": "integer", "description": "速率限制（消息/秒）", "format": "int32", "nullable": true}, "loginAttempts": {"type": "integer", "description": "登录尝试次数", "format": "int32", "nullable": true}, "expireTime": {"type": "string", "description": "过期时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastAuthTime": {"type": "string", "description": "最后认证时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastLoginTime": {"type": "string", "description": "最后登录时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastLoginIP": {"maxLength": 45, "type": "string", "description": "最后登录IP", "nullable": true}, "successCount": {"type": "integer", "description": "认证成功次数", "format": "int32", "nullable": true}, "authSuccessCount": {"type": "integer", "description": "认证成功总数", "format": "int32", "nullable": true}, "failedCount": {"type": "integer", "description": "认证失败次数", "format": "int32", "nullable": true}, "isLocked": {"type": "boolean", "description": "是否锁定（0:未锁定，1:已锁定）", "nullable": true}, "lockTime": {"type": "string", "description": "锁定时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "unlockTime": {"type": "string", "description": "解锁时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lockReason": {"maxLength": 256, "type": "string", "description": "锁定原因", "nullable": true}, "maxFailedCount": {"type": "integer", "description": "最大失败次数", "format": "int32", "nullable": true}, "lockDuration": {"type": "integer", "description": "锁定持续时间（分钟）", "format": "int32", "nullable": true}, "ipWhitelist": {"maxLength": 255, "type": "string", "description": "IP白名单", "nullable": true}, "ipBlacklist": {"maxLength": 255, "type": "string", "description": "IP黑名单", "nullable": true}, "allowedTopics": {"maxLength": 255, "type": "string", "description": "允许的主题列表", "nullable": true}, "deniedTopics": {"maxLength": 255, "type": "string", "description": "拒绝的主题列表", "nullable": true}, "maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32", "nullable": true}, "currentConnections": {"type": "integer", "description": "当前连接数", "format": "int32", "nullable": true}, "connectionTimeout": {"type": "integer", "description": "连接超时时间（秒）", "format": "int32", "nullable": true}, "maxMessageSize": {"type": "integer", "description": "最大消息大小", "format": "int32", "nullable": true}, "maxSubscriptions": {"type": "integer", "description": "最大订阅数", "format": "int32", "nullable": true}, "messageRateLimit": {"type": "integer", "description": "消息速率限制", "format": "int32", "nullable": true}, "byteRateLimit": {"type": "integer", "description": "字节速率限制", "format": "int32", "nullable": true}, "extendedProperties": {"maxLength": 255, "type": "string", "description": "扩展属性（JSON）", "nullable": true}, "key": {"maxLength": 255, "type": "string", "description": "", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "MQTT 客户端认证信息表更新输入参数"}, "UpdateMqttClientInput": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "clientId": {"maxLength": 128, "type": "string", "description": "客户端ID", "nullable": true}, "deviceName": {"maxLength": 64, "type": "string", "description": "设备名称", "nullable": true}, "groupId": {"maxLength": 64, "type": "string", "description": "设备组ID", "nullable": true}, "productKey": {"maxLength": 64, "type": "string", "description": "产品Key", "nullable": true}, "deviceSecret": {"maxLength": 128, "type": "string", "description": "设备密钥（加密存储）", "nullable": true}, "ipAddress": {"maxLength": 45, "type": "string", "description": "IP地址", "nullable": true}, "port": {"type": "integer", "description": "端口", "format": "int32", "nullable": true}, "status": {"maxLength": 32, "type": "string", "description": "连接状态", "nullable": true}, "protocolVersion": {"maxLength": 16, "type": "string", "description": "协议版本", "nullable": true}, "keepAlive": {"type": "integer", "description": "心跳间隔（秒）", "format": "int32", "nullable": true}, "cleanSession": {"type": "boolean", "description": "清除会话（0:否，1:是）", "nullable": true}, "connectedAt": {"type": "string", "description": "连接时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "disconnectedAt": {"type": "string", "description": "断开时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "messagesSent": {"type": "integer", "description": "发送消息数", "format": "int64", "nullable": true}, "messagesReceived": {"type": "integer", "description": "接收消息数", "format": "int64", "nullable": true}, "bytesSent": {"type": "integer", "description": "发送字节数", "format": "int64", "nullable": true}, "bytesReceived": {"type": "integer", "description": "接收字节数", "format": "int64", "nullable": true}, "subscriptionCount": {"type": "integer", "description": "订阅数量", "format": "int32", "nullable": true}, "maxSubscriptions": {"type": "integer", "description": "最大订阅数", "format": "int32", "nullable": true}, "deviceType": {"maxLength": 32, "type": "string", "description": "设备类型", "nullable": true}, "deviceVersion": {"maxLength": 32, "type": "string", "description": "设备版本", "nullable": true}, "deviceTags": {"maxLength": 255, "type": "string", "description": "设备标签（JSON）", "nullable": true}, "locationInfo": {"maxLength": 255, "type": "string", "description": "位置信息（JSON）", "nullable": true}, "isOnline": {"type": "boolean", "description": "是否在线（0:离线，1:在线）", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "authMode": {"maxLength": 32, "type": "string", "description": "认证模式", "nullable": true}, "clientType": {"maxLength": 32, "type": "string", "description": "客户端类型", "nullable": true}, "lastError": {"maxLength": 500, "type": "string", "description": "最后错误信息", "nullable": true}, "extendedProperties": {"maxLength": 255, "type": "string", "description": "扩展属性（JSON）", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "MQTT 客户端连接信息表更新输入参数"}, "UpdateMqttDeviceGroupInput": {"required": ["accessKeyId", "accessKeySecret", "authMode", "byteRateLimit", "certificateRequired", "connectionTimeout", "createdDeviceCount", "currentDeviceCount", "defaultQosLevel", "enableRetainMessage", "enableSharedSubscription", "enableWildcardSubscription", "encryptionRequired", "groupId", "groupName", "groupType", "id", "instanceId", "isEnabled", "keepAliveTimeout", "maxConnections", "maxDevice<PERSON><PERSON>nt", "maxMessageSize", "messageRateLimit", "onlineDeviceCount", "productKey", "securityLevel", "sign<PERSON>ethod", "totalByteCount", "totalMessageCount"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64"}, "groupId": {"maxLength": 64, "minLength": 1, "type": "string", "description": "设备组标识（如：GID_TestGroup）"}, "groupName": {"maxLength": 128, "minLength": 1, "type": "string", "description": "设备组名称"}, "groupType": {"maxLength": 32, "minLength": 1, "type": "string", "description": "设备组类型（Production、Test、Development等）"}, "productKey": {"maxLength": 64, "minLength": 1, "type": "string", "description": "产品Key（阿里云IoT产品标识）"}, "description": {"maxLength": 500, "type": "string", "description": "设备组描述", "nullable": true}, "maxDeviceCount": {"type": "integer", "description": "最大设备数量", "format": "int32"}, "currentDeviceCount": {"type": "integer", "description": "当前设备数量", "format": "int32"}, "allowedTopicPatterns": {"maxLength": 255, "type": "string", "description": "允许的主题模式（JSON数组）", "nullable": true}, "deniedTopicPatterns": {"maxLength": 255, "type": "string", "description": "禁止的主题模式（JSON数组）", "nullable": true}, "defaultQosLevel": {"type": "boolean", "description": "默认QoS等级（0,1,2）"}, "maxMessageSize": {"type": "integer", "description": "最大消息大小（字节）", "format": "int32"}, "messageRateLimit": {"type": "integer", "description": "消息速率限制（消息/秒）", "format": "int32"}, "byteRateLimit": {"type": "integer", "description": "字节速率限制（字节/秒）", "format": "int32"}, "maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32"}, "connectionTimeout": {"type": "integer", "description": "连接超时时间（秒）", "format": "int32"}, "keepAliveTimeout": {"type": "integer", "description": "心跳超时时间（秒）", "format": "int32"}, "enableRetainMessage": {"type": "boolean", "description": "是否允许保留消息"}, "enableWildcardSubscription": {"type": "boolean", "description": "是否允许通配符订阅"}, "enableSharedSubscription": {"type": "boolean", "description": "是否允许共享订阅"}, "ipWhitelist": {"maxLength": 255, "type": "string", "description": "IP白名单（JSON数组）", "nullable": true}, "ipBlacklist": {"maxLength": 255, "type": "string", "description": "IP黑名单（JSON数组）", "nullable": true}, "allowedTimeRanges": {"maxLength": 255, "type": "string", "description": "允许连接的时间范围（JSON）", "nullable": true}, "securityLevel": {"maxLength": 32, "minLength": 1, "type": "string", "description": "安全级别（Low、Medium、High）"}, "encryptionRequired": {"type": "boolean", "description": "是否要求加密连接"}, "certificateRequired": {"type": "boolean", "description": "是否要求客户端证书"}, "authMode": {"maxLength": 32, "minLength": 1, "type": "string", "description": "认证模式（Signature、Username、JWT、Certificate）"}, "signMethod": {"maxLength": 32, "minLength": 1, "type": "string", "description": "签名算法（hmacsha1、hmacsha256、hmacmd5）"}, "accessKeyId": {"maxLength": 64, "minLength": 1, "type": "string", "description": "AccessKey ID（阿里云认证）"}, "accessKeySecret": {"maxLength": 128, "minLength": 1, "type": "string", "description": "AccessKey Secret（加密存储）"}, "jwtSecret": {"maxLength": 256, "type": "string", "description": "JWT密钥", "nullable": true}, "jwtExpiry": {"type": "integer", "description": "JWT过期时间（秒）", "format": "int32", "nullable": true}, "caCertificate": {"maxLength": 255, "type": "string", "description": "CA证书", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用"}, "expireTime": {"type": "string", "description": "过期时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createdDeviceCount": {"type": "integer", "description": "已创建设备数", "format": "int32"}, "onlineDeviceCount": {"type": "integer", "description": "在线设备数", "format": "int32"}, "totalMessageCount": {"type": "integer", "description": "总消息数", "format": "int64"}, "totalByteCount": {"type": "integer", "description": "总字节数", "format": "int64"}, "lastMessageTime": {"type": "string", "description": "最后消息时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "alertRules": {"maxLength": 255, "type": "string", "description": "告警规则（JSON）", "nullable": true}, "monitorConfig": {"maxLength": 255, "type": "string", "description": "监控配置（JSON）", "nullable": true}, "extendedProperties": {"maxLength": 255, "type": "string", "description": "扩展属性（JSON）", "nullable": true}, "tags": {"maxLength": 255, "type": "string", "description": "标签（JSON）", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "MQTT设备组管理表更新输入参数"}, "UpdateMqttInstanceInput": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "instanceName": {"maxLength": 128, "type": "string", "description": "实例名称", "nullable": true}, "instanceCode": {"maxLength": 64, "type": "string", "description": "实例编码", "nullable": true}, "instanceType": {"maxLength": 32, "type": "string", "description": "实例类型（EMQX、阿里云等）", "nullable": true}, "serverHost": {"maxLength": 128, "type": "string", "description": "服务器地址", "nullable": true}, "serverPort": {"type": "integer", "description": "服务器端口", "format": "int32", "nullable": true}, "apiHost": {"maxLength": 128, "type": "string", "description": "API地址", "nullable": true}, "apiPort": {"type": "integer", "description": "API端口", "format": "int32", "nullable": true}, "apiUsername": {"maxLength": 64, "type": "string", "description": "API用户名", "nullable": true}, "apiPassword": {"maxLength": 128, "type": "string", "description": "API密码（加密存储）", "nullable": true}, "status": {"maxLength": 32, "type": "string", "description": "实例状态", "nullable": true}, "enableSsl": {"type": "boolean", "description": "是否启用SSL（0:否，1:是）", "nullable": true}, "sslPort": {"type": "integer", "description": "SSL端口", "format": "int32", "nullable": true}, "enableWebSocket": {"type": "boolean", "description": "是否启用WebSocket（0:否，1:是）", "nullable": true}, "wsPort": {"type": "integer", "description": "WebSocket端口", "format": "int32", "nullable": true}, "wssPort": {"type": "integer", "description": "WebSocket SSL端口", "format": "int32", "nullable": true}, "maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32", "nullable": true}, "currentConnections": {"type": "integer", "description": "当前连接数", "format": "int32", "nullable": true}, "aliyunProductKey": {"maxLength": 64, "type": "string", "description": "阿里云产品Key", "nullable": true}, "aliyunRegionId": {"maxLength": 32, "type": "string", "description": "阿里云区域ID", "nullable": true}, "aliyunInstanceId": {"maxLength": 64, "type": "string", "description": "阿里云实例ID", "nullable": true}, "deviceIdPrefix": {"maxLength": 32, "type": "string", "description": "设备ID前缀", "nullable": true}, "groupIdPrefix": {"maxLength": 32, "type": "string", "description": "组ID前缀", "nullable": true}, "aliyunCompatible": {"type": "boolean", "description": "是否兼容阿里云（0:否，1:是）", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "configJson": {"maxLength": 255, "type": "string", "description": "配置JSON", "nullable": true}, "lastHeartbeat": {"type": "string", "description": "最后心跳时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "totalMessages": {"type": "integer", "description": "总消息数", "format": "int64", "nullable": true}, "totalBytes": {"type": "integer", "description": "总字节数", "format": "int64", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "description": "备注", "nullable": true}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "MQTT 实例信息表更新输入参数"}, "UpdateMqttSubscriptionInput": {"required": ["id"], "type": "object", "properties": {"instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "clientId": {"maxLength": 128, "type": "string", "description": "客户端ID", "nullable": true}, "clientRefId": {"type": "integer", "description": "客户端引用ID", "format": "int64", "nullable": true}, "topicName": {"maxLength": 255, "type": "string", "description": "主题名称", "nullable": true}, "topicId": {"type": "integer", "description": "主题ID", "format": "int64", "nullable": true}, "qosLevel": {"maximum": 2, "minimum": 0, "type": "integer", "description": "QoS等级（0、1、2）", "format": "int32", "nullable": true}, "status": {"maxLength": 32, "type": "string", "description": "订阅状态（Active、Inactive、Suspended）", "nullable": true}, "subscribedAt": {"type": "string", "description": "订阅时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "unsubscribedAt": {"type": "string", "description": "取消订阅时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastActivity": {"type": "string", "description": "最后活动时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "messagesReceived": {"type": "integer", "description": "接收消息数", "format": "int64", "nullable": true}, "bytesReceived": {"type": "integer", "description": "接收字节数", "format": "int64", "nullable": true}, "messagesDropped": {"type": "integer", "description": "丢弃消息数", "format": "int64", "nullable": true}, "lastMessageTime": {"type": "string", "description": "最后消息时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastMessageId": {"maxLength": 64, "type": "string", "description": "最后消息ID", "nullable": true}, "isSharedSubscription": {"type": "boolean", "description": "是否共享订阅", "nullable": true}, "sharedGroupName": {"maxLength": 64, "type": "string", "description": "共享组名", "nullable": true}, "subscriptionOptions": {"type": "string", "description": "订阅选项（JSON）", "nullable": true}, "configJson": {"type": "string", "description": "配置JSON", "nullable": true}, "messageFilter": {"maxLength": 255, "type": "string", "description": "消息过滤器", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用", "nullable": true}, "priority": {"maximum": 10, "minimum": 0, "type": "integer", "description": "优先级", "format": "int32", "nullable": true}, "maxQueueLength": {"type": "integer", "description": "最大队列长度", "format": "int32", "nullable": true}, "currentQueueLength": {"type": "integer", "description": "当前队列长度", "format": "int32", "nullable": true}, "handlingStrategy": {"maxLength": 32, "type": "string", "description": "处理策略（FIFO、LIFO、Priority）", "nullable": true}, "retryCount": {"type": "integer", "description": "重试次数", "format": "int32", "nullable": true}, "maxRetryCount": {"type": "integer", "description": "最大重试次数", "format": "int32", "nullable": true}, "lastError": {"maxLength": 500, "type": "string", "description": "最后错误信息", "nullable": true}, "statistics": {"type": "string", "description": "统计信息（JSON）", "nullable": true}, "extendedProperties": {"type": "string", "description": "扩展属性（JSON）", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "description": "备注", "nullable": true}, "id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "description": "更新MQTT订阅关系表参数"}, "UpdateMqttTopicInput": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "instanceId": {"type": "integer", "description": "实例ID", "format": "int64", "nullable": true}, "topicName": {"maxLength": 256, "type": "string", "description": "主题名称", "nullable": true}, "topicType": {"maxLength": 32, "type": "string", "description": "主题类型", "nullable": true}, "qosLevel": {"type": "boolean", "description": "QoS等级（0,1,2）", "nullable": true}, "retainMessage": {"type": "boolean", "description": "是否保留消息（0:否，1:是）", "nullable": true}, "subscriptionCount": {"type": "integer", "description": "订阅数量", "format": "int32", "nullable": true}, "messageCount": {"type": "integer", "description": "消息数量", "format": "int64", "nullable": true}, "byteCount": {"type": "integer", "description": "字节数量", "format": "int64", "nullable": true}, "lastMessageTime": {"type": "string", "description": "最后消息时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "lastMessageContent": {"maxLength": 255, "type": "string", "description": "最后消息内容", "nullable": true}, "maxMessageSize": {"type": "integer", "description": "最大消息大小", "format": "int32", "nullable": true}, "messageExpiry": {"type": "integer", "description": "消息过期时间（秒）", "format": "int32", "nullable": true}, "allowPublish": {"type": "boolean", "description": "允许发布（0:否，1:是）", "nullable": true}, "allowSubscribe": {"type": "boolean", "description": "允许订阅（0:否，1:是）", "nullable": true}, "publishPermissions": {"maxLength": 255, "type": "string", "description": "发布权限（JSON）", "nullable": true}, "subscribePermissions": {"maxLength": 255, "type": "string", "description": "订阅权限（JSON）", "nullable": true}, "isRetained": {"type": "boolean", "description": "是否保留（0:否，1:是）", "nullable": true}, "description": {"maxLength": 500, "type": "string", "description": "描述", "nullable": true}, "topicTags": {"maxLength": 255, "type": "string", "description": "主题标签（JSON）", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用（0:否，1:是）", "nullable": true}, "isSystemTopic": {"type": "boolean", "description": "是否系统主题（0:否，1:是）", "nullable": true}, "monitorConfig": {"maxLength": 255, "type": "string", "description": "监控配置（JSON）", "nullable": true}, "alertRules": {"maxLength": 255, "type": "string", "description": "告警规则（JSON）", "nullable": true}, "forwardRules": {"maxLength": 255, "type": "string", "description": "转发规则（JSON）", "nullable": true}, "extendedProperties": {"maxLength": 255, "type": "string", "description": "扩展属性（JSON）", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "MQTT 主题信息表更新输入参数"}, "ValidateClientIdInput": {"required": ["clientId"], "type": "object", "properties": {"clientId": {"minLength": 1, "type": "string", "description": "客户端ID"}}, "additionalProperties": false, "description": "验证ClientId输入"}, "ValidateClientIdOutput": {"type": "object", "properties": {"clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "isValid": {"type": "boolean", "description": "是否有效"}, "message": {"type": "string", "description": "消息", "nullable": true}, "validateTime": {"type": "string", "description": "验证时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "验证ClientId输出"}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}], "tags": [{"name": "emqxRealtime", "description": "EMQX实时数据交换控制器 🧩"}, {"name": "mqttClient", "description": "MQTT 客户端连接信息表服务 🧩"}, {"name": "mqttClientAuth", "description": "MQTT 客户端认证信息表服务 🧩"}, {"name": "mqttDeviceGroup", "description": "MQTT设备组管理表服务 🧩"}, {"name": "mqttInstance", "description": "MQTT 实例信息表服务 🧩"}, {"name": "mqttMessageHistory", "description": "MQTT消息历史记录服务 🧩"}, {"name": "mqttSubscription", "description": "MQTT订阅关系服务"}, {"name": "mqttTopic", "description": "MQTT 主题信息表服务 🧩"}]}