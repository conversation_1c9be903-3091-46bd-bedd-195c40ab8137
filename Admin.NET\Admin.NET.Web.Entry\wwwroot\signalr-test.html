<!DOCTYPE html>
<html>
<head>
    <title>SignalR连接测试</title>
    <script src="https://unpkg.com/@microsoft/signalr@latest/dist/browser/signalr.min.js"></script>
</head>
<body>
    <h1>SignalR连接测试</h1>
    <div id="status">未连接</div>
    <button onclick="connect()">连接</button>
    <button onclick="disconnect()">断开连接</button>
    <button onclick="ping()">发送Ping</button>
    <div id="messages"></div>

    <script>
        let connection = null;
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');

        function addMessage(message) {
            const div = document.createElement('div');
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            messagesDiv.appendChild(div);
        }

        async function connect() {
            try {
                connection = new signalR.HubConnectionBuilder()
                    .withUrl("/realtimeSubscriptionHub")
                    .build();

                connection.on("ReceivePong", function (message) {
                    addMessage('收到Pong: ' + message);
                });

                connection.on("ReceiveData", function (data) {
                    addMessage('收到数据: ' + JSON.stringify(data));
                });

                connection.onclose(function () {
                    statusDiv.textContent = '连接已断开';
                    statusDiv.style.color = 'red';
                    addMessage('连接已断开');
                });

                await connection.start();
                statusDiv.textContent = '已连接';
                statusDiv.style.color = 'green';
                addMessage('SignalR连接成功！');
            } catch (err) {
                statusDiv.textContent = '连接失败: ' + err.toString();
                statusDiv.style.color = 'red';
                addMessage('连接失败: ' + err.toString());
            }
        }

        async function disconnect() {
            if (connection) {
                await connection.stop();
                connection = null;
                statusDiv.textContent = '未连接';
                statusDiv.style.color = 'black';
                addMessage('手动断开连接');
            }
        }

        async function ping() {
            if (connection && connection.state === signalR.HubConnectionState.Connected) {
                try {
                    const result = await connection.invoke("Ping", "测试消息");
                    addMessage('Ping发送成功，返回: ' + result);
                } catch (err) {
                    addMessage('Ping发送失败: ' + err.toString());
                }
            } else {
                addMessage('请先连接SignalR');
            }
        }
    </script>
</body>
</html>