{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",

  "JWTSettings": {
    "ValidateIssuerSigningKey": true, // 是否验证密钥，bool 类型，默认true
    "IssuerSigningKey": "3c1cbc3f546eda35168c3aa3cb91780fbe703f0996c6d123ea96dc85c70bbc0a", // 密钥，string 类型，必须是复杂密钥，长度大于16
    "ValidateIssuer": true, // 是否验证签发方，bool 类型，默认true
    "ValidIssuer": "Admin.NET", // 签发方，string 类型
    "ValidateAudience": true, // 是否验证签收方，bool 类型，默认true
    "ValidAudience": "Admin.NET", // 签收方，string 类型
    "ValidateLifetime": true, // 是否验证过期时间，bool 类型，默认true，建议true
    //"ExpiredTime": 20, // 过期时间，long 类型，单位分钟，默认20分钟，最大支持 13 年
    "ClockSkew": 5, // 过期时间容错值，long 类型，单位秒，默认5秒
    "Algorithm": "HS256", // 加密算法，string 类型，默认 HS256
    "RequireExpirationTime": true // 验证过期时间，设置 false 将永不过期
  }
}