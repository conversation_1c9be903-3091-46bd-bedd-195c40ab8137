using Admin.NET.Application.Service.EmqxRealtime.DTOs;
using Admin.NET.Application.Service.EmqxRealtime.Interfaces;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace Admin.NET.Application.Service.EmqxRealtime.Hubs;

/// <summary>
/// 实时订阅SignalR Hub
/// </summary>
public class RealtimeSubscriptionHub : Hub
{
    private readonly ILogger<RealtimeSubscriptionHub> _logger;
    private readonly IRealtimeSubscriptionService _subscriptionService;
    private readonly IDeviceGroupService _deviceGroupService;

    public RealtimeSubscriptionHub(
        ILogger<RealtimeSubscriptionHub> logger,
        IRealtimeSubscriptionService subscriptionService,
        IDeviceGroupService deviceGroupService)
    {
        _logger = logger;
        _subscriptionService = subscriptionService;
        _deviceGroupService = deviceGroupService;
    }

    /// <summary>
    /// 连接建立时调用
    /// </summary>
    /// <returns></returns>
    public override async Task OnConnectedAsync()
    {
        try
        {
            var connectionId = Context.ConnectionId;
            var userId = GetUserId();
            var userName = GetUserName();
            var clientIp = GetClientIp();
            var userAgent = GetUserAgent();

            await _subscriptionService.AddConnectionAsync(connectionId, userId, userName, clientIp, userAgent);

            // 发送连接成功事件
            await Clients.Caller.SendAsync("Connected", new
            {
                connectionId,
                serverTime = DateTime.Now,
                message = "连接成功"
            });

            _logger.LogInformation("新连接建立: {ConnectionId}, 用户: {UserName}, IP: {ClientIp}", 
                connectionId, userName, clientIp);

            await base.OnConnectedAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理连接建立失败: {ConnectionId}", Context.ConnectionId);
            await Clients.Caller.SendAsync("Error", new { message = "连接建立失败", details = ex.Message });
        }
    }

    /// <summary>
    /// 连接断开时调用
    /// </summary>
    /// <param name="exception">异常信息</param>
    /// <returns></returns>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        try
        {
            var connectionId = Context.ConnectionId;
            await _subscriptionService.RemoveConnectionAsync(connectionId);

            _logger.LogInformation("连接已断开: {ConnectionId}, 异常: {Exception}", 
                connectionId, exception?.Message);

            await base.OnDisconnectedAsync(exception);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理连接断开失败: {ConnectionId}", Context.ConnectionId);
        }
    }

    /// <summary>
    /// 订阅主题
    /// </summary>
    /// <param name="request">订阅请求</param>
    /// <returns></returns>
    public async Task Subscribe(SubscriptionRequest request)
    {
        try
        {
            var connectionId = Context.ConnectionId;
            
            // 验证请求参数
            if (request.InstanceId <= 0)
            {
                await Clients.Caller.SendAsync("SubscriptionError", new 
                { 
                    message = "无效的实例ID", 
                    details = "实例ID必须大于0" 
                });
                return;
            }

            if (!request.SubscribeToAllTopics && !request.Topics.Any())
            {
                await Clients.Caller.SendAsync("SubscriptionError", new 
                { 
                    message = "无效的订阅参数", 
                    details = "必须指定主题或启用全主题订阅" 
                });
                return;
            }

            // 执行订阅
            var success = await _subscriptionService.SubscribeAsync(connectionId, request);
            
            if (success)
            {
                // 发送订阅成功事件
                await Clients.Caller.SendAsync("SubscriptionSuccess", new
                {
                    instanceId = request.InstanceId,
                    topics = request.Topics,
                    subscribeToAllTopics = request.SubscribeToAllTopics,
                    deviceGroup = request.DeviceGroup,
                    subscribeTime = DateTime.Now
                });

                // 如果需要历史消息，发送历史消息
                if (request.IncludeHistory && request.MaxHistoryCount > 0)
                {
                    var historyMessages = await _subscriptionService.GetHistoryMessagesAsync(
                        request.InstanceId, request.Topics, request.MaxHistoryCount);
                    
                    if (historyMessages.Any())
                    {
                        await Clients.Caller.SendAsync("HistoryMessages", historyMessages);
                    }
                }

                _logger.LogInformation("订阅成功: {ConnectionId}, 实例: {InstanceId}, 主题: {Topics}", 
                    connectionId, request.InstanceId, string.Join(", ", request.Topics));
            }
            else
            {
                await Clients.Caller.SendAsync("SubscriptionError", new 
                { 
                    message = "订阅失败", 
                    details = "服务器内部错误" 
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "订阅处理失败: {ConnectionId}", Context.ConnectionId);
            await Clients.Caller.SendAsync("SubscriptionError", new 
            { 
                message = "订阅处理失败", 
                details = ex.Message 
            });
        }
    }

    /// <summary>
    /// 订阅设备分组
    /// </summary>
    /// <param name="request">订阅请求</param>
    /// <returns></returns>
    public async Task SubscribeDeviceGroup(SubscriptionRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.DeviceGroup))
            {
                await Clients.Caller.SendAsync("SubscriptionError", new 
                { 
                    message = "无效的设备分组", 
                    details = "设备分组名称不能为空" 
                });
                return;
            }

            // 获取设备分组信息
            var deviceGroup = await _deviceGroupService.GetGroupByNameAsync(request.DeviceGroup);
            if (deviceGroup == null)
            {
                await Clients.Caller.SendAsync("SubscriptionError", new 
                { 
                    message = "设备分组不存在", 
                    details = $"找不到名为 '{request.DeviceGroup}' 的设备分组" 
                });
                return;
            }

            if (!deviceGroup.IsEnabled)
            {
                await Clients.Caller.SendAsync("SubscriptionError", new 
                { 
                    message = "设备分组已禁用", 
                    details = $"设备分组 '{request.DeviceGroup}' 当前已禁用" 
                });
                return;
            }

            // 构建主题列表
            var topics = new List<string>();
            if (!string.IsNullOrEmpty(deviceGroup.TopicPrefix))
            {
                topics.Add($"{deviceGroup.TopicPrefix}/#");
            }
            else
            {
                topics.Add("#"); // 订阅所有主题
            }

            request.Topics = topics;
            await Subscribe(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设备分组订阅处理失败: {ConnectionId}, 分组: {DeviceGroup}", 
                Context.ConnectionId, request.DeviceGroup);
            await Clients.Caller.SendAsync("SubscriptionError", new 
            { 
                message = "设备分组订阅失败", 
                details = ex.Message 
            });
        }
    }

    /// <summary>
    /// 取消订阅
    /// </summary>
    /// <returns></returns>
    public async Task Unsubscribe()
    {
        try
        {
            var connectionId = Context.ConnectionId;
            var success = await _subscriptionService.UnsubscribeAsync(connectionId);
            
            if (success)
            {
                await Clients.Caller.SendAsync("UnsubscribeSuccess", new
                {
                    connectionId,
                    unsubscribeTime = DateTime.Now
                });
                
                _logger.LogInformation("取消订阅成功: {ConnectionId}", connectionId);
            }
            else
            {
                await Clients.Caller.SendAsync("Error", new 
                { 
                    message = "取消订阅失败" 
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消订阅处理失败: {ConnectionId}", Context.ConnectionId);
            await Clients.Caller.SendAsync("Error", new 
            { 
                message = "取消订阅处理失败", 
                details = ex.Message 
            });
        }
    }

    /// <summary>
    /// 获取订阅信息
    /// </summary>
    /// <returns></returns>
    public async Task GetSubscriptionInfo()
    {
        try
        {
            var connectionId = Context.ConnectionId;
            var subscriptionInfo = await _subscriptionService.GetSubscriptionInfoAsync(connectionId);
            
            await Clients.Caller.SendAsync("SubscriptionInfo", subscriptionInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取订阅信息失败: {ConnectionId}", Context.ConnectionId);
            await Clients.Caller.SendAsync("Error", new 
            { 
                message = "获取订阅信息失败", 
                details = ex.Message 
            });
        }
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    /// <returns></returns>
    public async Task GetStatistics()
    {
        try
        {
            var statistics = await _subscriptionService.GetStatisticsAsync();
            await Clients.Caller.SendAsync("Statistics", statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取统计信息失败: {ConnectionId}", Context.ConnectionId);
            await Clients.Caller.SendAsync("Error", new 
            { 
                message = "获取统计信息失败", 
                details = ex.Message 
            });
        }
    }

    /// <summary>
    /// 心跳检测
    /// </summary>
    /// <returns></returns>
    public async Task Ping()
    {
        try
        {
            await Clients.Caller.SendAsync("Pong", new
            {
                connectionId = Context.ConnectionId,
                serverTime = DateTime.Now
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "心跳检测失败: {ConnectionId}", Context.ConnectionId);
        }
    }

    #region 私有方法

    private long? GetUserId()
    {
        var userIdClaim = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return long.TryParse(userIdClaim, out var userId) ? userId : null;
    }

    private string? GetUserName()
    {
        return Context.User?.FindFirst(ClaimTypes.Name)?.Value;
    }

    private string? GetClientIp()
    {
        return Context.GetHttpContext()?.Connection?.RemoteIpAddress?.ToString();
    }

    private string? GetUserAgent()
    {
        return Context.GetHttpContext()?.Request?.Headers["User-Agent"].FirstOrDefault();
    }

    #endregion
}