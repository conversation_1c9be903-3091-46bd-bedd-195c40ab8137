using Admin.NET.Application.Service.EmqxRealtime.DTOs;
using Admin.NET.Application.Entity;

namespace Admin.NET.Application.Service.EmqxRealtime.Interfaces;

/// <summary>
/// 设备分组服务接口
/// </summary>
public interface IDeviceGroupService
{
    /// <summary>
    /// 获取所有设备分组
    /// </summary>
    /// <returns></returns>
    Task<List<DeviceGroupDto>> GetAllGroupsAsync();

    /// <summary>
    /// 根据ID获取设备分组
    /// </summary>
    /// <param name="id">分组ID</param>
    /// <returns></returns>
    Task<DeviceGroupDto?> GetGroupByIdAsync(long id);

    /// <summary>
    /// 根据名称获取设备分组
    /// </summary>
    /// <param name="name">分组名称</param>
    /// <returns></returns>
    Task<DeviceGroupDto?> GetGroupByNameAsync(string name);

    /// <summary>
    /// 创建设备分组
    /// </summary>
    /// <param name="dto">设备分组DTO</param>
    /// <returns></returns>
    Task<long> CreateGroupAsync(DeviceGroupDto dto);

    /// <summary>
    /// 更新设备分组
    /// </summary>
    /// <param name="id">分组ID</param>
    /// <param name="dto">设备分组DTO</param>
    /// <returns></returns>
    Task<bool> UpdateGroupAsync(long id, DeviceGroupDto dto);

    /// <summary>
    /// 删除设备分组
    /// </summary>
    /// <param name="id">分组ID</param>
    /// <returns></returns>
    Task<bool> DeleteGroupAsync(long id);

    /// <summary>
    /// 根据设备ID自动匹配分组
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns></returns>
    Task<DeviceGroupDto?> MatchGroupByDeviceIdAsync(string deviceId);

    /// <summary>
    /// 根据主题自动匹配分组
    /// </summary>
    /// <param name="topic">主题</param>
    /// <returns></returns>
    Task<DeviceGroupDto?> MatchGroupByTopicAsync(string topic);

    /// <summary>
    /// 获取分组下的设备列表
    /// </summary>
    /// <param name="groupId">分组ID</param>
    /// <returns></returns>
    Task<List<string>> GetDevicesInGroupAsync(long groupId);

    /// <summary>
    /// 向分组添加设备
    /// </summary>
    /// <param name="groupId">分组ID</param>
    /// <param name="deviceIds">设备ID列表</param>
    /// <returns></returns>
    Task<bool> AddDevicesToGroupAsync(long groupId, List<string> deviceIds);

    /// <summary>
    /// 从分组移除设备
    /// </summary>
    /// <param name="groupId">分组ID</param>
    /// <param name="deviceIds">设备ID列表</param>
    /// <returns></returns>
    Task<bool> RemoveDevicesFromGroupAsync(long groupId, List<string> deviceIds);
}