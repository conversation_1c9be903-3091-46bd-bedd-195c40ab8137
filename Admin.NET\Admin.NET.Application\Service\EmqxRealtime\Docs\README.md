# MQTT 实时订阅服务

![.NET](https://img.shields.io/badge/.NET-6.0%2B-blueviolet) ![SignalR](https://img.shields.io/badge/SignalR-realtime-brightgreen) ![License](https://img.shields.io/badge/license-MIT-blue)

## 📖 概述

本项目是一个功能完整的 **MQTT 实时订阅服务**，基于 **SignalR** 构建，专为需要实时获取 MQTT 数据的前端和客户端应用设计。它将后端 MQTT Broker 的消息实时、高效地推送给订阅者，并提供历史消息回放、状态监控等高级功能。

## 🏗️ 系统架构

服务采用清晰的分层架构，确保了高效的实时通信和良好的可扩展性。

```mermaid
graph TD
    subgraph "客户端层 (Client Layer)"
        A[Web 前端]
        B[移动应用]
        C[桌面应用]
    end

    subgraph "实时订阅服务 (Realtime Subscription Service)"
        D[SignalR Hub]
        E[RESTful API]
        F[设备分组管理器]
        G[消息处理器]
    end

    subgraph "缓存层 (Cache Layer)"
        H[(Redis 缓存)]
        I[内存缓存]
    end

    subgraph "数据持久化 (Data Persistence)"
        J[(MySQL 数据库)]
        K[历史数据聚合器]
    end

    subgraph "MQTT 基础设施 (MQTT Infrastructure)"
        L(EMQX Broker)
        M[设备连接池]
    end

    A & B & C -.->|WebSocket/SignalR| D
    A & B & C -.->|HTTP/HTTPS| E
    D <--> G
    E <--> F
    G <--> H
    G <--> I
    F --> H
    H -.->|定期聚合| K
    K --> J
    G <-->|MQTT 协议| L
    L <--> M
```

## ✨ 主要功能

| 功能分类 | 具体功能 |
| :--- | :--- |
| **核心订阅** | ✅ **实时消息订阅**: 支持订阅指定 MQTT 实例的特定主题。 |
| | ✅ **多主题订阅**: 支持使用通配符 (`+`, `#`) 同时订阅多个主题。 |
| | ✅ **全主题订阅**: 一键订阅指定实例下的所有主题消息。 |
| **消息处理** | ✅ **历史消息回放**: 获取连接前的历史消息，确保消息连续性。 |
| | ✅ **智能消息缓存**: 在内存中缓存最新消息，加速历史消息的获取。 |
| | ✅ **设备ID自动分组**: 根据设备标识自动归类到指定分组，简化多设备管理。 |
| **数据持久化** | ✅ **Redis缓存**: 将实时缓存保存至 Redis，减少 MySQL 访问。 |
| | ✅ **历史数据聚合**: 定期批量写入 MySQL，提升写入效率。 |
| **连接管理** | ✅ **多客户端支持**: 允许多个客户端同时连接和订阅。 |
| | ✅ **自动重连**: SignalR 客户端支持自动断线重连。 |
| **监控与统计** | ✅ **实时状态统计**: 提供连接数、活跃实例、消息吞吐量等关键指标。 |

## 🛠️ 技术栈

- **后端框架**: ASP.NET Core
- **实时通信**: SignalR
- **数据库**: Entity Framework Core, SQLSugar
- **MQTT 客户端**: MQTTnet
- **缓存**: MemoryCache, Redis (StackExchange.Redis)

## 📁 项目结构

```
EmqxRealtime/
├── Services/                           # 核心服务层
│   ├── RealtimeSubscriptionService.cs  # 实时订阅服务
│   ├── MqttMessageHistoryService.cs    # 消息历史服务
│   ├── DeviceGroupService.cs           # 设备分组服务
│   ├── RedisCache Service.cs           # Redis 缓存服务
│   └── DataAggregationService.cs       # 数据聚合服务
├── Interfaces/                         # 服务接口
│   ├── IRealtimeSubscriptionService.cs
│   ├── IDeviceGroupService.cs
│   ├── IRedisCacheService.cs
│   └── IDataAggregationService.cs
├── Hubs/                               # SignalR Hub
│   └── RealtimeSubscriptionHub.cs
├── Controllers/                        # RESTful API 控制器
│   ├── RealtimeSubscriptionController.cs
│   └── DeviceGroupController.cs
├── DTOs/                               # 数据传输对象
│   ├── SubscriptionRequest.cs
│   ├── DeviceGroupDto.cs
│   └── MessageDto.cs
├── Models/                             # 数据模型
│   ├── DeviceGroup.cs
│   ├── MqttMessage.cs
│   └── SubscriptionInfo.cs
├── Jobs/                               # 后台任务
│   └── DataAggregationJob.cs
├── Configuration/                      # 配置文件
│   ├── RedisOptions.cs
│   └── MqttOptions.cs
└── Docs/                               # 文档
    └── README.md
```

## ⚙️ 环境准备

### 基础环境
1.  **.NET SDK**: .NET 6.0 或更高版本
2.  **IDE**: Visual Studio 2022 或 JetBrains Rider
3.  **Git**: 用于版本控制

### 基础设施服务
1.  **MySQL 数据库**: 5.7+ 或 8.0+
    - 用于存储历史消息和配置信息
    - 建议配置主从复制以提高可用性

2.  **Redis 缓存服务器**: 6.0+
    - 用于实时消息缓存和会话管理
    - 建议配置集群模式以支持高并发

3.  **MQTT Broker**: EMQX 5.0+ (推荐) 或 Mosquitto
    - 支持 WebSocket 和标准 MQTT 协议
    - 建议启用认证和 SSL/TLS 加密

### 配置要求
- **内存**: 最低 4GB，推荐 8GB+
- **CPU**: 最低 2 核，推荐 4 核+
- **存储**: SSD 硬盘，至少 20GB 可用空间
- **网络**: 稳定的网络连接，支持 WebSocket

## 🚀 快速开始

1.  **克隆项目**
    ```bash
    git clone <your-repo-url>
    cd Admin.NET
    ```

2.  **配置 `appsettings.json`**
    ```json
    {
      "ConnectionStrings": {
        "DefaultConnection": "Server=localhost;Database=MqttRealtime;Uid=root;Pwd=password;",
        "Redis": "localhost:6379"
      },
      "MqttOptions": {
        "Server": "localhost",
        "Port": 1883,
        "Username": "admin",
        "Password": "password",
        "ClientId": "RealtimeService"
      },
      "RedisOptions": {
        "ConnectionString": "localhost:6379",
        "Database": 0,
        "KeyPrefix": "mqtt:realtime:"
      },
      "DataAggregation": {
        "BatchSize": 1000,
        "FlushIntervalSeconds": 300
      }
    }
    ```

3.  **启动服务器**
    ```bash
    dotnet run
    ```

4.  **连接客户端**
    使用前端示例页面或您自己的客户端连接到 SignalR Hub (`/realtimeSubscriptionHub`)。

## 📡 API 接口

### SignalR Hub (`/realtimeSubscriptionHub`)

#### 客户端调用方法 (Client -> Server)

| 方法名 | 参数 (JSON) | 描述 |
| :--- | :--- | :--- |
| `Subscribe` | `{ instanceId, topics, subscribeToAllTopics, includeHistory, maxHistoryCount }` | 订阅指定主题。 |
| `Unsubscribe` | (无) | 取消当前连接的所有订阅。 |
| `GetSubscriptionInfo` | (无) | 获取当前订阅的详细信息。 |
| `GetStatistics` | (无) | 获取服务的实时统计数据。 |
| `Ping` | (无) | 心跳检测，用于保持连接活跃。 |

#### 服务器推送事件 (Server -> Client)

| 事件名 | 数据 (JSON) | 描述 |
| :--- | :--- | :--- |
| `Connected` | `{ connectionId, serverTime }` | 成功连接到 Hub。 |
| `SubscriptionSuccess` | `{ instanceId, topics }` | 订阅成功。 |
| `SubscriptionError` | `{ message, details }` | 订阅失败。 |
| `RealtimeMessage` | `{ topic, payload, timestamp, instanceId }` | 收到实时 MQTT 消息。 |
| `HistoryMessages` | `[ { topic, payload, ... } ]` | 推送历史消息数组。 |
| `Statistics` | `{ activeConnections, ... }` | 推送实时统计数据。 |
| `Error` | `{ message, details }` | 通用错误通知。 |

### REST API

| 方法 | 路径 | 描述 |
| :--- | :--- | :--- |
| `GET` | `/api/realtimeSubscription/statistics` | 获取服务的全局统计信息。 |
| `GET` | `/api/realtimeSubscription/instances` | 获取所有可用的 MQTT 实例列表。 |
| `GET` | `/api/realtimeSubscription/topics/suggestions` | 根据实例ID获取主题建议。 |
| `GET` | `/api/realtimeSubscription/templates` | 获取订阅请求的模板。 |
| `GET` | `/api/deviceGroup` | 获取所有设备分组列表。 |
| `POST` | `/api/deviceGroup` | 创建新的设备分组。 |
| `PUT` | `/api/deviceGroup/{id}` | 更新指定设备分组。 |
| `DELETE` | `/api/deviceGroup/{id}` | 删除指定设备分组。 |
| `GET` | `/api/deviceGroup/{id}/devices` | 获取分组下的设备列表。 |
| `POST` | `/api/deviceGroup/{id}/devices` | 向分组添加设备。 |

## 💡 使用示例

### JavaScript 客户端

```javascript
import * as signalR from "@microsoft/signalr";

class MqttRealtimeClient {
    constructor(hubUrl) {
        this.connection = new signalR.HubConnectionBuilder()
            .withUrl(hubUrl)
            .withAutomaticReconnect()
            .configureLogging(signalR.LogLevel.Information)
            .build();

        this.setupEventHandlers();
    }

    setupEventHandlers() {
        this.connection.on('RealtimeMessage', (message) => {
            console.log('新消息:', message);
            // 在此更新你的 UI
        });

        this.connection.on('SubscriptionSuccess', (data) => {
            console.log('订阅成功:', data);
        });
    }

    async connect() {
        try {
            await this.connection.start();
            console.log('SignalR Connected.');
        } catch (err) {
            console.error('Connection failed: ', err);
        }
    }

    async subscribe(instanceId, topics) {
        const request = {
            instanceId: instanceId,
            topics: topics,
            subscribeToAllTopics: false,
            includeHistory: true,
            maxHistoryCount: 50
        };
        await this.connection.invoke('Subscribe', request);
    }
}

// --- 使用示例 ---
const client = new MqttRealtimeClient('/realtimeSubscriptionHub');
await client.connect();
if (client.connection.state === signalR.HubConnectionState.Connected) {
    // 订阅特定主题
    await client.subscribe(1, ['device/+/data', 'sensor/+/temperature']);
    
    // 订阅设备分组
    await client.subscribeDeviceGroup(1, 'factory-floor-sensors');
}
```

### C# 客户端

```csharp
using Microsoft.AspNetCore.SignalR.Client;

public class MqttRealtimeClient
{
    private HubConnection _connection;

    public async Task ConnectAsync(string hubUrl)
    {
        _connection = new HubConnectionBuilder()
            .WithUrl(hubUrl)
            .WithAutomaticReconnect()
            .Build();

        _connection.On<object>("RealtimeMessage", message => {
            Console.WriteLine($"收到消息: {message}");
        });

        await _connection.StartAsync();
    }

    public async Task SubscribeAsync(int instanceId, List<string> topics)
    {
        var request = new { instanceId, topics, includeHistory = true };
        await _connection.InvokeAsync("Subscribe", request);
    }

    public async Task SubscribeDeviceGroupAsync(int instanceId, string groupName)
    {
        var request = new { instanceId, deviceGroup = groupName, includeHistory = true };
        await _connection.InvokeAsync("SubscribeDeviceGroup", request);
    }
}
```

## 🔍 监控与调试

- **日志记录**: 在 `appsettings.json` 中调整 `Logging.LogLevel` 以获取更详细的 SignalR 和服务日志。
- **性能计数器**: 服务内置了关键指标的实时统计，可通过 `GetStatistics` 方法或 REST API 获取。
- **浏览器开发者工具**: 在网络(Network)选项卡中监控 WebSocket 流量，检查连接状态和消息帧。
- **Redis 监控**: 使用 Redis CLI 或 RedisInsight 监控缓存使用情况和性能指标。
- **数据库监控**: 监控 MySQL 的连接数、查询性能和存储使用情况。

## 🚀 性能优化

### 缓存策略
- **Redis 集群**: 在高并发场景下，建议使用 Redis 集群模式
- **缓存过期**: 合理设置缓存过期时间，避免内存溢出
- **批量操作**: 使用 Redis Pipeline 进行批量读写操作

### 数据库优化
- **索引优化**: 为常用查询字段创建合适的索引
- **分表分库**: 当数据量过大时，考虑按时间或设备ID进行分表
- **读写分离**: 使用主从复制，读操作分散到从库

### 消息处理优化
- **异步处理**: 使用异步方法处理消息，避免阻塞
- **消息队列**: 在高吞吐场景下，考虑引入消息队列进行削峰填谷
- **连接池**: 合理配置 MQTT 客户端连接池大小

## 🏗️ 部署建议

### Docker 部署
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:6.0
WORKDIR /app
COPY . .
EXPOSE 80
ENTRYPOINT ["dotnet", "Admin.NET.Web.Entry.dll"]
```

### 负载均衡
- 使用 Nginx 或 HAProxy 进行负载均衡
- 配置 SignalR 的 Sticky Sessions
- 使用 Redis 作为 SignalR 的 Backplane

### 高可用部署
- 多实例部署，避免单点故障
- 数据库主从复制或集群部署
- Redis 哨兵模式或集群模式

## 🤝 贡献指南

我们欢迎任何形式的贡献！

1.  Fork 本项目。
2.  创建您的功能分支 (`git checkout -b feature/YourAmazingFeature`)。
3.  提交您的更改 (`git commit -m 'Add some AmazingFeature'`)。
4.  推送到分支 (`git push origin feature/YourAmazingFeature`)。
5.  提交 Pull Request。

## 📄 许可证

本项目采用 [MIT](LICENSE) 许可证。