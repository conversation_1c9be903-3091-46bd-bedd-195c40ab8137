{"GlobalPropertiesHash": "9Ho/ilev7QGHmrVF6Ca6dw2qJFlYM3Hjutj7UEU6ENM=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["E6qD5HHl+DH1lpGvOECdMAbgQtNSd2+EOfwllXYod/M=", "6RU7FMfWJSjWLfNUkno+m62rz1QqyEBIF4A8vQc57ag=", "7s2npnYGacfwJxk43TVZ5u6KwaQqGhnPQ2q3QqHnmGE=", "bV7Pzscdck1Gzonoxd1+DtrikycLNPCgdR0EyB2hN0c=", "ZJxKmbcMjFEgYAcsYu9fXaQf6+sKtL9pSryZmPRoSwg=", "xt0YOJ1cNXHxfzG8tGD+WvEU33Lim7WWBMNg3NMqri0=", "bZrsmf5ipLsw7WoJtA59/zTHGr6euJJoPq3XQtz2574=", "/uCmKqMtJGNanC2pSHQsp6hZFttBSF/ekzJ2HDLMzAE=", "D/3V14np5mbcGd7f4DU4ErsAGhjxze/ayIj2BaB4s6k=", "EnC08zYSyIkUdwWngUMPLz+p+J3auHIlk979CHn7xFs=", "aCyPgKg77MbnmJsM+9q7BRPIAMHsD795V8wA7NTbWKU=", "CC15WhWHvu+KuxtKVndjsjD6I9x35Ioi+HPlaeTJxGU=", "xey8dRXn+idAuniB063J4e9Py22omDwcbJqi4g1PAT0=", "XuFGUUojk18Cw38CpeCW+MZBXG2bN3ASUcjwjVyORnE=", "8Qi/5UYhVqs63gpE3RbDks0jHBpO8/dEQLR20rdVBsc=", "FVmsDGapL9snSiF53OncHDuVcaYGMEaT/uzEAZzCVtI=", "mwdhvkROX3YjJ5x/YGneFZjKeZYN+XhOaMVte4QOLvs=", "AXQ6HnYF566+cdAIG8LsjqfWY9TqPHQ6AKeLsCd8sk4=", "vMrYxUAXAX4V1rmP9Ku3kA7t6D8fAdWr/CwkAqK5amw=", "qU9EolNxQHosTOcfY976N+JKZdVCHzCWfbfpEvih32w=", "qNqgTM4WJim4adqD2zB3/bQzjOw7hu3tTJ8Wo7b4pHA="], "CachedAssets": {"E6qD5HHl+DH1lpGvOECdMAbgQtNSd2+EOfwllXYod/M=": {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\images\\logo.png", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "images/logo#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3fy106m53y", "Integrity": "gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\logo.png", "FileLength": 8024, "LastWriteTime": "2025-08-04T07:42:01.8443988+00:00"}, "7s2npnYGacfwJxk43TVZ5u6KwaQqGhnPQ2q3QqHnmGE=": {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\api.ts.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/api.ts#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7xhqoadx3i", "Integrity": "O20JdNr7irQ0VBm6Gx0733b+cGL2pPNPopmb0gHMMsc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\api.ts.vm", "FileLength": 1947, "LastWriteTime": "2025-08-04T07:42:02.4812955+00:00"}, "bV7Pzscdck1Gzonoxd1+DtrikycLNPCgdR0EyB2hN0c=": {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\data.data.ts.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/data.data.ts#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1ls0y0ljjz", "Integrity": "tQIyjWgbguBvPICyajItesFiG9LOiOkctu+bfNpxa3E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\data.data.ts.vm", "FileLength": 4782, "LastWriteTime": "2025-08-04T07:42:02.4822978+00:00"}, "ZJxKmbcMjFEgYAcsYu9fXaQf6+sKtL9pSryZmPRoSwg=": {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\dataModal.vue.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/dataModal.vue#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "js73fnatoh", "Integrity": "M1VXyaFY9uYEVbKzX00ZKbwmC//cSoBmXFRli9uRjeE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\dataModal.vue.vm", "FileLength": 2607, "LastWriteTime": "2025-08-04T07:42:02.4822978+00:00"}, "xt0YOJ1cNXHxfzG8tGD+WvEU33Lim7WWBMNg3NMqri0=": {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Dto.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/Dto.cs#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o6ujo32hiy", "Integrity": "cHywhBuRRTLnl3VxKeyVH2uWeIBPHaJpLogvcm5l+us=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\Dto.cs.vm", "FileLength": 1149, "LastWriteTime": "2025-08-04T07:42:01.8453985+00:00"}, "bZrsmf5ipLsw7WoJtA59/zTHGr6euJJoPq3XQtz2574=": {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\editDialog.vue.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/editDialog.vue#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4q9uqurpsl", "Integrity": "vA7a3gJ5X75eafIDqgLZq00QBkvlKNUWA8r/Zhm/SGQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\editDialog.vue.vm", "FileLength": 8301, "LastWriteTime": "2025-08-04T07:42:02.4836287+00:00"}, "/uCmKqMtJGNanC2pSHQsp6hZFttBSF/ekzJ2HDLMzAE=": {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Entity.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/Entity.cs#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rytambt81f", "Integrity": "M2g02yCDi6ZIWCXEH0PiCc7TswLvXXZXmSVet03Ys0U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\Entity.cs.vm", "FileLength": 1944, "LastWriteTime": "2025-08-04T07:42:01.8453985+00:00"}, "D/3V14np5mbcGd7f4DU4ErsAGhjxze/ayIj2BaB4s6k=": {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\index.vue.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/index.vue#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "w0skud15m3", "Integrity": "4Ezik7dAEHUFzJp+pUhCaLS52kTNrC4y10Jk2MYX9Ig=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\index.vue.vm", "FileLength": 18294, "LastWriteTime": "2025-08-04T07:42:02.4836287+00:00"}, "EnC08zYSyIkUdwWngUMPLz+p+J3auHIlk979CHn7xFs=": {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Input.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/Input.cs#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nago0bencd", "Integrity": "geueeXb2OfaZ3pGxdpESDplZblZwHdReJ+ud6bOWT68=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\Input.cs.vm", "FileLength": 7236, "LastWriteTime": "2025-08-04T07:42:01.8453985+00:00"}, "aCyPgKg77MbnmJsM+9q7BRPIAMHsD795V8wA7NTbWKU=": {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Output.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/Output.cs#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "t5kbegls1c", "Integrity": "0Zh1wgX4oeQvYoG9a8vc5BwDzoSln6XeSSVHntj0w/A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\Output.cs.vm", "FileLength": 2453, "LastWriteTime": "2025-08-04T07:42:01.8453985+00:00"}, "CC15WhWHvu+KuxtKVndjsjD6I9x35Ioi+HPlaeTJxGU=": {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\SeedData.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/SeedData.cs#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "us9yshxka1", "Integrity": "4gmpwHIGRA3OXE7RGaIKLOHJnJxIPC/B8EHYXQLrdQ8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\SeedData.cs.vm", "FileLength": 1149, "LastWriteTime": "2025-08-04T07:42:01.8453985+00:00"}, "xey8dRXn+idAuniB063J4e9Py22omDwcbJqi4g1PAT0=": {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Service.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/Service.cs#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4lrwgxpcio", "Integrity": "WOh559bzcZKpxasrX1fjZqARWH5Spa34aK2JgE3Dch4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\Service.cs.vm", "FileLength": 21326, "LastWriteTime": "2025-08-04T07:42:01.8463986+00:00"}, "XuFGUUojk18Cw38CpeCW+MZBXG2bN3ASUcjwjVyORnE=": {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\upload\\logo.png", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "upload/logo#[.{fingerprint}]?.png", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3fy106m53y", "Integrity": "gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "wwwroot\\upload\\logo.png", "FileLength": 8024, "LastWriteTime": "2025-08-04T07:42:02.4849851+00:00"}, "6RU7FMfWJSjWLfNUkno+m62rz1QqyEBIF4A8vQc57ag=": {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\signalr-test.html", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "signalr-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "28wfgm604s", "Integrity": "HieyRWT9Nj/LheCHDxL1k6a7CMum8YqDO+MUfj4dMqI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\signalr-test.html", "FileLength": 2883, "LastWriteTime": "2025-08-05T07:16:41.0585191+00:00"}}, "CachedCopyCandidates": {}}