using Admin.NET.Application.Service.EmqxRealtime.DTOs;
using Admin.NET.Application.Service.EmqxRealtime.Interfaces;
using Admin.NET.Application.Entity;
using Admin.NET.Core;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System.Text.RegularExpressions;
using System.Collections.Concurrent;

namespace Admin.NET.Application.Service.EmqxRealtime.Services;

/// <summary>
/// 设备分组服务实现
/// </summary>
public class DeviceGroupService : IDeviceGroupService, ITransient
{
    private readonly ISqlSugarClient _db;
    private readonly ILogger<DeviceGroupService> _logger;
    private readonly IRedisCacheService _redisCacheService;
    
    // 缓存编译后的正则表达式
    private readonly ConcurrentDictionary<long, Regex> _compiledPatterns = new();
    
    // 缓存设备分组信息
    private readonly ConcurrentDictionary<long, DeviceGroupDto> _groupCache = new();
    private DateTime _lastCacheUpdate = DateTime.MinValue;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5);

    public DeviceGroupService(
        ISqlSugarClient db,
        ILogger<DeviceGroupService> logger,
        IRedisCacheService redisCacheService)
    {
        _db = db;
        _logger = logger;
        _redisCacheService = redisCacheService;
    }

    public async Task<List<DeviceGroupDto>> GetAllGroupsAsync()
    {
        try
        {
            await RefreshCacheIfNeededAsync();
            return _groupCache.Values.OrderBy(g => g.Sort).ThenBy(g => g.CreateTime).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有设备分组失败");
            return new List<DeviceGroupDto>();
        }
    }

    public async Task<DeviceGroupDto?> GetGroupByIdAsync(long id)
    {
        try
        {
            await RefreshCacheIfNeededAsync();
            _groupCache.TryGetValue(id, out var group);
            return group;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据ID获取设备分组失败: {Id}", id);
            return null;
        }
    }

    public async Task<DeviceGroupDto?> GetGroupByNameAsync(string name)
    {
        try
        {
            await RefreshCacheIfNeededAsync();
            return _groupCache.Values.FirstOrDefault(g => g.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据名称获取设备分组失败: {Name}", name);
            return null;
        }
    }

    public async Task<long> CreateGroupAsync(DeviceGroupDto dto)
    {
        try
        {
            var entity = new MqttDeviceGroup
            {
                GroupName = dto.Name,
                Description = dto.Description,
                DeviceIdPattern = dto.DeviceIdPattern,
                TopicPrefix = dto.TopicPrefix,
                IsEnabled = dto.IsEnabled,
                Sort = dto.Sort > 0 ? dto.Sort : await GetNextSortAsync(),
                CreateTime = DateTime.Now
            };

            var result = await _db.Insertable(entity).ExecuteReturnSnowflakeIdAsync();
            
            // 清除缓存
            await ClearCacheAsync();
            
            _logger.LogInformation("创建设备分组成功: {Name}, ID: {Id}", dto.Name, result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建设备分组失败: {Name}", dto.Name);
            throw;
        }
    }

    public async Task<bool> UpdateGroupAsync(long id, DeviceGroupDto dto)
    {
        try
        {
            var entity = await _db.Queryable<MqttDeviceGroup>().FirstAsync(g => g.Id == id);
            if (entity == null)
            {
                return false;
            }

            entity.GroupName = dto.Name;
            entity.Description = dto.Description;
            entity.DeviceIdPattern = dto.DeviceIdPattern;
            entity.TopicPrefix = dto.TopicPrefix;
            entity.IsEnabled = dto.IsEnabled;
            entity.Sort = dto.Sort;
            entity.UpdateTime = DateTime.Now;

            var result = await _db.Updateable(entity).ExecuteCommandHasChangeAsync();
            
            // 清除缓存
            await ClearCacheAsync();
            
            _logger.LogInformation("更新设备分组成功: {Name}, ID: {Id}", dto.Name, id);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新设备分组失败: ID={Id}", id);
            return false;
        }
    }

    public async Task<bool> DeleteGroupAsync(long id)
    {
        try
        {
            var result = await _db.Deleteable<MqttDeviceGroup>().Where(g => g.Id == id).ExecuteCommandHasChangeAsync();
            
            // 清除缓存
            await ClearCacheAsync();
            
            _logger.LogInformation("删除设备分组成功: ID={Id}", id);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除设备分组失败: ID={Id}", id);
            return false;
        }
    }

    public async Task<DeviceGroupDto?> MatchGroupByDeviceIdAsync(string deviceId)
    {
        try
        {
            if (string.IsNullOrEmpty(deviceId))
                return null;

            await RefreshCacheIfNeededAsync();

            foreach (var group in _groupCache.Values.Where(g => g.IsEnabled && !string.IsNullOrEmpty(g.DeviceIdPattern)))
            {
                if (_compiledPatterns.TryGetValue(group.Id, out var regex))
                {
                    if (regex.IsMatch(deviceId))
                    {
                        _logger.LogDebug("设备ID {DeviceId} 匹配到分组: {GroupName}", deviceId, group.Name);
                        return group;
                    }
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据设备ID匹配分组失败: {DeviceId}", deviceId);
            return null;
        }
    }

    public async Task<DeviceGroupDto?> MatchGroupByTopicAsync(string topic)
    {
        try
        {
            if (string.IsNullOrEmpty(topic))
                return null;

            await RefreshCacheIfNeededAsync();

            foreach (var group in _groupCache.Values.Where(g => g.IsEnabled && !string.IsNullOrEmpty(g.TopicPrefix)))
            {
                if (topic.StartsWith(group.TopicPrefix!, StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogDebug("主题 {Topic} 匹配到分组: {GroupName}", topic, group.Name);
                    return group;
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据主题匹配分组失败: {Topic}", topic);
            return null;
        }
    }

    public async Task<List<string>> GetDevicesInGroupAsync(long groupId)
    {
        try
        {
            // 从Redis缓存获取设备列表
            var cacheKey = $"device_group:{groupId}:devices";
            var cachedDevices = await _redisCacheService.GetStatisticsAsync<List<string>>(cacheKey);
            if (cachedDevices != null)
            {
                return cachedDevices;
            }

            // 从数据库查询（这里需要根据实际的设备表结构调整）
            // 暂时返回空列表，实际项目中需要实现设备表的查询
            var devices = new List<string>();
            
            // 缓存结果
            await _redisCacheService.CacheStatisticsAsync(cacheKey, devices, 300); // 5分钟过期
            
            return devices;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取分组设备列表失败: GroupId={GroupId}", groupId);
            return new List<string>();
        }
    }

    public async Task<bool> AddDevicesToGroupAsync(long groupId, List<string> deviceIds)
    {
        try
        {
            // 这里需要根据实际的设备表结构实现
            // 暂时只清除缓存
            var cacheKey = $"device_group:{groupId}:devices";
            await _redisCacheService.DeleteAsync(cacheKey);
            
            _logger.LogInformation("向分组 {GroupId} 添加设备: {DeviceIds}", groupId, string.Join(", ", deviceIds));
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "向分组添加设备失败: GroupId={GroupId}", groupId);
            return false;
        }
    }

    public async Task<bool> RemoveDevicesFromGroupAsync(long groupId, List<string> deviceIds)
    {
        try
        {
            // 这里需要根据实际的设备表结构实现
            // 暂时只清除缓存
            var cacheKey = $"device_group:{groupId}:devices";
            await _redisCacheService.DeleteAsync(cacheKey);
            
            _logger.LogInformation("从分组 {GroupId} 移除设备: {DeviceIds}", groupId, string.Join(", ", deviceIds));
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从分组移除设备失败: GroupId={GroupId}", groupId);
            return false;
        }
    }

    private async Task RefreshCacheIfNeededAsync()
    {
        if (DateTime.Now - _lastCacheUpdate > _cacheExpiry)
        {
            await RefreshCacheAsync();
        }
    }

    private async Task RefreshCacheAsync()
    {
        try
        {
            var groups = await _db.Queryable<MqttDeviceGroup>()
                .OrderBy(g => g.Sort)
                .ThenBy(g => g.CreateTime)
                .ToListAsync();

            _groupCache.Clear();
            _compiledPatterns.Clear();

            foreach (var group in groups)
            {
                var dto = new DeviceGroupDto
                {
                    Id = group.Id,
                    Name = group.GroupName,
                    Description = group.Description,
                    DeviceIdPattern = group.DeviceIdPattern,
                    TopicPrefix = group.TopicPrefix,
                    IsEnabled = group.IsEnabled,
                    Sort = group.Sort,
                    CreateTime = group.CreateTime,
                    UpdateTime = group.UpdateTime
                };

                _groupCache.TryAdd(group.Id, dto);

                // 编译正则表达式
                if (!string.IsNullOrEmpty(group.DeviceIdPattern))
                {
                    try
                    {
                        var regex = new Regex(group.DeviceIdPattern, RegexOptions.Compiled | RegexOptions.IgnoreCase);
                        _compiledPatterns.TryAdd(group.Id, regex);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "编译设备ID模式失败: GroupId={GroupId}, Pattern={Pattern}", 
                            group.Id, group.DeviceIdPattern);
                    }
                }
            }

            _lastCacheUpdate = DateTime.Now;
            _logger.LogDebug("设备分组缓存已刷新，共 {Count} 个分组", groups.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新设备分组缓存失败");
        }
    }

    private async Task<int> GetNextSortAsync()
    {
        try
        {
            var maxSort = await _db.Queryable<MqttDeviceGroup>().MaxAsync(g => g.Sort);
            return maxSort + 1;
        }
        catch
        {
            return 1;
        }
    }

    private async Task ClearCacheAsync()
    {
        _groupCache.Clear();
        _compiledPatterns.Clear();
        _lastCacheUpdate = DateTime.MinValue;
        await Task.CompletedTask;
    }


}