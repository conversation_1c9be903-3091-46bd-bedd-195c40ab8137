{"openapi": "3.0.4", "info": {"title": "审批流程", "description": "对业务实体数据的增删改操作进行流程审批。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>", "version": "1.0.0"}, "paths": {"/api/approvalFlow/detail": {"get": {"tags": ["approvalFlow"], "summary": "获取审批流", "operationId": "api-approvalFlow-detail-Get", "parameters": [{"name": "Id", "in": "query", "description": "主键Id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_ApprovalFlow"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_ApprovalFlow"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_ApprovalFlow"}}}}}}}, "/api/approvalFlow/info": {"get": {"tags": ["approvalFlow"], "summary": "根据编码获取审批流信息", "operationId": "api-approvalFlow-info-Get", "parameters": [{"name": "code", "in": "query", "description": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_ApprovalFlow"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_ApprovalFlow"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_ApprovalFlow"}}}}}}}, "/api/approvalFlow/list": {"get": {"tags": ["approvalFlow"], "summary": "获取审批流列表", "operationId": "api-approvalFlow-list-Get", "parameters": [{"name": "Code", "in": "query", "description": "编号", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "description": "名称", "schema": {"type": "string"}}, {"name": "Remark", "in": "query", "description": "备注", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "description": "当前页码", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "description": "页码容量", "schema": {"type": "integer", "format": "int32"}}, {"name": "Field", "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "Order", "in": "query", "description": "排序方向", "schema": {"type": "string"}}, {"name": "DescStr", "in": "query", "description": "降序排序", "schema": {"type": "string"}}, {"name": "Search.Fields", "in": "query", "description": "字段名称集合", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Search.Keyword", "in": "query", "description": "关键字", "schema": {"type": "string"}}, {"name": "Keyword", "in": "query", "description": "模糊查询关键字", "schema": {"type": "string"}}, {"name": "Filter.Logic", "in": "query", "description": "过滤条件", "schema": {"$ref": "#/components/schemas/FilterLogicEnum"}}, {"name": "Filter.Filters", "in": "query", "description": "筛选过滤条件子项", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}}, {"name": "Filter.Field", "in": "query", "description": "字段名称", "schema": {"type": "string"}}, {"name": "Filter.Operator", "in": "query", "description": "逻辑运算符", "schema": {"$ref": "#/components/schemas/FilterOperatorEnum"}}, {"name": "Filter.Value", "in": "query", "description": "字段值", "schema": {"additionalProperties": false}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_List_ApprovalFlowOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_List_ApprovalFlowOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_List_ApprovalFlowOutput"}}}}}}}, "/api/approvalFlow/page": {"post": {"tags": ["approvalFlow"], "summary": "分页查询审批流", "operationId": "api-approvalFlow-page-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ApprovalFlowInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApprovalFlowInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApprovalFlowInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApprovalFlowInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/ApprovalFlowInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_ApprovalFlowOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_ApprovalFlowOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_SqlSugarPagedList_ApprovalFlowOutput"}}}}}}}, "/api/approvalFlow/add": {"post": {"tags": ["approvalFlow"], "summary": "增加审批流", "operationId": "api-approvalFlow-add-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AddApprovalFlowInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddApprovalFlowInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddApprovalFlowInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddApprovalFlowInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/AddApprovalFlowInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Int64"}}}}}}}, "/api/approvalFlow/update": {"post": {"tags": ["approvalFlow"], "summary": "更新审批流", "operationId": "api-approvalFlow-update-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateApprovalFlowInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateApprovalFlowInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateApprovalFlowInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateApprovalFlowInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/UpdateApprovalFlowInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/approvalFlow/delete": {"post": {"tags": ["approvalFlow"], "summary": "删除审批流", "operationId": "api-approvalFlow-delete-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DeleteApprovalFlowInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeleteApprovalFlowInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteApprovalFlowInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteApprovalFlowInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/DeleteApprovalFlowInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/approvalFlow/flowList": {"get": {"tags": ["approvalFlow"], "summary": "获取审批流结构", "operationId": "api-approvalFlow-flowList-Get", "parameters": [{"name": "code", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_Object"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Object"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_Object"}}}}}}}, "/api/approvalFlow/formRoutes": {"get": {"tags": ["approvalFlow"], "summary": "获取审批流规则", "operationId": "api-approvalFlow-formRoutes-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_List_String"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_List_String"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_List_String"}}}}}}}}, "components": {"schemas": {"AddApprovalFlowInput": {"required": ["isDelete"], "type": "object", "properties": {"code": {"type": "string", "description": "编号", "nullable": true}, "name": {"type": "string", "description": "名称", "nullable": true}, "formJson": {"type": "string", "description": "表单", "nullable": true}, "flowJson": {"type": "string", "description": "流程", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "createUserName": {"type": "string", "description": "创建者姓名", "nullable": true}, "updateUserId": {"type": "integer", "description": "修改者Id", "format": "int64", "nullable": true}, "updateUserName": {"type": "string", "description": "修改者姓名", "nullable": true}, "createOrgId": {"type": "integer", "description": "创建者部门Id", "format": "int64", "nullable": true}, "createOrgName": {"type": "string", "description": "创建者部门名称", "nullable": true}, "isDelete": {"type": "boolean", "description": "软删除"}}, "additionalProperties": false, "description": "审批流增加输入参数"}, "AdminResult_ApprovalFlow": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/ApprovalFlow"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_Int64": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"type": "integer", "description": "数据", "format": "int64"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_List_ApprovalFlowOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/ApprovalFlowOutput"}, "description": "数据", "nullable": true}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_List_String": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"type": "array", "items": {"type": "string"}, "description": "数据", "nullable": true}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_Object": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"additionalProperties": false, "description": "数据", "nullable": true}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "AdminResult_SqlSugarPagedList_ApprovalFlowOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/SqlSugarPagedList_ApprovalFlowOutput"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "ApprovalFlow": {"type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "example": "2025-08-05 14:17:44"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "createUserName": {"type": "string", "description": "创建者姓名", "nullable": true}, "updateUserId": {"type": "integer", "description": "修改者Id", "format": "int64", "nullable": true}, "updateUserName": {"type": "string", "description": "修改者姓名", "nullable": true}, "isDelete": {"type": "boolean", "description": "软删除"}, "orgId": {"type": "integer", "description": "机构Id", "format": "int64"}, "code": {"maxLength": 32, "type": "string", "description": "编号", "nullable": true}, "name": {"maxLength": 32, "type": "string", "description": "名称", "nullable": true}, "formJson": {"type": "string", "description": "表单", "nullable": true}, "flowJson": {"type": "string", "description": "流程", "nullable": true}, "status": {"type": "integer", "description": "状态", "format": "int32", "nullable": true}, "remark": {"maxLength": 256, "type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "审批流程信息表"}, "ApprovalFlowInput": {"type": "object", "properties": {"search": {"$ref": "#/components/schemas/Search"}, "keyword": {"type": "string", "description": "模糊查询关键字", "nullable": true}, "filter": {"$ref": "#/components/schemas/Filter"}, "page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": "string", "description": "排序字段", "nullable": true}, "order": {"type": "string", "description": "排序方向", "nullable": true}, "descStr": {"type": "string", "description": "降序排序", "nullable": true}, "code": {"type": "string", "description": "编号", "nullable": true}, "name": {"type": "string", "description": "名称", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "审批流分页查询输入参数"}, "ApprovalFlowOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "code": {"type": "string", "description": "编号", "nullable": true}, "name": {"type": "string", "description": "名称", "nullable": true}, "formJson": {"type": "string", "description": "表单", "nullable": true}, "flowJson": {"type": "string", "description": "流程", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "createUserName": {"type": "string", "description": "创建者姓名", "nullable": true}, "updateUserId": {"type": "integer", "description": "修改者Id", "format": "int64", "nullable": true}, "updateUserName": {"type": "string", "description": "修改者姓名", "nullable": true}, "createOrgId": {"type": "integer", "description": "创建者部门Id", "format": "int64", "nullable": true}, "createOrgName": {"type": "string", "description": "创建者部门名称", "nullable": true}, "isDelete": {"type": "boolean", "description": "软删除"}}, "additionalProperties": false, "description": "审批流输出参数"}, "DeleteApprovalFlowInput": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "description": "审批流删除输入参数"}, "Filter": {"type": "object", "properties": {"logic": {"$ref": "#/components/schemas/FilterLogicEnum"}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}, "description": "筛选过滤条件子项", "nullable": true}, "field": {"type": "string", "description": "字段名称", "nullable": true}, "operator": {"$ref": "#/components/schemas/FilterOperatorEnum"}, "value": {"additionalProperties": false, "description": "字段值", "nullable": true}}, "additionalProperties": false, "description": "筛选过滤条件"}, "FilterLogicEnum": {"enum": [0, 1, 2], "type": "integer", "description": "过滤条件<br />&nbsp;并且 And = 0<br />&nbsp;或者 Or = 1<br />&nbsp;异或 Xor = 2<br />", "format": "int32"}, "FilterOperatorEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8], "type": "integer", "description": "过滤逻辑运算符<br />&nbsp;等于 EQ = 0<br />&nbsp;不等于 NEQ = 1<br />&nbsp;小于 LT = 2<br />&nbsp;小于等于 LTE = 3<br />&nbsp;大于 GT = 4<br />&nbsp;大于等于 GTE = 5<br />&nbsp;开始包含 StartsWith = 6<br />&nbsp;末尾包含 EndsWith = 7<br />&nbsp;包含 Contains = 8<br />", "format": "int32"}, "Search": {"type": "object", "properties": {"fields": {"type": "array", "items": {"type": "string"}, "description": "字段名称集合", "nullable": true}, "keyword": {"type": "string", "description": "关键字", "nullable": true}}, "additionalProperties": false, "description": "模糊查询条件"}, "SqlSugarPagedList_ApprovalFlowOutput": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/ApprovalFlowOutput"}, "description": "当前页集合", "nullable": true}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合"}, "UpdateApprovalFlowInput": {"required": ["id"], "type": "object", "properties": {"code": {"type": "string", "description": "编号", "nullable": true}, "name": {"type": "string", "description": "名称", "nullable": true}, "formJson": {"type": "string", "description": "表单", "nullable": true}, "flowJson": {"type": "string", "description": "流程", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "createUserName": {"type": "string", "description": "创建者姓名", "nullable": true}, "updateUserId": {"type": "integer", "description": "修改者Id", "format": "int64", "nullable": true}, "updateUserName": {"type": "string", "description": "修改者姓名", "nullable": true}, "createOrgId": {"type": "integer", "description": "创建者部门Id", "format": "int64", "nullable": true}, "createOrgName": {"type": "string", "description": "创建者部门名称", "nullable": true}, "isDelete": {"type": "boolean", "description": "软删除"}, "id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "description": "审批流更新输入参数"}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}], "tags": [{"name": "approvalFlow", "description": "审批流程服务"}]}