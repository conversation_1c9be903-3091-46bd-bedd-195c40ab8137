﻿{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",

  // 支付宝支付配置，文档地址：https://openhome.alipay.com/develop/sandbox/app
  "Alipay": {
    "ServerUrl": "https://openapi-sandbox.dl.alipaydev.com/gateway.do", // 支付宝网关地址
    "WebsocketUrl": "openchannel-sandbox.dl.alipaydev.com", // websocket服务地址
    //"AuthUrl": "https://openauth.alipay.com/oauth2/publicAppAuthorize.htm", // 正式环境授权回调地址
    "AuthUrl": "https://openauth-sandbox.dl.alipaydev.com/oauth2/publicAppAuthorize.htm", // 沙箱环境授权回调地址
    "AppAuthUrl": "http://xxxxxxxxxx", // 应用授权回调地址
    "NotifyUrl": "http://xxxxxxxxx/api/sysAlipay/Notify", // 应用网关地址
    "RootCertPath": "Alipaycrt/alipayRootCert.crt", // 支付宝根证书存放路径
    "AccountList": [
      {
        "Name": "sandbox 默认应用",
        "AppId": "xxxxxxxxxxxxxx",
        "SignType": "RSA2",
        "PrivateKey": "xxxxxxxxxxxxxxxxx",
        "EncryptKey": "xxxxxxxxxxxxxxxxxxxx",
        "AppCertPath": "Alipaycrt/appPublicCert.crt", // 应用公钥证书存放路径
        "AlipayPublicCertPath": "Alipaycrt/alipayPublicCert.crt" // 支付宝公钥证书存放路径
      },
      {
        "Name": "sandbox 默认应用2",
        "AppId": "xxxxxxxxxxxxxx",
        "SignType": "RSA2",
        "PrivateKey": "xxxxxxxxxxxxxxxxx",
        "EncryptKey": "xxxxxxxxxxxxxxxxxxxx",
        "AppCertPath": "Alipaycrt/appPublicCert.crt", // 应用公钥证书存放路径
        "AlipayPublicCertPath": "Alipaycrt/alipayPublicCert.crt" // 支付宝公钥证书存放路径
      }
    ]
  }
}