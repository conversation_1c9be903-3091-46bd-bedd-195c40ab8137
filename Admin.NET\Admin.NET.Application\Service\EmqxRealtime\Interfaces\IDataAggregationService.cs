using Admin.NET.Application.Service.EmqxRealtime.DTOs;
using Admin.NET.Application.Entity;

namespace Admin.NET.Application.Service.EmqxRealtime.Interfaces;

/// <summary>
/// 数据聚合服务接口
/// </summary>
public interface IDataAggregationService
{
    /// <summary>
    /// 添加消息到聚合队列
    /// </summary>
    /// <param name="message">消息</param>
    /// <returns></returns>
    Task AddMessageToQueueAsync(MessageDto message);

    /// <summary>
    /// 批量聚合消息到数据库
    /// </summary>
    /// <returns></returns>
    Task<int> FlushMessagesToDatabaseAsync();

    /// <summary>
    /// 获取队列中的消息数量
    /// </summary>
    /// <returns></returns>
    Task<int> GetQueueSizeAsync();

    /// <summary>
    /// 清空聚合队列
    /// </summary>
    /// <returns></returns>
    Task ClearQueueAsync();

    /// <summary>
    /// 获取聚合统计信息
    /// </summary>
    /// <returns></returns>
    Task<object> GetAggregationStatisticsAsync();

    /// <summary>
    /// 启动自动聚合任务
    /// </summary>
    /// <returns></returns>
    Task StartAutoAggregationAsync();

    /// <summary>
    /// 停止自动聚合任务
    /// </summary>
    /// <returns></returns>
    Task StopAutoAggregationAsync();

    /// <summary>
    /// 检查聚合任务状态
    /// </summary>
    /// <returns></returns>
    bool IsAutoAggregationRunning();

    /// <summary>
    /// 手动触发聚合
    /// </summary>
    /// <returns></returns>
    Task<int> TriggerManualAggregationAsync();

    /// <summary>
    /// 获取历史消息
    /// </summary>
    /// <param name="instanceId">实例ID</param>
    /// <param name="topics">主题列表</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="maxCount">最大数量</param>
    /// <returns></returns>
    Task<List<MessageDto>> GetHistoryMessagesAsync(int instanceId, List<string> topics, DateTime? startTime = null, DateTime? endTime = null, int maxCount = 50);

    /// <summary>
    /// 清理过期的历史消息
    /// </summary>
    /// <param name="retentionDays">保留天数</param>
    /// <returns></returns>
    Task<int> CleanupExpiredMessagesAsync(int retentionDays = 30);
}