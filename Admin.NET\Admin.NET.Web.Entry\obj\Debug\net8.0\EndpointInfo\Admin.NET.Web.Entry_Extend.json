{"openapi": "3.0.4", "info": {"title": "Extend", "description": "<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>", "version": "1.0.0"}, "paths": {"/api/Extend/baiDuTranslation/translation": {"get": {"tags": ["baiDuTranslation"], "summary": "百度在线翻译", "description": "源语种和目标语种支持：\r\nzh:简体中文\r\ncht:繁體中文(台灣)\r\nyue:繁體中文(香港)\r\nen:英语\r\nde:德语\r\nspa:西班牙语\r\nfin:芬兰语\r\nfra:法语\r\nit:意大利语\r\njp:日语\r\nkor:韩语\r\nnor:挪威语\r\npl:波兰语\r\npt:葡萄牙语\r\nru:俄语\r\nth:泰语\r\nid:印度尼西亚语\r\nmay:马来西亚\r\nvie:越南语\r\n\r\n更多语种请查看：https://api.fanyi.baidu.com/doc/21", "operationId": "api-Extend-baiDuTranslation-translation-Get", "parameters": [{"name": "from", "in": "query", "description": "翻译源语种", "required": true, "schema": {"type": "string"}}, {"name": "to", "in": "query", "description": "翻译目标语种", "required": true, "schema": {"type": "string"}}, {"name": "content", "in": "query", "description": "文本内容", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminResult_BaiDuTranslationResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminResult_BaiDuTranslationResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminResult_BaiDuTranslationResult"}}}}}}}, "/api/Extend/baiDuTranslation/generatePageI18nFile": {"post": {"tags": ["baiDuTranslation"], "summary": "生成前端页面i18n文件", "operationId": "api-Extend-baiDuTranslation-generatePageI18nFile-Post", "responses": {"200": {"description": "OK"}}}}, "/api/Extend/baiDuTranslation/generateMenuI18nFile": {"post": {"tags": ["baiDuTranslation"], "summary": "生成前端菜单i18n文件", "operationId": "api-Extend-baiDuTranslation-generateMenuI18nFile-Post", "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"AdminResult_BaiDuTranslationResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": "string", "description": "类型success、warning、error", "nullable": true}, "message": {"type": "string", "description": "错误信息", "nullable": true}, "result": {"$ref": "#/components/schemas/BaiDuTranslationResult"}, "extras": {"additionalProperties": false, "description": "附加数据", "nullable": true}, "time": {"type": "string", "description": "时间", "format": "date-time", "example": "2025-08-05 14:17:44"}}, "additionalProperties": false, "description": "全局返回结果"}, "BaiDuTranslationResult": {"type": "object", "properties": {"from": {"type": "string", "description": "源语种", "nullable": true}, "to": {"type": "string", "description": "目标语种", "nullable": true}, "trans_result": {"type": "array", "items": {"$ref": "#/components/schemas/TransResult"}, "description": "翻译结果", "nullable": true}, "error_code": {"type": "string", "description": "错误码 正常为0", "nullable": true}, "error_msg": {"type": "string", "description": "错误信息", "nullable": true}}, "additionalProperties": false, "description": "百度翻译结果"}, "TransResult": {"type": "object", "properties": {"src": {"type": "string", "description": "源字符", "nullable": true}, "dst": {"type": "string", "description": "目标字符", "nullable": true}}, "additionalProperties": false, "description": "翻译结果"}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}], "tags": [{"name": "baiDuTranslation", "description": "百度翻译"}]}