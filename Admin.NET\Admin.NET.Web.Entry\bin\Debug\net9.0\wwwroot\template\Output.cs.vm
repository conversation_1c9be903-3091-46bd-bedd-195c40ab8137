// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！
using Magicodes.ExporterAndImporter.Core;
namespace @(Model.NameSpace);

/// <summary>
/// @(Model.BusName)输出参数
/// </summary>
public class @(Model.ClassName)Output
{
    @foreach (var column in Model.TableField){
    @:/// <summary>
    @:/// @column.ColumnComment
    @:/// </summary>
    @:public @column.NetType @(column.PropertyName) { get; set; }    
    if(column.EffectType == "ForeignKey" || column.EffectType == "ApiTreeSelector") {
    @:
    @:/// <summary>
    @:/// @(column.ColumnComment) 描述
    @:/// </summary>
    @:public string @(column.ExtendedPropertyName) { get; set; } 
    }else if(column.EffectType == "Upload" || column.EffectType == "Upload_SingleFile"){
    @:
    @:/// <summary>
    @:/// @(column.ColumnComment) 附件信息
    @:/// </summary>
    @:public SysFile @(column.ExtendedPropertyName) { get; set; }
    }
    @:
    }
}
@if (Model.ApiTreeFieldList.Count > 0) {
foreach(var column in Model.ApiTreeFieldList) {
@:/// <summary>
@:/// @column.ColumnComment 树选择器输出参数
@:/// </summary>
@:public class Tree@(column.PropertyNameTrimEndId)Output : @(Model.ClassName)
@:{
    @:/// <summary>
    @:/// 节点值
    @:/// </summary>
    @:public @column.NetType Value { get; set; }
    @:
    @:/// <summary>
    @:/// 节点文本
    @:/// </summary>
    @:public string Label { get; set; }
    @:
    @:/// <summary>
    @:/// 子集数据
    @:/// </summary>
    @:public List<Tree@(column.PropertyNameTrimEndId)Output> Children { get; set; }
@:}
@:
}
}
@if (Model.ImportFieldList.Count > 0) {
@:
@:/// <summary>
@:/// @(Model.BusName)数据导入模板实体
@:/// </summary>
@:public class Export@(Model.ClassName)Output : Import@(Model.ClassName)Input
@:{
@:    [ImporterHeader(IsIgnore = true)]
@:    [ExporterHeader(IsIgnore = true)]
@:    public override string Error { get; set; }
@:}
}