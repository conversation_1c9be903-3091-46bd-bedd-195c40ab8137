// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
namespace Admin.NET.Application.Entity;

/// <summary>
/// MQTT设备组管理表
/// </summary>
[Tenant("1300000000001")]
[SugarTable("mqtt_device_group", "MQTT设备组管理表")]
public partial class MqttDeviceGroup : EntityBaseDel
{
    /// <summary>
    /// 实例ID
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "InstanceId", ColumnDescription = "实例ID")]
    public virtual long InstanceId { get; set; }
    
    /// <summary>
    /// 设备组标识（如：GID_TestGroup）
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "GroupId", ColumnDescription = "设备组标识（如：GID_TestGroup）", Length = 64)]
    public virtual string GroupId { get; set; }
    
    /// <summary>
    /// 设备组名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "GroupName", ColumnDescription = "设备组名称", Length = 128)]
    public virtual string GroupName { get; set; }
    
    /// <summary>
    /// 设备组类型（Production、Test、Development等）
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "GroupType", ColumnDescription = "设备组类型（Production、Test、Development等）", Length = 32)]
    public virtual string GroupType { get; set; }
    
    /// <summary>
    /// 产品Key（阿里云IoT产品标识）
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "ProductKey", ColumnDescription = "产品Key（阿里云IoT产品标识）", Length = 64)]
    public virtual string ProductKey { get; set; }
    
    /// <summary>
    /// 设备组描述
    /// </summary>
    [SugarColumn(ColumnName = "Description", ColumnDescription = "设备组描述", Length = 500)]
    public virtual string? Description { get; set; }
    
    /// <summary>
    /// 最大设备数量
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "MaxDeviceCount", ColumnDescription = "最大设备数量")]
    public virtual int MaxDeviceCount { get; set; }
    
    /// <summary>
    /// 当前设备数量
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "CurrentDeviceCount", ColumnDescription = "当前设备数量", DefaultValue = "0")]
    public virtual int CurrentDeviceCount { get; set; }
    
    /// <summary>
    /// 允许的主题模式（JSON数组）
    /// </summary>
    [SugarColumn(ColumnName = "AllowedTopicPatterns", ColumnDescription = "允许的主题模式（JSON数组）", Length = 0)]
    public virtual string? AllowedTopicPatterns { get; set; }
    
    /// <summary>
    /// 禁止的主题模式（JSON数组）
    /// </summary>
    [SugarColumn(ColumnName = "DeniedTopicPatterns", ColumnDescription = "禁止的主题模式（JSON数组）", Length = 0)]
    public virtual string? DeniedTopicPatterns { get; set; }
    
    /// <summary>
    /// 默认QoS等级（0,1,2）
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "DefaultQosLevel", ColumnDescription = "默认QoS等级（0,1,2）", DefaultValue = "0")]
    public virtual bool DefaultQosLevel { get; set; }
    
    /// <summary>
    /// 最大消息大小（字节）
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "MaxMessageSize", ColumnDescription = "最大消息大小（字节）")]
    public virtual int MaxMessageSize { get; set; }
    
    /// <summary>
    /// 消息速率限制（消息/秒）
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "MessageRateLimit", ColumnDescription = "消息速率限制（消息/秒）")]
    public virtual int MessageRateLimit { get; set; }
    
    /// <summary>
    /// 字节速率限制（字节/秒）
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "ByteRateLimit", ColumnDescription = "字节速率限制（字节/秒）")]
    public virtual int ByteRateLimit { get; set; }
    
    /// <summary>
    /// 最大连接数
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "MaxConnections", ColumnDescription = "最大连接数")]
    public virtual int MaxConnections { get; set; }
    
    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "ConnectionTimeout", ColumnDescription = "连接超时时间（秒）")]
    public virtual int ConnectionTimeout { get; set; }
    
    /// <summary>
    /// 心跳超时时间（秒）
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "KeepAliveTimeout", ColumnDescription = "心跳超时时间（秒）")]
    public virtual int KeepAliveTimeout { get; set; }
    
    /// <summary>
    /// 是否允许保留消息
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "EnableRetainMessage", ColumnDescription = "是否允许保留消息", DefaultValue = "0")]
    public virtual bool EnableRetainMessage { get; set; }
    
    /// <summary>
    /// 是否允许通配符订阅
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "EnableWildcardSubscription", ColumnDescription = "是否允许通配符订阅", DefaultValue = "0")]
    public virtual bool EnableWildcardSubscription { get; set; }
    
    /// <summary>
    /// 是否允许共享订阅
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "EnableSharedSubscription", ColumnDescription = "是否允许共享订阅", DefaultValue = "0")]
    public virtual bool EnableSharedSubscription { get; set; }
    
    /// <summary>
    /// IP白名单（JSON数组）
    /// </summary>
    [SugarColumn(ColumnName = "IpWhitelist", ColumnDescription = "IP白名单（JSON数组）", Length = 0)]
    public virtual string? IpWhitelist { get; set; }
    
    /// <summary>
    /// IP黑名单（JSON数组）
    /// </summary>
    [SugarColumn(ColumnName = "IpBlacklist", ColumnDescription = "IP黑名单（JSON数组）", Length = 0)]
    public virtual string? IpBlacklist { get; set; }
    
    /// <summary>
    /// 允许连接的时间范围（JSON）
    /// </summary>
    [SugarColumn(ColumnName = "AllowedTimeRanges", ColumnDescription = "允许连接的时间范围（JSON）", Length = 0)]
    public virtual string? AllowedTimeRanges { get; set; }
    
    /// <summary>
    /// 安全级别（Low、Medium、High）
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "SecurityLevel", ColumnDescription = "安全级别（Low、Medium、High）", Length = 32)]
    public virtual string SecurityLevel { get; set; }
    
    /// <summary>
    /// 是否要求加密连接
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "EncryptionRequired", ColumnDescription = "是否要求加密连接", DefaultValue = "0")]
    public virtual bool EncryptionRequired { get; set; }
    
    /// <summary>
    /// 是否要求客户端证书
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "CertificateRequired", ColumnDescription = "是否要求客户端证书", DefaultValue = "0")]
    public virtual bool CertificateRequired { get; set; }
    
    /// <summary>
    /// 认证模式（Signature、Username、JWT、Certificate）
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "AuthMode", ColumnDescription = "认证模式（Signature、Username、JWT、Certificate）", Length = 32)]
    public virtual string AuthMode { get; set; }
    
    /// <summary>
    /// 签名算法（hmacsha1、hmacsha256、hmacmd5）
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "SignMethod", ColumnDescription = "签名算法（hmacsha1、hmacsha256、hmacmd5）", Length = 32)]
    public virtual string SignMethod { get; set; }
    
    /// <summary>
    /// AccessKey ID（阿里云认证）
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "AccessKeyId", ColumnDescription = "AccessKey ID（阿里云认证）", Length = 64)]
    public virtual string AccessKeyId { get; set; }
    
    /// <summary>
    /// AccessKey Secret（加密存储）
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "AccessKeySecret", ColumnDescription = "AccessKey Secret（加密存储）", Length = 128)]
    public virtual string AccessKeySecret { get; set; }
    
    /// <summary>
    /// JWT密钥
    /// </summary>
    [SugarColumn(ColumnName = "JwtSecret", ColumnDescription = "JWT密钥", Length = 256)]
    public virtual string? JwtSecret { get; set; }
    
    /// <summary>
    /// JWT过期时间（秒）
    /// </summary>
    [SugarColumn(ColumnName = "JwtExpiry", ColumnDescription = "JWT过期时间（秒）")]
    public virtual int? JwtExpiry { get; set; }
    
    /// <summary>
    /// CA证书
    /// </summary>
    [SugarColumn(ColumnName = "CaCertificate", ColumnDescription = "CA证书", Length = 0)]
    public virtual string? CaCertificate { get; set; }
    
    /// <summary>
    /// 是否启用
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "IsEnabled", ColumnDescription = "是否启用", DefaultValue = "1")]
    public virtual bool IsEnabled { get; set; }
    
    /// <summary>
    /// 过期时间
    /// </summary>
    [SugarColumn(ColumnName = "ExpireTime", ColumnDescription = "过期时间")]
    public virtual DateTime? ExpireTime { get; set; }
    
    /// <summary>
    /// 最后活动时间
    /// </summary>
    [SugarColumn(ColumnName = "LastActivity", ColumnDescription = "最后活动时间")]
    public virtual DateTime? LastActivity { get; set; }
    
    /// <summary>
    /// 已创建设备数
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "CreatedDeviceCount", ColumnDescription = "已创建设备数", DefaultValue = "0")]
    public virtual int CreatedDeviceCount { get; set; }
    
    /// <summary>
    /// 在线设备数
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "OnlineDeviceCount", ColumnDescription = "在线设备数", DefaultValue = "0")]
    public virtual int OnlineDeviceCount { get; set; }
    
    /// <summary>
    /// 总消息数
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "TotalMessageCount", ColumnDescription = "总消息数", DefaultValue = "0")]
    public virtual long TotalMessageCount { get; set; }
    
    /// <summary>
    /// 总字节数
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "TotalByteCount", ColumnDescription = "总字节数", DefaultValue = "0")]
    public virtual long TotalByteCount { get; set; }
    
    /// <summary>
    /// 最后消息时间
    /// </summary>
    [SugarColumn(ColumnName = "LastMessageTime", ColumnDescription = "最后消息时间")]
    public virtual DateTime? LastMessageTime { get; set; }
    
    /// <summary>
    /// 告警规则（JSON）
    /// </summary>
    [SugarColumn(ColumnName = "AlertRules", ColumnDescription = "告警规则（JSON）", Length = 0)]
    public virtual string? AlertRules { get; set; }
    
    /// <summary>
    /// 监控配置（JSON）
    /// </summary>
    [SugarColumn(ColumnName = "MonitorConfig", ColumnDescription = "监控配置（JSON）", Length = 0)]
    public virtual string? MonitorConfig { get; set; }
    
    /// <summary>
    /// 扩展属性（JSON）
    /// </summary>
    [SugarColumn(ColumnName = "ExtendedProperties", ColumnDescription = "扩展属性（JSON）", Length = 0)]
    public virtual string? ExtendedProperties { get; set; }
    
    /// <summary>
    /// 标签（JSON）
    /// </summary>
    [SugarColumn(ColumnName = "Tags", ColumnDescription = "标签（JSON）", Length = 0)]
    public virtual string? Tags { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "Remark", ColumnDescription = "备注", Length = 500)]
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "Sort", ColumnDescription = "排序", DefaultValue = "0")]
    public virtual int Sort { get; set; }
    
    /// <summary>
    /// 设备ID模式（用于自动分组）
    /// </summary>
    [SugarColumn(ColumnName = "DeviceIdPattern", ColumnDescription = "设备ID模式（用于自动分组）", Length = 200)]
    public virtual string? DeviceIdPattern { get; set; }
    
    /// <summary>
    /// 主题前缀
    /// </summary>
    [SugarColumn(ColumnName = "TopicPrefix", ColumnDescription = "主题前缀", Length = 200)]
    public virtual string? TopicPrefix { get; set; }
    
}
