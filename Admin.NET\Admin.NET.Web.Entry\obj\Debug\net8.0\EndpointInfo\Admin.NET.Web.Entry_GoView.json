{"openapi": "3.0.4", "info": {"title": "GoView 大屏可视化", "description": "GoView 是一个高效的拖拽式低代码数据可视化开发平台，将图表或页面元素封装为基础组件，无需编写代码即可制作数据大屏，减少心智负担。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>", "version": "2.2.8"}, "paths": {"/api/goview/project/list": {"get": {"tags": ["project"], "summary": "获取项目列表 🔖", "operationId": "api-goview-project-list-Get", "parameters": [{"name": "page", "in": "query", "description": "", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "limit", "in": "query", "description": "", "schema": {"type": "integer", "format": "int32", "default": 12}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GoViewResult_List_GoViewProItemOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GoViewResult_List_GoViewProItemOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoViewResult_List_GoViewProItemOutput"}}}}}}}, "/api/goview/project/edit": {"post": {"tags": ["project"], "summary": "修改项目 🔖", "operationId": "api-goview-project-edit-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GoViewProEditInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GoViewProEditInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoViewProEditInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GoViewProEditInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/GoViewProEditInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/goview/project/publish": {"put": {"tags": ["project"], "summary": "修改发布状态 🔖", "operationId": "api-goview-project-publish-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GoViewProPublishInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GoViewProPublishInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoViewProPublishInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GoViewProPublishInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/GoViewProPublishInput"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/goview/project/upload": {"post": {"tags": ["project"], "summary": "上传预览图 🔖", "operationId": "api-goview-project-upload-Post", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"object": {"type": "string", "format": "binary"}}}, "encoding": {"object": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewProUploadOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewProUploadOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewProUploadOutput"}}}}}}}, "/api/goview/project/uploadBackGround": {"post": {"tags": ["project"], "summary": "上传背景图", "operationId": "api-goview-project-uploadBackGround-Post", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"object": {"type": "string", "format": "binary"}}}, "encoding": {"object": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewProUploadOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewProUploadOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewProUploadOutput"}}}}}}}, "/api/goview/sys/login": {"post": {"tags": ["sys"], "summary": "GoView 登录 🔖", "operationId": "api-goview-sys-login-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GoViewLoginInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GoViewLoginInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoViewLoginInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GoViewLoginInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/GoViewLoginInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewLoginOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewLoginOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewLoginOutput"}}}}}}}, "/api/goview/sys/logout": {"get": {"tags": ["sys"], "summary": "GoView 退出 🔖", "operationId": "api-goview-sys-logout-Get", "responses": {"200": {"description": "OK"}}}}, "/api/goview/project/create": {"post": {"tags": ["project"], "summary": "新增项目 🔖", "operationId": "api-goview-project-create-Post", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GoViewProCreateInput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GoViewProCreateInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoViewProCreateInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GoViewProCreateInput"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/GoViewProCreateInput"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewProCreateOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewProCreateOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewProCreateOutput"}}}}}}}, "/api/goview/project/delete": {"delete": {"tags": ["project"], "summary": "删除项目 🔖", "operationId": "api-goview-project-delete-Delete", "parameters": [{"name": "ids", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/goview/project/getData": {"get": {"tags": ["project"], "summary": "获取项目数据 🔖", "operationId": "api-goview-project-getData-Get", "parameters": [{"name": "projectId", "in": "query", "description": "", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewProDetailOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewProDetailOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewProDetailOutput"}}}}}}}, "/api/goview/project/save/data": {"post": {"tags": ["project"], "summary": "保存项目数据 🔖", "operationId": "api-goview-project-save-data-Post", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"ProjectId": {"type": "integer", "description": "项目Id", "format": "int64"}, "Content": {"type": "string", "description": "项目内容"}}}, "encoding": {"ProjectId": {"style": "form"}, "Content": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/goview/project/getIndexImage/{id}": {"get": {"tags": ["project"], "summary": "获取预览图 🔖", "operationId": "api-goview-project-getIndexImage-id-Get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/goview/project/getBackGroundImage/{id}": {"get": {"tags": ["project"], "summary": "获取背景图", "operationId": "api-goview-project-getBackGroundImage-id-Get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/goview/sys/getOssInfo": {"get": {"tags": ["sys"], "summary": "获取 OSS 上传接口 🔖", "operationId": "api-goview-sys-getOssInfo-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewOssUrlOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewOssUrlOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoViewResult_GoViewOssUrlOutput"}}}}}}}}, "components": {"schemas": {"GoViewLoginInput": {"required": ["password", "username"], "type": "object", "properties": {"username": {"minLength": 1, "type": "string", "description": "用户名"}, "password": {"minLength": 1, "type": "string", "description": "密码"}, "tenantId": {"type": "integer", "description": "租户", "format": "int64", "nullable": true}}, "additionalProperties": false, "description": "登录输入"}, "GoViewLoginOutput": {"type": "object", "properties": {"userinfo": {"$ref": "#/components/schemas/GoViewLoginUserInfo"}, "token": {"$ref": "#/components/schemas/GoViewLoginToken"}}, "additionalProperties": false, "description": "登录输出"}, "GoViewLoginToken": {"type": "object", "properties": {"tokenName": {"type": "string", "description": "Token 名", "nullable": true}, "tokenValue": {"type": "string", "description": "Token 值", "nullable": true}}, "additionalProperties": false, "description": "登录 Token"}, "GoViewLoginUserInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "用户 Id", "nullable": true}, "username": {"type": "string", "description": "用户名", "nullable": true}, "nickname": {"type": "string", "description": "昵称", "nullable": true}}, "additionalProperties": false, "description": "用户信息"}, "GoViewOssUrlOutput": {"type": "object", "properties": {"bucketName": {"type": "string", "description": "桶名", "nullable": true}, "bucketURL": {"type": "string", "description": "BucketURL 地址", "nullable": true}}, "additionalProperties": false, "description": "获取 OSS 上传接口输出"}, "GoViewProCreateInput": {"type": "object", "properties": {"projectName": {"type": "string", "description": "项目名称", "nullable": true}, "remarks": {"type": "string", "description": "项目备注", "nullable": true}, "indexImage": {"type": "string", "description": "预览图片url", "nullable": true}}, "additionalProperties": false, "description": "GoView 新增项目"}, "GoViewProCreateOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "项目Id", "format": "int64"}}, "additionalProperties": false, "description": "GoView 新增项目输出"}, "GoViewProDetailOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "项目Id", "format": "int64"}, "projectName": {"type": "string", "description": "项目名称", "nullable": true}, "stateEnum": {"$ref": "#/components/schemas/GoViewProStateEnum"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "indexImage": {"type": "string", "description": "预览图片url", "nullable": true}, "backGroundImage": {"type": "string", "description": "背景图片url", "nullable": true}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "remarks": {"type": "string", "description": "项目备注", "nullable": true}, "content": {"type": "string", "description": "项目内容", "nullable": true}}, "additionalProperties": false, "description": "GoView 项目详情"}, "GoViewProEditInput": {"type": "object", "properties": {"id": {"type": "integer", "description": "项目Id", "format": "int64"}, "projectName": {"type": "string", "description": "项目名称", "nullable": true}, "indexImage": {"type": "string", "description": "预览图片url", "nullable": true}}, "additionalProperties": false, "description": "GoView 编辑项目"}, "GoViewProItemOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "项目Id", "format": "int64"}, "projectName": {"type": "string", "description": "项目名称", "nullable": true}, "stateEnum": {"$ref": "#/components/schemas/GoViewProStateEnum"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "indexImage": {"type": "string", "description": "预览图片url", "nullable": true}, "backGroundImage": {"type": "string", "description": "背景图片url", "nullable": true}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "remarks": {"type": "string", "description": "项目备注", "nullable": true}}, "additionalProperties": false, "description": "GoView 项目 Item"}, "GoViewProPublishInput": {"type": "object", "properties": {"id": {"type": "integer", "description": "项目Id", "format": "int64"}, "stateEnum": {"$ref": "#/components/schemas/GoViewProStateEnum"}}, "additionalProperties": false, "description": "GoView 修改项目发布状态"}, "GoViewProStateEnum": {"enum": [1, -1], "type": "integer", "description": "GoView 项目状态<br />&nbsp;已发布 Published = 1<br />&nbsp;未发布 UnPublish = -1<br />", "format": "int32"}, "GoViewProUploadOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "Id", "format": "int64"}, "bucketName": {"type": "string", "description": "仓储名称", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "createUserId": {"type": "integer", "description": "创建者Id", "format": "int64", "nullable": true}, "fileName": {"type": "string", "description": "文件名称", "nullable": true}, "fileSize": {"type": "integer", "description": "文件大小KB", "format": "int32"}, "fileSuffix": {"type": "string", "description": "文件后缀", "nullable": true}, "fileUrl": {"type": "string", "description": "文件 Url", "nullable": true}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "nullable": true, "example": "2025-08-05 14:17:44"}, "updateUserId": {"type": "integer", "description": "修改者Id", "format": "int64", "nullable": true}}, "additionalProperties": false, "description": "GoView 上传项目输出"}, "GoViewResult_GoViewLoginOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "msg": {"type": "string", "description": "信息", "nullable": true}, "data": {"$ref": "#/components/schemas/GoViewLoginOutput"}, "count": {"type": "integer", "description": "总数", "format": "int32", "nullable": true}}, "additionalProperties": false, "description": "GoView 返回结果"}, "GoViewResult_GoViewOssUrlOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "msg": {"type": "string", "description": "信息", "nullable": true}, "data": {"$ref": "#/components/schemas/GoViewOssUrlOutput"}, "count": {"type": "integer", "description": "总数", "format": "int32", "nullable": true}}, "additionalProperties": false, "description": "GoView 返回结果"}, "GoViewResult_GoViewProCreateOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "msg": {"type": "string", "description": "信息", "nullable": true}, "data": {"$ref": "#/components/schemas/GoViewProCreateOutput"}, "count": {"type": "integer", "description": "总数", "format": "int32", "nullable": true}}, "additionalProperties": false, "description": "GoView 返回结果"}, "GoViewResult_GoViewProDetailOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "msg": {"type": "string", "description": "信息", "nullable": true}, "data": {"$ref": "#/components/schemas/GoViewProDetailOutput"}, "count": {"type": "integer", "description": "总数", "format": "int32", "nullable": true}}, "additionalProperties": false, "description": "GoView 返回结果"}, "GoViewResult_GoViewProUploadOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "msg": {"type": "string", "description": "信息", "nullable": true}, "data": {"$ref": "#/components/schemas/GoViewProUploadOutput"}, "count": {"type": "integer", "description": "总数", "format": "int32", "nullable": true}}, "additionalProperties": false, "description": "GoView 返回结果"}, "GoViewResult_List_GoViewProItemOutput": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "msg": {"type": "string", "description": "信息", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GoViewProItemOutput"}, "description": "数据", "nullable": true}, "count": {"type": "integer", "description": "总数", "format": "int32", "nullable": true}}, "additionalProperties": false, "description": "GoView 返回结果"}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}], "tags": [{"name": "project", "description": "项目管理服务 🧩"}, {"name": "sys", "description": "系统登录服务 🧩"}]}