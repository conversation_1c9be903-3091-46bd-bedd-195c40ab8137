{"Version": 1, "Hash": "9dTyLdUz9yI3dDmfWl37jm+eUX8G2KXrCURCVGtRgWU=", "Source": "Admin.NET.Web.Entry", "BasePath": "_content/Admin.NET.Web.Entry", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Admin.NET.Web.Entry\\wwwroot", "Source": "Admin.NET.Web.Entry", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "Pattern": "**"}], "Assets": [{"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\images\\logo.png", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "images/logo#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3fy106m53y", "Integrity": "gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\logo.png", "FileLength": 8024, "LastWriteTime": "2025-08-04T07:42:01+00:00"}, {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\api.ts.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/api.ts#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7xhqoadx3i", "Integrity": "O20JdNr7irQ0VBm6Gx0733b+cGL2pPNPopmb0gHMMsc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\api.ts.vm", "FileLength": 1947, "LastWriteTime": "2025-08-04T07:42:02+00:00"}, {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\data.data.ts.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/data.data.ts#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1ls0y0ljjz", "Integrity": "tQIyjWgbguBvPICyajItesFiG9LOiOkctu+bfNpxa3E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\data.data.ts.vm", "FileLength": 4782, "LastWriteTime": "2025-08-04T07:42:02+00:00"}, {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\dataModal.vue.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/dataModal.vue#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "js73fnatoh", "Integrity": "M1VXyaFY9uYEVbKzX00ZKbwmC//cSoBmXFRli9uRjeE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\dataModal.vue.vm", "FileLength": 2607, "LastWriteTime": "2025-08-04T07:42:02+00:00"}, {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Dto.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/Dto.cs#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o6ujo32hiy", "Integrity": "cHywhBuRRTLnl3VxKeyVH2uWeIBPHaJpLogvcm5l+us=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\Dto.cs.vm", "FileLength": 1149, "LastWriteTime": "2025-08-04T07:42:01+00:00"}, {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\editDialog.vue.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/editDialog.vue#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4q9uqurpsl", "Integrity": "vA7a3gJ5X75eafIDqgLZq00QBkvlKNUWA8r/Zhm/SGQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\editDialog.vue.vm", "FileLength": 8301, "LastWriteTime": "2025-08-04T07:42:02+00:00"}, {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Entity.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/Entity.cs#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rytambt81f", "Integrity": "M2g02yCDi6ZIWCXEH0PiCc7TswLvXXZXmSVet03Ys0U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\Entity.cs.vm", "FileLength": 1944, "LastWriteTime": "2025-08-04T07:42:01+00:00"}, {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\index.vue.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/index.vue#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "w0skud15m3", "Integrity": "4Ezik7dAEHUFzJp+pUhCaLS52kTNrC4y10Jk2MYX9Ig=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\index.vue.vm", "FileLength": 18294, "LastWriteTime": "2025-08-04T07:42:02+00:00"}, {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Input.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/Input.cs#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nago0bencd", "Integrity": "geueeXb2OfaZ3pGxdpESDplZblZwHdReJ+ud6bOWT68=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\Input.cs.vm", "FileLength": 7236, "LastWriteTime": "2025-08-04T07:42:01+00:00"}, {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Output.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/Output.cs#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "t5kbegls1c", "Integrity": "0Zh1wgX4oeQvYoG9a8vc5BwDzoSln6XeSSVHntj0w/A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\Output.cs.vm", "FileLength": 2453, "LastWriteTime": "2025-08-04T07:42:01+00:00"}, {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\SeedData.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/SeedData.cs#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "us9yshxka1", "Integrity": "4gmpwHIGRA3OXE7RGaIKLOHJnJxIPC/B8EHYXQLrdQ8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\SeedData.cs.vm", "FileLength": 1149, "LastWriteTime": "2025-08-04T07:42:01+00:00"}, {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Service.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "template/Service.cs#[.{fingerprint}]?.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4lrwgxpcio", "Integrity": "WOh559bzcZKpxasrX1fjZqARWH5Spa34aK2JgE3Dch4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template\\Service.cs.vm", "FileLength": 21326, "LastWriteTime": "2025-08-04T07:42:01+00:00"}, {"Identity": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\upload\\logo.png", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "upload/logo#[.{fingerprint}]?.png", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3fy106m53y", "Integrity": "gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "wwwroot\\upload\\logo.png", "FileLength": 8024, "LastWriteTime": "2025-08-04T07:42:02+00:00"}], "Endpoints": [{"Route": "images/logo.3fy106m53y.png", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\images\\logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8024"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3fy106m53y"}, {"Name": "label", "Value": "images/logo.png"}, {"Name": "integrity", "Value": "sha256-gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0="}]}, {"Route": "images/logo.png", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\images\\logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8024"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0="}]}, {"Route": "template/api.ts.7xhqoadx3i.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\api.ts.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1947"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"O20JdNr7irQ0VBm6Gx0733b+cGL2pPNPopmb0gHMMsc=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7xhqoadx3i"}, {"Name": "label", "Value": "template/api.ts.vm"}, {"Name": "integrity", "Value": "sha256-O20JdNr7irQ0VBm6Gx0733b+cGL2pPNPopmb0gHMMsc="}]}, {"Route": "template/api.ts.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\api.ts.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1947"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"O20JdNr7irQ0VBm6Gx0733b+cGL2pPNPopmb0gHMMsc=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O20JdNr7irQ0VBm6Gx0733b+cGL2pPNPopmb0gHMMsc="}]}, {"Route": "template/data.data.ts.1ls0y0ljjz.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\data.data.ts.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4782"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"tQIyjWgbguBvPICyajItesFiG9LOiOkctu+bfNpxa3E=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1ls0y0ljjz"}, {"Name": "label", "Value": "template/data.data.ts.vm"}, {"Name": "integrity", "Value": "sha256-tQIyjWgbguBvPICyajItesFiG9LOiOkctu+bfNpxa3E="}]}, {"Route": "template/data.data.ts.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\data.data.ts.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4782"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"tQIyjWgbguBvPICyajItesFiG9LOiOkctu+bfNpxa3E=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tQIyjWgbguBvPICyajItesFiG9LOiOkctu+bfNpxa3E="}]}, {"Route": "template/dataModal.vue.js73fnatoh.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\dataModal.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2607"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"M1VXyaFY9uYEVbKzX00ZKbwmC//cSoBmXFRli9uRjeE=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "js73fnatoh"}, {"Name": "label", "Value": "template/dataModal.vue.vm"}, {"Name": "integrity", "Value": "sha256-M1VXyaFY9uYEVbKzX00ZKbwmC//cSoBmXFRli9uRjeE="}]}, {"Route": "template/dataModal.vue.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\dataModal.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2607"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"M1VXyaFY9uYEVbKzX00ZKbwmC//cSoBmXFRli9uRjeE=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M1VXyaFY9uYEVbKzX00ZKbwmC//cSoBmXFRli9uRjeE="}]}, {"Route": "template/Dto.cs.o6ujo32hiy.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Dto.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1149"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"cHywhBuRRTLnl3VxKeyVH2uWeIBPHaJpLogvcm5l+us=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o6ujo32hiy"}, {"Name": "label", "Value": "template/Dto.cs.vm"}, {"Name": "integrity", "Value": "sha256-cHywhBuRRTLnl3VxKeyVH2uWeIBPHaJpLogvcm5l+us="}]}, {"Route": "template/Dto.cs.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Dto.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1149"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"cHywhBuRRTLnl3VxKeyVH2uWeIBPHaJpLogvcm5l+us=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cHywhBuRRTLnl3VxKeyVH2uWeIBPHaJpLogvcm5l+us="}]}, {"Route": "template/editDialog.vue.4q9uqurpsl.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\editDialog.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8301"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"vA7a3gJ5X75eafIDqgLZq00QBkvlKNUWA8r/Zhm/SGQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4q9uqurpsl"}, {"Name": "label", "Value": "template/editDialog.vue.vm"}, {"Name": "integrity", "Value": "sha256-vA7a3gJ5X75eafIDqgLZq00QBkvlKNUWA8r/Zhm/SGQ="}]}, {"Route": "template/editDialog.vue.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\editDialog.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8301"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"vA7a3gJ5X75eafIDqgLZq00QBkvlKNUWA8r/Zhm/SGQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vA7a3gJ5X75eafIDqgLZq00QBkvlKNUWA8r/Zhm/SGQ="}]}, {"Route": "template/Entity.cs.rytambt81f.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Entity.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1944"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"M2g02yCDi6ZIWCXEH0PiCc7TswLvXXZXmSVet03Ys0U=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rytambt81f"}, {"Name": "label", "Value": "template/Entity.cs.vm"}, {"Name": "integrity", "Value": "sha256-M2g02yCDi6ZIWCXEH0PiCc7TswLvXXZXmSVet03Ys0U="}]}, {"Route": "template/Entity.cs.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Entity.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1944"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"M2g02yCDi6ZIWCXEH0PiCc7TswLvXXZXmSVet03Ys0U=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M2g02yCDi6ZIWCXEH0PiCc7TswLvXXZXmSVet03Ys0U="}]}, {"Route": "template/index.vue.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\index.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18294"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"4Ezik7dAEHUFzJp+pUhCaLS52kTNrC4y10Jk2MYX9Ig=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4Ezik7dAEHUFzJp+pUhCaLS52kTNrC4y10Jk2MYX9Ig="}]}, {"Route": "template/index.vue.w0skud15m3.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\index.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18294"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"4Ezik7dAEHUFzJp+pUhCaLS52kTNrC4y10Jk2MYX9Ig=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w0skud15m3"}, {"Name": "label", "Value": "template/index.vue.vm"}, {"Name": "integrity", "Value": "sha256-4Ezik7dAEHUFzJp+pUhCaLS52kTNrC4y10Jk2MYX9Ig="}]}, {"Route": "template/Input.cs.nago0bencd.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Input.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7236"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"geueeXb2OfaZ3pGxdpESDplZblZwHdReJ+ud6bOWT68=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nago0bencd"}, {"Name": "label", "Value": "template/Input.cs.vm"}, {"Name": "integrity", "Value": "sha256-geueeXb2OfaZ3pGxdpESDplZblZwHdReJ+ud6bOWT68="}]}, {"Route": "template/Input.cs.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Input.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7236"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"geueeXb2OfaZ3pGxdpESDplZblZwHdReJ+ud6bOWT68=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geueeXb2OfaZ3pGxdpESDplZblZwHdReJ+ud6bOWT68="}]}, {"Route": "template/Output.cs.t5kbegls1c.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Output.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2453"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0Zh1wgX4oeQvYoG9a8vc5BwDzoSln6XeSSVHntj0w/A=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t5kbegls1c"}, {"Name": "label", "Value": "template/Output.cs.vm"}, {"Name": "integrity", "Value": "sha256-0Zh1wgX4oeQvYoG9a8vc5BwDzoSln6XeSSVHntj0w/A="}]}, {"Route": "template/Output.cs.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Output.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2453"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0Zh1wgX4oeQvYoG9a8vc5BwDzoSln6XeSSVHntj0w/A=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0Zh1wgX4oeQvYoG9a8vc5BwDzoSln6XeSSVHntj0w/A="}]}, {"Route": "template/SeedData.cs.us9yshxka1.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\SeedData.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1149"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"4gmpwHIGRA3OXE7RGaIKLOHJnJxIPC/B8EHYXQLrdQ8=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "us9yshxka1"}, {"Name": "label", "Value": "template/SeedData.cs.vm"}, {"Name": "integrity", "Value": "sha256-4gmpwHIGRA3OXE7RGaIKLOHJnJxIPC/B8EHYXQLrdQ8="}]}, {"Route": "template/SeedData.cs.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\SeedData.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1149"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"4gmpwHIGRA3OXE7RGaIKLOHJnJxIPC/B8EHYXQLrdQ8=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4gmpwHIGRA3OXE7RGaIKLOHJnJxIPC/B8EHYXQLrdQ8="}]}, {"Route": "template/Service.cs.4lrwgxpcio.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21326"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"WOh559bzcZKpxasrX1fjZqARWH5Spa34aK2JgE3Dch4=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4lrwgxpcio"}, {"Name": "label", "Value": "template/Service.cs.vm"}, {"Name": "integrity", "Value": "sha256-WOh559bzcZKpxasrX1fjZqARWH5Spa34aK2JgE3Dch4="}]}, {"Route": "template/Service.cs.vm", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\template\\Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21326"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"WOh559bzcZKpxasrX1fjZqARWH5Spa34aK2JgE3Dch4=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WOh559bzcZKpxasrX1fjZqARWH5Spa34aK2JgE3Dch4="}]}, {"Route": "upload/logo.3fy106m53y.png", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\upload\\logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8024"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3fy106m53y"}, {"Name": "label", "Value": "upload/logo.png"}, {"Name": "integrity", "Value": "sha256-gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0="}]}, {"Route": "upload/logo.png", "AssetFile": "D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\upload\\logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8024"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 07:42:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gSMB1TpeKSOFdJNXbNCm9j/317VSDpfb1LsKu+pisB0="}]}]}