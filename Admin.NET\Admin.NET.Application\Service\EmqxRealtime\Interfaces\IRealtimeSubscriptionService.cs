using Admin.NET.Application.Service.EmqxRealtime.DTOs;
using Admin.NET.Application.Entity;

namespace Admin.NET.Application.Service.EmqxRealtime.Interfaces;

/// <summary>
/// 实时订阅服务接口
/// </summary>
public interface IRealtimeSubscriptionService
{
    /// <summary>
    /// 订阅主题
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="request">订阅请求</param>
    /// <returns></returns>
    Task<bool> SubscribeAsync(string connectionId, SubscriptionRequest request);

    /// <summary>
    /// 取消订阅
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns></returns>
    Task<bool> UnsubscribeAsync(string connectionId);

    /// <summary>
    /// 获取订阅信息
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns></returns>
    Task<SubscriptionInfo?> GetSubscriptionInfoAsync(string connectionId);

    /// <summary>
    /// 获取历史消息
    /// </summary>
    /// <param name="instanceId">实例ID</param>
    /// <param name="topics">主题列表</param>
    /// <param name="maxCount">最大数量</param>
    /// <returns></returns>
    Task<List<MessageDto>> GetHistoryMessagesAsync(int instanceId, List<string> topics, int maxCount = 50);

    /// <summary>
    /// 获取统计信息
    /// </summary>
    /// <returns></returns>
    Task<object> GetStatisticsAsync();

    /// <summary>
    /// 添加连接
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="userId">用户ID</param>
    /// <param name="userName">用户名</param>
    /// <param name="clientIp">客户端IP</param>
    /// <param name="userAgent">用户代理</param>
    /// <returns></returns>
    Task AddConnectionAsync(string connectionId, long? userId = null, string? userName = null, string? clientIp = null, string? userAgent = null);

    /// <summary>
    /// 移除连接
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns></returns>
    Task RemoveConnectionAsync(string connectionId);

    /// <summary>
    /// 处理MQTT消息
    /// </summary>
    /// <param name="message">消息</param>
    /// <returns></returns>
    Task ProcessMqttMessageAsync(MessageDto message);

    /// <summary>
    /// 获取所有活跃连接
    /// </summary>
    /// <returns></returns>
    Task<List<SubscriptionInfo>> GetActiveConnectionsAsync();
}