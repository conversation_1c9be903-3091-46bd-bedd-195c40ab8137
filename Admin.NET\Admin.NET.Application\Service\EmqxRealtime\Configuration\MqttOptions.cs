namespace Admin.NET.Application.Service.EmqxRealtime.Configuration;

/// <summary>
/// MQTT配置选项
/// </summary>
public class MqttOptions
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "MqttOptions";

    /// <summary>
    /// MQTT服务器地址
    /// </summary>
    public string Server { get; set; } = "localhost";

    /// <summary>
    /// MQTT服务器端口
    /// </summary>
    public int Port { get; set; } = 1883;

    /// <summary>
    /// 用户名
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = "RealtimeService";

    /// <summary>
    /// 是否使用SSL
    /// </summary>
    public bool UseSsl { get; set; } = false;

    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    public int ConnectTimeout { get; set; } = 30;

    /// <summary>
    /// 保持连接时间（秒）
    /// </summary>
    public int KeepAlive { get; set; } = 60;

    /// <summary>
    /// 自动重连
    /// </summary>
    public bool AutoReconnect { get; set; } = true;

    /// <summary>
    /// 重连延迟（秒）
    /// </summary>
    public int ReconnectDelay { get; set; } = 5;

    /// <summary>
    /// 最大重连次数
    /// </summary>
    public int MaxReconnectAttempts { get; set; } = 10;

    /// <summary>
    /// 清理会话
    /// </summary>
    public bool CleanSession { get; set; } = true;

    /// <summary>
    /// 默认QoS等级
    /// </summary>
    public int DefaultQoS { get; set; } = 0;
}