using Admin.NET.Application.Service.EmqxRealtime.BackgroundTasks;
using Admin.NET.Application.Service.EmqxRealtime.Configuration;
using Admin.NET.Application.Service.EmqxRealtime.Hubs;
using Admin.NET.Application.Service.EmqxRealtime.Interfaces;
using Admin.NET.Application.Service.EmqxRealtime.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;

namespace Admin.NET.Application.Service.EmqxRealtime.Extensions;

/// <summary>
/// 服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加EMQX实时服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns></returns>
    public static IServiceCollection AddEmqxRealtimeServices(this IServiceCollection services, IConfiguration configuration)
    {
        // 注册配置选项
        services.Configure<MqttOptions>(configuration.GetSection("Mqtt"));
        services.Configure<RedisOptions>(configuration.GetSection("Redis"));

        // 注册Redis连接
        services.AddSingleton<IConnectionMultiplexer>(provider =>
        {
            var redisOptions = configuration.GetSection("Redis").Get<RedisOptions>();
            if (redisOptions == null || string.IsNullOrEmpty(redisOptions.ConnectionString))
            {
                throw new InvalidOperationException("Redis连接字符串未配置");
            }

            var configurationOptions = ConfigurationOptions.Parse(redisOptions.ConnectionString);
            configurationOptions.ConnectTimeout = redisOptions.ConnectTimeout * 1000;
            configurationOptions.SyncTimeout = redisOptions.SyncTimeout * 1000;
            configurationOptions.ConnectRetry = redisOptions.RetryCount;
            configurationOptions.AbortOnConnectFail = false;
            configurationOptions.ReconnectRetryPolicy = new ExponentialRetry(1000);

            return ConnectionMultiplexer.Connect(configurationOptions);
        });

        // 注册Redis数据库
        services.AddScoped<IDatabase>(provider =>
        {
            var redis = provider.GetRequiredService<IConnectionMultiplexer>();
            var redisOptions = configuration.GetSection("Redis").Get<RedisOptions>();
            return redis.GetDatabase(redisOptions?.Database ?? 0);
        });

        // 注册核心服务
        services.AddScoped<IRedisCacheService, RedisCacheService>();
        services.AddScoped<IDeviceGroupService, DeviceGroupService>();
        services.AddScoped<IDataAggregationService, DataAggregationService>();
        services.AddScoped<IRealtimeSubscriptionService, RealtimeSubscriptionService>();

        // 注册后台服务
        services.AddHostedService<MqttMessageProcessor>();
        services.AddHostedService<DataAggregationTask>();

        // 注册SignalR
        services.AddSignalR(options =>
        {
            options.EnableDetailedErrors = true;
            options.KeepAliveInterval = TimeSpan.FromSeconds(15);
            options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
            options.HandshakeTimeout = TimeSpan.FromSeconds(15);
            options.MaximumReceiveMessageSize = 1024 * 1024; // 1MB
        })
        .AddStackExchangeRedis(provider =>
        {
            var redis = provider.GetRequiredService<IConnectionMultiplexer>();
            return redis;
        });

        return services;
    }

    /// <summary>
    /// 添加EMQX实时服务（简化版本，使用默认配置）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="mqttConnectionString">MQTT连接字符串</param>
    /// <param name="redisConnectionString">Redis连接字符串</param>
    /// <returns></returns>
    public static IServiceCollection AddEmqxRealtimeServices(
        this IServiceCollection services, 
        string mqttConnectionString, 
        string redisConnectionString)
    {
        // 解析MQTT连接字符串
        var mqttOptions = ParseMqttConnectionString(mqttConnectionString);
        services.Configure<MqttOptions>(options =>
        {
            options.Server = mqttOptions.Server;
            options.Port = mqttOptions.Port;
            options.Username = mqttOptions.Username;
            options.Password = mqttOptions.Password;
            options.UseSsl = mqttOptions.UseSsl;
        });

        // 配置Redis选项
        services.Configure<RedisOptions>(options =>
        {
            options.ConnectionString = redisConnectionString;
        });

        // 注册Redis连接
        services.AddSingleton<IConnectionMultiplexer>(provider =>
        {
            var configurationOptions = ConfigurationOptions.Parse(redisConnectionString);
            configurationOptions.AbortOnConnectFail = false;
            configurationOptions.ReconnectRetryPolicy = new ExponentialRetry(1000);
            return ConnectionMultiplexer.Connect(configurationOptions);
        });

        // 注册Redis数据库
        services.AddScoped<IDatabase>(provider =>
        {
            var redis = provider.GetRequiredService<IConnectionMultiplexer>();
            return redis.GetDatabase();
        });

        // 注册核心服务
        services.AddScoped<IRedisCacheService, RedisCacheService>();
        services.AddScoped<IDeviceGroupService, DeviceGroupService>();
        services.AddScoped<IDataAggregationService, DataAggregationService>();
        services.AddScoped<IRealtimeSubscriptionService, RealtimeSubscriptionService>();

        // 注册后台服务
        services.AddHostedService<MqttMessageProcessor>();
        services.AddHostedService<DataAggregationTask>();

        // 注册SignalR
        services.AddSignalR(options =>
        {
            options.EnableDetailedErrors = true;
            options.KeepAliveInterval = TimeSpan.FromSeconds(15);
            options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
        })
        .AddStackExchangeRedis(redisConnectionString);

        return services;
    }

    /// <summary>
    /// 验证服务配置
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns></returns>
    public static IServiceCollection ValidateEmqxRealtimeConfiguration(this IServiceCollection services)
    {
        services.AddSingleton<IStartupFilter, EmqxRealtimeStartupFilter>();
        return services;
    }

    private static MqttOptions ParseMqttConnectionString(string connectionString)
    {
        var options = new MqttOptions();
        
        try
        {
            // 简单的连接字符串解析
            // 格式: mqtt://username:password@server:port 或 mqtts://username:password@server:port
            var uri = new Uri(connectionString);
            
            options.Server = uri.Host;
            options.Port = uri.Port > 0 ? uri.Port : (uri.Scheme == "mqtts" ? 8883 : 1883);
            options.UseSsl = uri.Scheme == "mqtts";
            
            if (!string.IsNullOrEmpty(uri.UserInfo))
            {
                var userInfo = uri.UserInfo.Split(':');
                if (userInfo.Length >= 1)
                    options.Username = userInfo[0];
                if (userInfo.Length >= 2)
                    options.Password = userInfo[1];
            }
        }
        catch
        {
            // 如果解析失败，使用默认值
            options.Server = "localhost";
            options.Port = 1883;
        }

        return options;
    }
}

/// <summary>
/// 启动过滤器，用于验证配置
/// </summary>
public class EmqxRealtimeStartupFilter : IStartupFilter
{
    private readonly ILogger<EmqxRealtimeStartupFilter> _logger;

    public EmqxRealtimeStartupFilter(ILogger<EmqxRealtimeStartupFilter> logger)
    {
        _logger = logger;
    }

    public Action<IApplicationBuilder> Configure(Action<IApplicationBuilder> next)
    {
        return app =>
        {
            // 验证Redis连接
            try
            {
                var redis = app.ApplicationServices.GetService<IConnectionMultiplexer>();
                if (redis?.IsConnected == true)
                {
                    _logger.LogInformation("Redis连接验证成功");
                }
                else
                {
                    _logger.LogWarning("Redis连接验证失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Redis连接验证异常");
            }

            // 验证服务注册
            try
            {
                var subscriptionService = app.ApplicationServices.GetService<IRealtimeSubscriptionService>();
                var deviceGroupService = app.ApplicationServices.GetService<IDeviceGroupService>();
                var cacheService = app.ApplicationServices.GetService<IRedisCacheService>();
                var aggregationService = app.ApplicationServices.GetService<IDataAggregationService>();

                if (subscriptionService != null && deviceGroupService != null && 
                    cacheService != null && aggregationService != null)
                {
                    _logger.LogInformation("EMQX实时服务注册验证成功");
                }
                else
                {
                    _logger.LogWarning("EMQX实时服务注册验证失败，部分服务未注册");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "EMQX实时服务验证异常");
            }

            next(app);
        };
    }
}

/// <summary>
/// 应用程序构建器扩展
/// </summary>
public static class ApplicationBuilderExtensions
{
    /// <summary>
    /// 使用EMQX实时服务
    /// </summary>
    /// <param name="app">应用程序构建器</param>
    /// <returns></returns>
    public static IApplicationBuilder UseEmqxRealtimeServices(this IApplicationBuilder app)
    {
        // 配置SignalR Hub
        app.UseRouting();
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapHub<RealtimeSubscriptionHub>("/hubs/realtime-subscription");
        });

        return app;
    }

    /// <summary>
    /// 使用EMQX实时服务（带自定义路径）
    /// </summary>
    /// <param name="app">应用程序构建器</param>
    /// <param name="hubPath">Hub路径</param>
    /// <returns></returns>
    public static IApplicationBuilder UseEmqxRealtimeServices(this IApplicationBuilder app, string hubPath)
    {
        app.UseRouting();
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapHub<RealtimeSubscriptionHub>(hubPath);
        });

        return app;
    }
}