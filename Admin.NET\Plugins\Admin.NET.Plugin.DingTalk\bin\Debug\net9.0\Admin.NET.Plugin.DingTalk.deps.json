{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Admin.NET.Plugin.DingTalk/1.0.0": {"dependencies": {"Admin.NET.Core": "1.0.0"}, "runtime": {"Admin.NET.Plugin.DingTalk.dll": {}}}, "AlibabaCloud.EndpointUtil/0.1.1": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Tea": "1.1.3"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.EndpointUtil.dll": {"assemblyVersion": "0.1.1.0", "fileVersion": "0.1.1.0"}}}, "AlibabaCloud.GatewaySpi/0.0.3": {"dependencies": {"Aliyun.Credentials": "1.5.0", "Tea": "1.1.3"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.GatewaySpi.dll": {"assemblyVersion": "0.0.1.0", "fileVersion": "0.0.1.0"}}}, "AlibabaCloud.OpenApiClient/0.1.14": {"dependencies": {"AlibabaCloud.GatewaySpi": "0.0.3", "AlibabaCloud.OpenApiUtil": "1.1.2", "AlibabaCloud.TeaUtil": "0.1.19", "AlibabaCloud.TeaXML": "0.0.5", "Aliyun.Credentials": "1.5.0", "Tea": "1.1.3"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.OpenApiClient.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AlibabaCloud.OpenApiUtil/1.1.2": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Tea": "1.1.3"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.OpenApiUtil.dll": {"assemblyVersion": "1.1.2.0", "fileVersion": "1.1.2.0"}}}, "AlibabaCloud.SDK.Dysmsapi20170525/4.0.0": {"dependencies": {"AlibabaCloud.EndpointUtil": "0.1.1", "AlibabaCloud.OpenApiClient": "0.1.14", "AlibabaCloud.OpenApiUtil": "1.1.2", "AlibabaCloud.TeaUtil": "0.1.19", "Tea": "1.1.3"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.SDK.Dysmsapi20170525.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AlibabaCloud.TeaUtil/0.1.19": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Tea": "1.1.3"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.TeaUtil.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AlibabaCloud.TeaXML/0.0.5": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Tea": "1.1.3"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.TeaXML.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AlipaySDKNet.Standard/4.9.627": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Portable.BouncyCastle": "*******", "System.Text.Encoding.CodePages": "9.0.6"}, "runtime": {"lib/netstandard2.0/AlipaySDKNet.Standard.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Aliyun.Credentials/1.5.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3", "Tea": "1.1.3"}, "runtime": {"lib/netstandard2.0/Aliyun.Credentials.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Aliyun.OSS.SDK.NetCore/2.13.0": {"runtime": {"lib/netstandard2.0/Aliyun.OSS.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AngleSharp/1.3.0": {"runtime": {"lib/net8.0/AngleSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AspectCore.Extensions.Reflection/2.4.0": {"runtime": {"lib/net7.0/AspectCore.Extensions.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AspNet.Security.OAuth.Gitee/9.4.0": {"runtime": {"lib/net9.0/AspNet.Security.OAuth.Gitee.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.27134"}}}, "AspNet.Security.OAuth.Weixin/9.4.0": {"runtime": {"lib/net9.0/AspNet.Security.OAuth.Weixin.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.27134"}}}, "AspNetCoreRateLimit/5.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/AspNetCoreRateLimit.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.0.2.0"}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "9.0.6", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "9.0.6", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.38.0.0", "fileVersion": "1.3800.24.12602"}}}, "Azure.Identity/1.11.4": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.6.0", "System.Security.Cryptography.ProtectedData": "9.0.6", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.11.4.0", "fileVersion": "1.1100.424.31005"}}}, "BceSdkDotNetCore/1.0.2.911": {"dependencies": {"Newtonsoft.Json": "13.0.3", "log4net": "3.1.0"}, "runtime": {"lib/netstandard2.0/BceSdkDotNetCore.dll": {"assemblyVersion": "1.0.2.911", "fileVersion": "1.0.2.911"}}}, "Ben.Demystifier/0.4.1": {"dependencies": {"System.Reflection.Metadata": "8.0.1"}, "runtime": {"lib/netstandard2.1/Ben.Demystifier.dll": {"assemblyVersion": "0.4.0.0", "fileVersion": "0.4.0.2"}}}, "BouncyCastle.Cryptography/2.6.1": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "2.6.1.59591"}}}, "CommunityToolkit.HighPerformance/8.1.0": {"runtime": {"lib/net7.0/CommunityToolkit.HighPerformance.dll": {"assemblyVersion": "8.1.0.0", "fileVersion": "8.1.0.1"}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/net5.0/DnsClient.dll": {"assemblyVersion": "1.6.1.0", "fileVersion": "1.6.1.0"}}}, "DocumentFormat.OpenXml/3.1.1": {"dependencies": {"DocumentFormat.OpenXml.Framework": "3.1.1"}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.dll": {"assemblyVersion": "3.1.1.0", "fileVersion": "3.1.1.0"}}}, "DocumentFormat.OpenXml.Framework/3.1.1": {"dependencies": {"System.IO.Packaging": "9.0.6"}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {"assemblyVersion": "3.1.1.0", "fileVersion": "3.1.1.0"}}}, "DynamicExpresso.Core/2.3.3": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.0/DynamicExpresso.Core.dll": {"assemblyVersion": "2.3.3.0", "fileVersion": "2.3.3.0"}}}, "Elastic.Clients.Elasticsearch/9.0.7": {"dependencies": {"Elastic.Transport": "0.9.2"}, "runtime": {"lib/net8.0/Elastic.Clients.Elasticsearch.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elastic.Transport/0.9.2": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/net8.0/Elastic.Transport.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.9.2.0"}}}, "Flurl/4.0.0": {"runtime": {"lib/netstandard2.0/Flurl.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "Flurl.Http/4.0.2": {"dependencies": {"Flurl": "4.0.0"}, "runtime": {"lib/net6.0/Flurl.Http.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.0.2.0"}}}, "Furion.Extras.Authentication.JwtBearer/*********": {"dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.0"}, "runtime": {"lib/net9.0/Furion.Extras.Authentication.JwtBearer.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Furion.Extras.ObjectMapper.Mapster/*********": {"dependencies": {"Mapster": "7.4.0", "Mapster.DependencyInjection": "1.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Furion.Extras.ObjectMapper.Mapster.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Furion.Pure/*********": {"dependencies": {"Furion.Pure.Extras.DependencyModel.CodeAnalysis": "*********", "Swashbuckle.AspNetCore": "9.0.3"}, "runtime": {"lib/net9.0/Furion.Pure.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Furion.Pure.Extras.DependencyModel.CodeAnalysis/*********": {"dependencies": {"Ben.Demystifier": "0.4.1", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "9.0.0", "Microsoft.AspNetCore.Razor.Language": "6.0.36", "Microsoft.CodeAnalysis.CSharp": "4.11.0", "Microsoft.Extensions.DependencyModel": "9.0.0"}, "runtime": {"lib/net9.0/Furion.Pure.Extras.DependencyModel.CodeAnalysis.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Hardware.Info/*********": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Diagnostics.PerformanceCounter": "9.0.6", "System.Management": "9.0.6"}, "runtime": {"lib/netstandard2.0/Hardware.Info.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Hashids.net/1.7.0": {"runtime": {"lib/net7.0/Hashids.net.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}}}, "Haukcode.WkHtmlToPdfDotNet/1.5.95": {"dependencies": {"System.Collections.Concurrent": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection.TypeExtensions": "4.7.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices": "4.3.0", "System.Threading.Thread": "4.3.0"}, "runtime": {"lib/netstandard2.0/WkHtmlToPdfDotNet.dll": {"assemblyVersion": "1.5.95.0", "fileVersion": "1.5.95.0"}}, "runtimeTargets": {"runtimes/linux-arm64/native/libwkhtmltox.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libwkhtmltox.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libwkhtmltox.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libwkhtmltox.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/wkhtmltox.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "********"}, "runtimes/win-x86/native/wkhtmltox.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "********"}}}, "HtmlToOpenXml.dll/3.1.0": {"dependencies": {"AngleSharp": "1.3.0", "DocumentFormat.OpenXml": "3.1.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.7"}, "runtime": {"lib/net8.0/HtmlToOpenXml.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "********", "fileVersion": "2.14.1.48190"}}}, "IP2Region.Ex/1.2.0": {"runtime": {"lib/netstandard2.0/IP2Region.Ex.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "IPTools.China/1.6.0": {"dependencies": {"IP2Region.Ex": "1.2.0", "IPTools.Core": "1.6.0"}, "runtime": {"lib/net5.0/IPTools.China.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "IPTools.Core/1.6.0": {"dependencies": {"IP2Region.Ex": "1.2.0"}, "runtime": {"lib/net5.0/IPTools.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "IPTools.International/1.6.0": {"dependencies": {"IPTools.Core": "1.6.0", "MaxMind.GeoIP2": "3.0.0"}, "runtime": {"lib/net5.0/IPTools.International.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Json.More.Net/2.0.2": {"runtime": {"lib/net8.0/Json.More.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "JsonPointer.Net/5.0.2": {"dependencies": {"Humanizer.Core": "2.14.1", "Json.More.Net": "2.0.2"}, "runtime": {"lib/net8.0/JsonPointer.Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "JsonSchema.Net/7.2.3": {"dependencies": {"JsonPointer.Net": "5.0.2"}, "runtime": {"lib/net8.0/JsonSchema.Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Lazy.Captcha.Core/2.1.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.0", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "SkiaSharp": "3.116.1", "SkiaSharp.NativeAssets.Linux.NoDependencies": "3.116.1"}, "runtime": {"lib/netstandard2.0/Lazy.Captcha.Core.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}}, "log4net/3.1.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.6"}, "runtime": {"lib/netstandard2.0/log4net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Magicodes.IE.Core/2.7.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "SixLabors.ImageSharp": "3.1.8", "System.ComponentModel.Annotations": "4.7.0"}, "runtime": {"lib/net8.0/Magicodes.IE.Core.dll": {"assemblyVersion": "2.7.6.0", "fileVersion": "2.7.6.0"}}, "resources": {"lib/net8.0/zh-Hans/Magicodes.IE.Core.resources.dll": {"locale": "zh-Hans"}}}, "Magicodes.IE.EPPlus/2.7.6": {"dependencies": {"SixLabors.ImageSharp": "3.1.8", "SkiaSharp": "3.116.1", "System.Security.Cryptography.Pkcs": "9.0.7", "System.Text.Encoding.CodePages": "9.0.6"}, "runtime": {"lib/net8.0/Magicodes.IE.EPPlus.dll": {"assemblyVersion": "2.7.6.0", "fileVersion": "2.7.6.0"}}}, "Magicodes.IE.Excel/2.7.6": {"dependencies": {"DynamicExpresso.Core": "2.3.3", "Magicodes.IE.Core": "2.7.6", "Magicodes.IE.EPPlus": "2.7.6", "SkiaSharp.NativeAssets.Linux.NoDependencies": "3.116.1", "System.Linq.Dynamic.Core": "1.6.6"}, "runtime": {"lib/net8.0/Magicodes.IE.Excel.dll": {"assemblyVersion": "2.7.6.0", "fileVersion": "2.7.6.0"}}}, "Magicodes.IE.Html/2.7.6": {"dependencies": {"Magicodes.IE.Core": "2.7.6", "Magicodes.RazorEngine.NetCore": "2.2.0"}, "runtime": {"lib/net8.0/Magicodes.IE.Html.dll": {"assemblyVersion": "2.7.6.0", "fileVersion": "2.7.6.0"}}}, "Magicodes.IE.Pdf/2.7.6": {"dependencies": {"Haukcode.WkHtmlToPdfDotNet": "1.5.95", "Magicodes.IE.Html": "2.7.6"}, "runtime": {"lib/net8.0/Magicodes.IE.Pdf.dll": {"assemblyVersion": "2.7.6.0", "fileVersion": "2.7.6.0"}}}, "Magicodes.IE.Word/2.7.6": {"dependencies": {"HtmlToOpenXml.dll": "3.1.0", "Magicodes.IE.Html": "2.7.6"}, "runtime": {"lib/net8.0/Magicodes.IE.Word.dll": {"assemblyVersion": "2.7.6.0", "fileVersion": "2.7.6.0"}}}, "Magicodes.RazorEngine.NetCore/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.36", "Microsoft.AspNetCore.Razor.Runtime": "2.2.0", "Microsoft.CSharp": "4.7.0", "Microsoft.CodeAnalysis.CSharp": "4.11.0", "System.Reflection.Emit": "4.3.0", "System.Security.Permissions": "9.0.6"}, "runtime": {"lib/netstandard2.1/RazorEngine.NetCore.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "MailKit/4.13.0": {"dependencies": {"MimeKit": "4.13.0", "System.Formats.Asn1": "8.0.1"}, "runtime": {"lib/net8.0/MailKit.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Mapster/7.4.0": {"dependencies": {"Mapster.Core": "1.2.1"}, "runtime": {"lib/net7.0/Mapster.dll": {"assemblyVersion": "7.4.0.0", "fileVersion": "7.4.0.0"}}}, "Mapster.Core/1.2.1": {"runtime": {"lib/net7.0/Mapster.Core.dll": {"assemblyVersion": "1.2.1.0", "fileVersion": "1.2.1.0"}}}, "Mapster.DependencyInjection/1.0.1": {"dependencies": {"Mapster": "7.4.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "runtime": {"lib/net7.0/Mapster.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Markdig.Signed/0.38.0": {"runtime": {"lib/net8.0/Markdig.Signed.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MaxMind.Db/2.4.0": {"runtime": {"lib/netstandard2.0/MaxMind.Db.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MaxMind.GeoIP2/3.0.0": {"dependencies": {"MaxMind.Db": "2.4.0", "Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/MaxMind.GeoIP2.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MessagePack/2.5.187": {"dependencies": {"MessagePack.Annotations": "2.5.187", "Microsoft.NET.StringTools": "17.6.3"}, "runtime": {"lib/net6.0/MessagePack.dll": {"assemblyVersion": "*******", "fileVersion": "2.5.187.63700"}}}, "MessagePack.Annotations/2.5.187": {"runtime": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "2.5.187.63700"}}}, "Microsoft.ApplicationInsights/2.22.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "9.0.6"}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.0": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Connections.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Features": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.7": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.AspNetCore.DataProtection/9.0.7": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "9.0.7", "Microsoft.AspNetCore.DataProtection.Abstractions": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "System.Security.Cryptography.Xml": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/9.0.7": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.AspNetCore.DataProtection.StackExchangeRedis/9.0.7": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "9.0.7", "StackExchange.Redis": "2.7.27"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.DataProtection.StackExchangeRedis.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7"}}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"dependencies": {"System.Text.Encodings.Web": "9.0.6"}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "9.0.6"}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.7", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.1"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}}, "Microsoft.AspNetCore.JsonPatch/9.0.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/9.0.0": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "9.0.0", "Newtonsoft.Json": "13.0.3", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Razor.Language/6.0.36": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "6.0.36.0", "fileVersion": "6.0.3624.51604"}}}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0", "Microsoft.AspNetCore.Razor": "2.2.0"}}, "Microsoft.AspNetCore.SignalR.Common/9.0.7": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson/9.0.7": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "9.0.7", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.AspNetCore.SignalR.StackExchangeRedis/9.0.7": {"dependencies": {"MessagePack": "2.5.187", "Microsoft.Extensions.Options": "9.0.7", "StackExchange.Redis": "2.7.27"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.SignalR.StackExchangeRedis.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.AspNetCore.StaticFiles/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.WebEncoders": "2.2.0"}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.CodeAnalysis.Analyzers/3.11.0": {}, "Microsoft.CodeAnalysis.Common/4.11.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.11.0", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "********", "fileVersion": "4.1100.24.37604"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.11.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.11.0", "Microsoft.CodeAnalysis.Common": "4.11.0", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "********", "fileVersion": "4.1100.24.37604"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/5.2.2": {"dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Data.SqlClient.SNI.runtime": "5.2.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "9.0.6", "System.Runtime.Caching": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}}, "resources": {"lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}, "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.Data.Sqlite/9.0.0": {"dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.0", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10"}}, "Microsoft.Data.Sqlite.Core/9.0.0": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52902"}}}, "Microsoft.Extensions.ApiDescription.Server/9.0.0": {}, "Microsoft.Extensions.Caching.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}}, "Microsoft.Extensions.Caching.Memory/9.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7"}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.DependencyModel/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Features/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.Features.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.7", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.ObjectPool/8.0.17": {}, "Microsoft.Extensions.Options/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}}, "Microsoft.Extensions.Primitives/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.WebEncoders/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "System.Text.Encodings.Web": "9.0.6"}}, "Microsoft.Identity.Client/4.61.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1", "System.Diagnostics.DiagnosticSource": "9.0.6"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "9.0.6"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Logging/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Tokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}}, "Microsoft.Management.Infrastructure/3.0.0": {"dependencies": {"Microsoft.Management.Infrastructure.Runtime.Unix": "3.0.0", "Microsoft.Management.Infrastructure.Runtime.Win": "3.0.0"}}, "Microsoft.Management.Infrastructure.CimCmdlets/7.5.2": {"dependencies": {"System.Management.Automation": "7.5.2"}, "runtimeTargets": {"runtimes/win/lib/net9.0/Microsoft.Management.Infrastructure.CimCmdlets.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}}}, "Microsoft.Management.Infrastructure.Runtime.Unix/3.0.0": {"runtimeTargets": {"runtimes/unix/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Management.Infrastructure.Runtime.Win/3.0.0": {"runtimeTargets": {"runtimes/win-arm64/lib/netstandard1.6/microsoft.management.infrastructure.dll": {"rid": "win-arm64", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "10.0.17763.30000"}, "runtimes/win-arm64/lib/netstandard1.6/microsoft.management.infrastructure.native.dll": {"rid": "win-arm64", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "10.0.17763.30000"}, "runtimes/win-x64/lib/netstandard1.6/microsoft.management.infrastructure.dll": {"rid": "win-x64", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "10.0.17763.30000"}, "runtimes/win-x64/lib/netstandard1.6/microsoft.management.infrastructure.native.dll": {"rid": "win-x64", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "10.0.17763.30000"}, "runtimes/win-x86/lib/netstandard1.6/microsoft.management.infrastructure.dll": {"rid": "win-x86", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "10.0.17763.30000"}, "runtimes/win-x86/lib/netstandard1.6/microsoft.management.infrastructure.native.dll": {"rid": "win-x86", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "10.0.17763.30000"}, "runtimes/win-arm64/native/microsoft.management.infrastructure.native.unmanaged.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "10.0.17763.30000"}, "runtimes/win-x64/native/microsoft.management.infrastructure.native.unmanaged.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.3.9600.18144"}, "runtimes/win-x86/native/microsoft.management.infrastructure.native.unmanaged.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "6.3.9600.18144"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7", "System.Buffers": "4.5.1"}}, "Microsoft.NET.StringTools/17.6.3": {"runtime": {"lib/net7.0/Microsoft.NET.StringTools.dll": {"assemblyVersion": "*******", "fileVersion": "17.6.3.22601"}}}, "Microsoft.NETCore.Platforms/1.1.1": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.OpenApi/1.6.23": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.23.0", "fileVersion": "1.6.23.0"}}}, "Microsoft.PowerShell.Commands.Diagnostics/7.5.2": {"dependencies": {"System.Management.Automation": "7.5.2"}, "runtimeTargets": {"runtimes/win/lib/net9.0/Microsoft.PowerShell.Commands.Diagnostics.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}}}, "Microsoft.PowerShell.Commands.Management/7.5.2": {"dependencies": {"Microsoft.PowerShell.Security": "7.5.2", "System.Diagnostics.EventLog": "9.0.6", "System.ServiceProcess.ServiceController": "9.0.6"}, "runtimeTargets": {"runtimes/unix/lib/net9.0/Microsoft.PowerShell.Commands.Management.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}, "runtimes/win/lib/net9.0/Microsoft.PowerShell.Commands.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}}}, "Microsoft.PowerShell.Commands.Utility/7.5.2": {"dependencies": {"Json.More.Net": "2.0.2", "JsonPointer.Net": "5.0.2", "JsonSchema.Net": "7.2.3", "Markdig.Signed": "0.38.0", "Microsoft.CodeAnalysis.Analyzers": "3.11.0", "Microsoft.CodeAnalysis.CSharp": "4.11.0", "Microsoft.PowerShell.MarkdownRender": "7.2.1", "Microsoft.Win32.SystemEvents": "9.0.6", "System.Drawing.Common": "9.0.6", "System.Management.Automation": "7.5.2", "System.Reflection.Metadata": "8.0.1", "System.Threading.AccessControl": "9.0.6"}, "runtimeTargets": {"runtimes/unix/lib/net9.0/Microsoft.PowerShell.Commands.Utility.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}, "runtimes/win/lib/net9.0/Microsoft.PowerShell.Commands.Utility.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}}}, "Microsoft.PowerShell.ConsoleHost/7.5.2": {"dependencies": {"System.Management.Automation": "7.5.2"}, "runtimeTargets": {"runtimes/unix/lib/net9.0/Microsoft.PowerShell.ConsoleHost.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}, "runtimes/win/lib/net9.0/Microsoft.PowerShell.ConsoleHost.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}}}, "Microsoft.PowerShell.CoreCLR.Eventing/7.5.2": {"dependencies": {"System.Diagnostics.EventLog": "9.0.6"}, "runtimeTargets": {"runtimes/win/lib/net9.0/Microsoft.PowerShell.CoreCLR.Eventing.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}}}, "Microsoft.PowerShell.MarkdownRender/7.2.1": {"dependencies": {"Markdig.Signed": "0.38.0"}, "runtime": {"lib/netstandard2.0/Microsoft.PowerShell.MarkdownRender.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.PowerShell.Native/7.4.0": {"runtimeTargets": {"runtimes/linux-arm/native/libpsl-native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libpsl-native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libpsl-native.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libpsl-native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libpsl-native.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/PowerShell.Core.Instrumentation.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-arm/native/pwrshplugin.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-arm64/native/PowerShell.Core.Instrumentation.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-arm64/native/pwrshplugin.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-x64/native/PowerShell.Core.Instrumentation.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-x64/native/pwrshplugin.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-x86/native/PowerShell.Core.Instrumentation.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-x86/native/pwrshplugin.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "10.0.10011.16384"}}}, "Microsoft.PowerShell.SDK/7.5.2": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.ObjectPool": "8.0.17", "Microsoft.Management.Infrastructure.CimCmdlets": "7.5.2", "Microsoft.PowerShell.Commands.Diagnostics": "7.5.2", "Microsoft.PowerShell.Commands.Management": "7.5.2", "Microsoft.PowerShell.Commands.Utility": "7.5.2", "Microsoft.PowerShell.ConsoleHost": "7.5.2", "Microsoft.PowerShell.Security": "7.5.2", "Microsoft.WSMan.Management": "7.5.2", "Microsoft.Win32.Registry.AccessControl": "9.0.6", "Microsoft.Win32.SystemEvents": "9.0.6", "Microsoft.Windows.Compatibility": "9.0.6", "System.CodeDom": "9.0.6", "System.ComponentModel.Composition": "9.0.6", "System.ComponentModel.Composition.Registration": "9.0.6", "System.Configuration.ConfigurationManager": "9.0.6", "System.Data.Odbc": "9.0.6", "System.Data.OleDb": "9.0.6", "System.Data.SqlClient": "4.9.0", "System.Diagnostics.EventLog": "9.0.6", "System.Diagnostics.PerformanceCounter": "9.0.6", "System.DirectoryServices": "9.0.6", "System.DirectoryServices.AccountManagement": "9.0.6", "System.DirectoryServices.Protocols": "9.0.6", "System.Drawing.Common": "9.0.6", "System.IO.Packaging": "9.0.6", "System.IO.Ports": "9.0.6", "System.Management": "9.0.6", "System.Management.Automation": "7.5.2", "System.Net.Http.WinHttpHandler": "9.0.6", "System.Private.ServiceModel": "4.10.3", "System.Reflection.Context": "9.0.6", "System.Runtime.Caching": "9.0.6", "System.Security.Cryptography.Pkcs": "9.0.7", "System.Security.Cryptography.ProtectedData": "9.0.6", "System.Security.Cryptography.Xml": "9.0.7", "System.Security.Permissions": "9.0.6", "System.ServiceModel.Duplex": "4.10.3", "System.ServiceModel.Http": "4.10.3", "System.ServiceModel.NetTcp": "4.10.3", "System.ServiceModel.Primitives": "4.10.3", "System.ServiceModel.Security": "4.10.3", "System.ServiceProcess.ServiceController": "9.0.6", "System.Speech": "9.0.6", "System.Text.Encoding.CodePages": "9.0.6", "System.Text.Encodings.Web": "9.0.6", "System.Threading.AccessControl": "9.0.6", "System.Web.Services.Description": "8.0.0", "System.Windows.Extensions": "9.0.6", "runtime.android-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.native.System.IO.Ports": "9.0.6", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.6"}, "runtimeTargets": {"runtimes/unix/lib/net9.0/Microsoft.PowerShell.SDK.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}, "runtimes/win/lib/net9.0/Microsoft.PowerShell.SDK.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}}}, "Microsoft.PowerShell.Security/7.5.2": {"dependencies": {"System.Management.Automation": "7.5.2"}, "runtimeTargets": {"runtimes/unix/lib/net9.0/Microsoft.PowerShell.Security.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}, "runtimes/win/lib/net9.0/Microsoft.PowerShell.Security.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}}}, "Microsoft.Security.Extensions/1.4.0": {"runtimeTargets": {"runtimes/win-arm64/lib/net5.0/getfilesiginforedistwrapper.dll": {"rid": "win-arm64", "assetType": "runtime", "assemblyVersion": "10.0.0.0", "fileVersion": "10.0.27797.1000"}, "runtimes/win-x64/lib/net5.0/getfilesiginforedistwrapper.dll": {"rid": "win-x64", "assetType": "runtime", "assemblyVersion": "10.0.0.0", "fileVersion": "10.0.27797.1000"}, "runtimes/win-x86/lib/net5.0/getfilesiginforedistwrapper.dll": {"rid": "win-x86", "assetType": "runtime", "assemblyVersion": "10.0.0.0", "fileVersion": "10.0.27797.1000"}, "runtimes/win-arm64/native/getfilesiginforedist.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/getfilesiginforedist.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/getfilesiginforedist.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.1", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.Registry.AccessControl/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/Microsoft.Win32.Registry.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Win32.SystemEvents/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Windows.Compatibility/9.0.6": {"dependencies": {"Microsoft.Win32.Registry.AccessControl": "9.0.6", "Microsoft.Win32.SystemEvents": "9.0.6", "System.CodeDom": "9.0.6", "System.ComponentModel.Composition": "9.0.6", "System.ComponentModel.Composition.Registration": "9.0.6", "System.Configuration.ConfigurationManager": "9.0.6", "System.Data.Odbc": "9.0.6", "System.Data.OleDb": "9.0.6", "System.Data.SqlClient": "4.9.0", "System.Diagnostics.EventLog": "9.0.6", "System.Diagnostics.PerformanceCounter": "9.0.6", "System.DirectoryServices": "9.0.6", "System.DirectoryServices.AccountManagement": "9.0.6", "System.DirectoryServices.Protocols": "9.0.6", "System.Drawing.Common": "9.0.6", "System.IO.Packaging": "9.0.6", "System.IO.Ports": "9.0.6", "System.Management": "9.0.6", "System.Reflection.Context": "9.0.6", "System.Runtime.Caching": "9.0.6", "System.Security.Cryptography.Pkcs": "9.0.7", "System.Security.Cryptography.ProtectedData": "9.0.6", "System.Security.Cryptography.Xml": "9.0.7", "System.Security.Permissions": "9.0.6", "System.Security.Principal.Windows": "5.0.0", "System.ServiceModel.Duplex": "4.10.3", "System.ServiceModel.Http": "4.10.3", "System.ServiceModel.NetTcp": "4.10.3", "System.ServiceModel.Primitives": "4.10.3", "System.ServiceModel.Security": "4.10.3", "System.ServiceModel.Syndication": "9.0.6", "System.ServiceProcess.ServiceController": "9.0.6", "System.Speech": "9.0.6", "System.Text.Encoding.CodePages": "9.0.6", "System.Threading.AccessControl": "9.0.6", "System.Web.Services.Description": "8.0.0"}}, "Microsoft.WSMan.Management/7.5.2": {"dependencies": {"Microsoft.WSMan.Runtime": "7.5.2", "System.Diagnostics.EventLog": "9.0.6", "System.Management.Automation": "7.5.2", "System.ServiceProcess.ServiceController": "9.0.6"}, "runtimeTargets": {"runtimes/win/lib/net9.0/Microsoft.WSMan.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}}}, "Microsoft.WSMan.Runtime/7.5.2": {"runtimeTargets": {"runtimes/win/lib/net9.0/Microsoft.WSMan.Runtime.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}}}, "MimeKit/4.13.0": {"dependencies": {"BouncyCastle.Cryptography": "2.6.1", "System.Security.Cryptography.Pkcs": "9.0.7"}, "runtime": {"lib/net8.0/MimeKit.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MiniExcel/1.41.3": {"runtime": {"lib/net9.0/MiniExcel.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Minio/5.0.0": {"dependencies": {"CommunityToolkit.HighPerformance": "8.1.0", "System.IO.Hashing": "7.0.0", "System.Reactive.Linq": "5.0.0"}, "runtime": {"lib/net7.0/Minio.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MiniWord/0.9.2": {"dependencies": {"DocumentFormat.OpenXml": "3.1.1"}, "runtime": {"lib/netstandard2.0/MiniWord.dll": {"assemblyVersion": "0.9.2.0", "fileVersion": "0.9.2.0"}}}, "MongoDB.Bson/3.3.0": {"dependencies": {"System.Memory": "4.6.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/net6.0/MongoDB.Bson.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.3.0.0"}}}, "MongoDB.Driver/3.3.0": {"dependencies": {"DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "MongoDB.Bson": "3.3.0", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.7.3"}, "runtime": {"lib/net6.0/MongoDB.Driver.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.3.0.0"}}}, "MySqlConnector/2.2.5": {"runtime": {"lib/net7.0/MySqlConnector.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.5.0"}}}, "NewLife.Core/11.5.2025.701": {"runtime": {"lib/net9.0/NewLife.Core.dll": {"assemblyVersion": "11.5.2025.701", "fileVersion": "11.5.2025.701"}}}, "NewLife.Redis/6.3.2025.701": {"dependencies": {"NewLife.Core": "11.5.2025.701"}, "runtime": {"lib/netstandard2.1/NewLife.Redis.dll": {"assemblyVersion": "6.3.2025.701", "fileVersion": "6.3.2025.701"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}}, "Novell.Directory.Ldap.NETStandard/4.0.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Novell.Directory.Ldap.NETStandard.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "Npgsql/5.0.18": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/net5.0/Npgsql.dll": {"assemblyVersion": "5.0.18.0", "fileVersion": "5.0.18.0"}}}, "OnceMi.AspNetCore.OSS/1.2.0": {"dependencies": {"Aliyun.OSS.SDK.NetCore": "2.13.0", "BceSdkDotNetCore": "1.0.2.911", "Microsoft.AspNetCore.StaticFiles": "2.2.0", "Microsoft.CSharp": "4.7.0", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Options": "9.0.7", "Minio": "5.0.0", "Qiniu": "8.3.1", "Tencent.QCloud.Cos.Sdk": "5.4.34"}, "runtime": {"lib/netstandard2.1/OnceMi.AspNetCore.OSS.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Oracle.ManagedDataAccess.Core/23.8.0": {"dependencies": {"System.Diagnostics.PerformanceCounter": "9.0.6", "System.DirectoryServices.Protocols": "9.0.6", "System.Formats.Asn1": "8.0.1", "System.Memory": "4.6.0", "System.Security.Cryptography.Pkcs": "9.0.7"}, "runtime": {"lib/net8.0/Oracle.ManagedDataAccess.dll": {"assemblyVersion": "2*******", "fileVersion": "2*******"}}}, "Oscar.Data.SqlClient/4.0.4": {"dependencies": {"System.Text.Encoding.CodePages": "9.0.6"}, "runtime": {"lib/netstandard2.0/Oscar.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"dependencies": {"System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.8.1080"}}}, "Portable.BouncyCastle/*******": {"runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Qiniu/8.3.1": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Qiniu.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "QRCoder/1.6.0": {"runtime": {"lib/net6.0/QRCoder.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "RabbitMQ.Client/7.1.2": {"dependencies": {"System.IO.Pipelines": "8.0.0", "System.Threading.RateLimiting": "8.0.0"}, "runtime": {"lib/net8.0/RabbitMQ.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/android-arm/native/libSystem.IO.Ports.Native.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/android-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/android-x64/native/libSystem.IO.Ports.Native.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/android-x86/native/libSystem.IO.Ports.Native.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-bionic-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-bionic-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.IO.Ports/9.0.6": {"dependencies": {"runtime.android-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.6"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "SharpCompress/0.30.1": {"runtime": {"lib/net5.0/SharpCompress.dll": {"assemblyVersion": "0.30.1.0", "fileVersion": "0.30.1.0"}}}, "SixLabors.ImageSharp/3.1.8": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.8.0"}}}, "SixLabors.ImageSharp.Web/3.1.5": {"dependencies": {"Microsoft.IO.RecyclableMemoryStream": "3.0.1", "SixLabors.ImageSharp": "3.1.8"}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.Web.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.5.0"}}}, "SkiaSharp/3.116.1": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "3.116.1", "SkiaSharp.NativeAssets.macOS": "3.116.1"}, "runtime": {"lib/net8.0/SkiaSharp.dll": {"assemblyVersion": "3.116.0.0", "fileVersion": "3.116.1.0"}}}, "SkiaSharp.NativeAssets.Linux.NoDependencies/3.116.1": {"runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/3.116.1": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/3.116.1": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SKIT.FlurlHttpClient.Common/3.1.1": {"dependencies": {"Flurl": "4.0.0", "Flurl.Http": "4.0.2", "Newtonsoft.Json": "13.0.3", "System.Text.Encodings.Web": "9.0.6", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/SKIT.FlurlHttpClient.Common.dll": {"assemblyVersion": "3.1.1.0", "fileVersion": "3.1.1.0"}}}, "SKIT.FlurlHttpClient.Wechat.Api/3.11.0": {"dependencies": {"BouncyCastle.Cryptography": "2.6.1", "SKIT.FlurlHttpClient.Common": "3.1.1"}, "runtime": {"lib/net8.0/SKIT.FlurlHttpClient.Wechat.Api.dll": {"assemblyVersion": "3.11.0.0", "fileVersion": "3.11.0.0"}}}, "SKIT.FlurlHttpClient.Wechat.TenpayV3/3.13.0": {"dependencies": {"BouncyCastle.Cryptography": "2.6.1", "SKIT.FlurlHttpClient.Common": "3.1.1"}, "runtime": {"lib/net8.0/SKIT.FlurlHttpClient.Wechat.TenpayV3.dll": {"assemblyVersion": "3.13.0.0", "fileVersion": "3.13.0.0"}}}, "Snappier/1.0.0": {"runtime": {"lib/net5.0/Snappier.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.core/2.1.10": {"dependencies": {"System.Memory": "4.6.0"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"runtimeTargets": {"runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-s390x/native/libe_sqlite3.so": {"rid": "linux-musl-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"rid": "linux-ppc64le", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SqlSugar.MongoDbCore/*********": {"dependencies": {"MongoDB.Driver": "3.3.0"}, "runtime": {"lib/net6.0/MongoDb.Ado.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/SqlSugar.MongoDbCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SqlSugarCore/*********": {"dependencies": {"Microsoft.Data.SqlClient": "5.2.2", "Microsoft.Data.Sqlite": "9.0.0", "MySqlConnector": "2.2.5", "Newtonsoft.Json": "13.0.3", "Npgsql": "5.0.18", "Oracle.ManagedDataAccess.Core": "23.8.0", "Oscar.Data.SqlClient": "4.0.4", "SqlSugarCore.Dm": "8.8.0", "SqlSugarCore.Kdbndp": "9.3.7.613", "System.Data.Common": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Text.RegularExpressions": "4.3.1"}, "runtime": {"lib/netstandard2.1/SqlSugar.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "SqlSugarCore.Dm/8.8.0": {"runtime": {"lib/netstandard2.1/DM.DmProvider.dll": {"assemblyVersion": "8.3.1.33449", "fileVersion": "8.3.1.33449"}}}, "SqlSugarCore.Kdbndp/9.3.7.613": {"runtime": {"lib/netstandard2.1/Kdbndp.dll": {"assemblyVersion": "9.3.7.613", "fileVersion": "9.3.7.613"}}}, "SSH.NET/2025.0.0": {"dependencies": {"BouncyCastle.Cryptography": "2.6.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Renci.SshNet.dll": {"assemblyVersion": "2025.0.0.1", "fileVersion": "2025.0.0.1"}}}, "StackExchange.Redis/2.7.27": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Pipelines.Sockets.Unofficial": "2.2.8"}, "runtime": {"lib/net6.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.7.27.49176"}}}, "Swashbuckle.AspNetCore/9.0.3": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "9.0.0", "Swashbuckle.AspNetCore.Swagger": "9.0.3", "Swashbuckle.AspNetCore.SwaggerGen": "9.0.3", "Swashbuckle.AspNetCore.SwaggerUI": "9.0.3"}}, "Swashbuckle.AspNetCore.Swagger/9.0.3": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.3": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "9.0.3"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.3": {"runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}}, "System.Buffers/4.5.1": {}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}}, "System.CodeDom/9.0.6": {"runtime": {"lib/net9.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/8.0.0": {}, "System.ComponentModel.Annotations/4.7.0": {}, "System.ComponentModel.Composition/9.0.6": {"runtime": {"lib/net9.0/System.ComponentModel.Composition.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.ComponentModel.Composition.Registration/9.0.6": {"dependencies": {"System.ComponentModel.Composition": "9.0.6", "System.Reflection.Context": "9.0.6"}, "runtime": {"lib/net9.0/System.ComponentModel.Composition.Registration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Configuration.ConfigurationManager/9.0.6": {"dependencies": {"System.Diagnostics.EventLog": "9.0.6", "System.Security.Cryptography.ProtectedData": "9.0.6"}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Data.Odbc/9.0.6": {"runtime": {"lib/net9.0/System.Data.Odbc.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/unix/lib/net9.0/System.Data.Odbc.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "runtimes/win/lib/net9.0/System.Data.Odbc.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Data.OleDb/9.0.6": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.6", "System.Diagnostics.PerformanceCounter": "9.0.6"}, "runtime": {"lib/net9.0/System.Data.OleDb.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Data.OleDb.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Data.SqlClient/4.9.0": {"dependencies": {"runtime.native.System.Data.SqlClient.sni": "4.4.0"}, "runtime": {"lib/net8.0/System.Data.SqlClient.dll": {"assemblyVersion": "4.6.1.6", "fileVersion": "4.900.24.56208"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "4.6.1.6", "fileVersion": "4.900.24.56208"}, "runtimes/win/lib/net8.0/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.6.1.6", "fileVersion": "4.900.24.56208"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.DiagnosticSource/9.0.6": {}, "System.Diagnostics.EventLog/9.0.6": {"runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Diagnostics.PerformanceCounter/9.0.6": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.6"}, "runtime": {"lib/net9.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.DirectoryServices/9.0.6": {"runtime": {"lib/net9.0/System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.DirectoryServices.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.DirectoryServices.AccountManagement/9.0.6": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.6", "System.DirectoryServices": "9.0.6", "System.DirectoryServices.Protocols": "9.0.6"}, "runtime": {"lib/net9.0/System.DirectoryServices.AccountManagement.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.DirectoryServices.AccountManagement.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.DirectoryServices.Protocols/9.0.6": {"runtime": {"lib/net9.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/linux/lib/net9.0/System.DirectoryServices.Protocols.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "runtimes/osx/lib/net9.0/System.DirectoryServices.Protocols.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "runtimes/win/lib/net9.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Drawing.Common/9.0.6": {"dependencies": {"Microsoft.Win32.SystemEvents": "9.0.6"}, "runtime": {"lib/net9.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}, "lib/net9.0/System.Private.Windows.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}}}, "System.Formats.Asn1/8.0.1": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.IO.Hashing/7.0.0": {"runtime": {"lib/net7.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.IO.Packaging/9.0.6": {"runtime": {"lib/net9.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.IO.Pipelines/8.0.0": {}, "System.IO.Ports/9.0.6": {"dependencies": {"runtime.native.System.IO.Ports": "9.0.6"}, "runtime": {"lib/net9.0/System.IO.Ports.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/unix/lib/net9.0/System.IO.Ports.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "runtimes/win/lib/net9.0/System.IO.Ports.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/1.6.6": {"runtime": {"lib/net9.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Management/9.0.6": {"dependencies": {"System.CodeDom": "9.0.6"}, "runtime": {"lib/net9.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Management.Automation/7.5.2": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.Management.Infrastructure": "3.0.0", "Microsoft.PowerShell.CoreCLR.Eventing": "7.5.2", "Microsoft.PowerShell.Native": "7.4.0", "Microsoft.Security.Extensions": "1.4.0", "Microsoft.Win32.Registry.AccessControl": "9.0.6", "Newtonsoft.Json": "13.0.3", "System.CodeDom": "9.0.6", "System.Configuration.ConfigurationManager": "9.0.6", "System.Diagnostics.DiagnosticSource": "9.0.6", "System.Diagnostics.EventLog": "9.0.6", "System.DirectoryServices": "9.0.6", "System.Management": "9.0.6", "System.Security.AccessControl": "6.0.1", "System.Security.Cryptography.Pkcs": "9.0.7", "System.Security.Cryptography.ProtectedData": "9.0.6", "System.Security.Permissions": "9.0.6", "System.Text.Encoding.CodePages": "9.0.6", "System.Windows.Extensions": "9.0.6"}, "runtimeTargets": {"runtimes/unix/lib/net9.0/System.Management.Automation.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}, "runtimes/win/lib/net9.0/System.Management.Automation.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.5.0.500", "fileVersion": "7.5.2.500"}}}, "System.Memory/4.6.0": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "9.0.6", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Net.Http/4.3.4": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "9.0.6", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Http.WinHttpHandler/9.0.6": {"runtime": {"lib/net9.0/System.Net.Http.WinHttpHandler.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Net.Http.WinHttpHandler.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Numerics.Vectors/4.5.0": {}, "System.Private.ServiceModel/4.10.3": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.ObjectPool": "8.0.17", "System.Numerics.Vectors": "4.5.0", "System.Reflection.DispatchProxy": "4.7.1", "System.Security.Cryptography.Xml": "9.0.7", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Private.ServiceModel.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}, "resources": {"lib/netstandard2.0/cs/System.Private.ServiceModel.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Private.ServiceModel.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Private.ServiceModel.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Private.ServiceModel.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Private.ServiceModel.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Private.ServiceModel.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Private.ServiceModel.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Private.ServiceModel.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Private.ServiceModel.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Private.ServiceModel.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Private.ServiceModel.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Private.ServiceModel.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Private.ServiceModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Private.Uri/4.3.2": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Reactive/5.0.0": {"runtime": {"lib/net5.0/System.Reactive.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.0.1"}}}, "System.Reactive.Linq/5.0.0": {"dependencies": {"System.Reactive": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/System.Reactive.Linq.dll": {"assemblyVersion": "3.0.6000.0", "fileVersion": "3.0.6000.0"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Context/9.0.6": {"runtime": {"lib/net9.0/System.Reflection.Context.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Reflection.DispatchProxy/4.7.1": {}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Metadata/8.0.1": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Reflection.TypeExtensions/4.7.0": {}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.Caching/9.0.6": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.6"}, "runtime": {"lib/net9.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.AccessControl/6.0.1": {}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Pkcs/9.0.7": {"runtime": {"lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/9.0.6": {"runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Xml/9.0.7": {"dependencies": {"System.Security.Cryptography.Pkcs": "9.0.7"}, "runtime": {"lib/net9.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "System.Security.Permissions/9.0.6": {"dependencies": {"System.Windows.Extensions": "9.0.6"}, "runtime": {"lib/net9.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.ServiceModel.Duplex/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Duplex.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Http/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Http.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.NetTcp/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Primitives/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}, "lib/net6.0/System.ServiceModel.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Security/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Security.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Syndication/9.0.6": {"runtime": {"lib/net9.0/System.ServiceModel.Syndication.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.ServiceProcess.ServiceController/9.0.6": {"dependencies": {"System.Diagnostics.EventLog": "9.0.6"}, "runtime": {"lib/net9.0/System.ServiceProcess.ServiceController.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.ServiceProcess.ServiceController.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Speech/9.0.6": {"runtime": {"lib/net9.0/System.Speech.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Speech.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encoding.CodePages/9.0.6": {}, "System.Text.Encodings.Web/9.0.6": {}, "System.Text.Json/8.0.5": {}, "System.Text.RegularExpressions/4.3.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.AccessControl/9.0.6": {"runtime": {"lib/net9.0/System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Threading.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Threading.RateLimiting/8.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Web.Services.Description/8.0.0": {"runtime": {"lib/netstandard2.0/System.Web.Services.Description.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.60704"}}, "resources": {"lib/netstandard2.0/cs/System.Web.Services.Description.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Web.Services.Description.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Web.Services.Description.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Web.Services.Description.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Web.Services.Description.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Web.Services.Description.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Web.Services.Description.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Web.Services.Description.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Web.Services.Description.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Web.Services.Description.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Web.Services.Description.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Web.Services.Description.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Web.Services.Description.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Windows.Extensions/9.0.6": {"runtime": {"lib/net9.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Tea/1.1.3": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Tea.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Tencent.QCloud.Cos.Sdk/5.4.34": {"runtime": {"lib/netstandard2.0/COSXML.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "TencentCloudSDK.Common/3.0.1273": {"dependencies": {"Newtonsoft.Json": "13.0.3", "System.Text.Encodings.Web": "9.0.6"}, "runtime": {"lib/netstandard2.0/TencentCloudCommon.dll": {"assemblyVersion": "3.0.1273.0", "fileVersion": "3.0.1273.0"}}}, "TencentCloudSDK.Sms/3.0.1273": {"dependencies": {"Newtonsoft.Json": "13.0.3", "System.Text.Encodings.Web": "9.0.6", "TencentCloudSDK.Common": "3.0.1273"}, "runtime": {"lib/netstandard2.0/TencentCloudSms.dll": {"assemblyVersion": "3.0.1273.0", "fileVersion": "3.0.1273.0"}}}, "UAParser/3.1.47": {"runtime": {"lib/netcoreapp2.0/UAParser.dll": {"assemblyVersion": "3.1.47.0", "fileVersion": "3.1.47.0"}}}, "XiHan.Framework/0.11.6": {"runtime": {"lib/net9.0/XiHan.Framework.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "XiHan.Framework.Utils/0.11.6": {"dependencies": {"BouncyCastle.Cryptography": "2.6.1", "XiHan.Framework": "0.11.6"}, "runtime": {"lib/net9.0/XiHan.Framework.Utils.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Yitter.IdGenerator/1.0.14": {"runtime": {"lib/netstandard2.0/Yitter.IdGenerator.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "ZstdSharp.Port/0.7.3": {"runtime": {"lib/net7.0/ZstdSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Admin.NET.Core/1.0.0": {"dependencies": {"AlibabaCloud.SDK.Dysmsapi20170525": "4.0.0", "AlipaySDKNet.Standard": "4.9.627", "AngleSharp": "1.3.0", "AspNet.Security.OAuth.Gitee": "9.4.0", "AspNet.Security.OAuth.Weixin": "9.4.0", "AspNetCoreRateLimit": "5.0.0", "AspectCore.Extensions.Reflection": "2.4.0", "BouncyCastle.Cryptography": "2.6.1", "Elastic.Clients.Elasticsearch": "9.0.7", "Furion.Extras.Authentication.JwtBearer": "*********", "Furion.Extras.ObjectMapper.Mapster": "*********", "Furion.Pure": "*********", "Hardware.Info": "*********", "Hashids.net": "1.7.0", "IPTools.China": "1.6.0", "IPTools.International": "1.6.0", "Lazy.Captcha.Core": "2.1.0", "Magicodes.IE.Excel": "2.7.6", "Magicodes.IE.Pdf": "2.7.6", "Magicodes.IE.Word": "2.7.6", "MailKit": "4.13.0", "Microsoft.AspNetCore.DataProtection.StackExchangeRedis": "9.0.7", "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson": "9.0.7", "Microsoft.AspNetCore.SignalR.StackExchangeRedis": "9.0.7", "Microsoft.PowerShell.SDK": "7.5.2", "MiniExcel": "1.41.3", "MiniWord": "0.9.2", "NewLife.Redis": "6.3.2025.701", "Novell.Directory.Ldap.NETStandard": "4.0.0", "OnceMi.AspNetCore.OSS": "1.2.0", "QRCoder": "1.6.0", "RabbitMQ.Client": "7.1.2", "SKIT.FlurlHttpClient.Wechat.Api": "3.11.0", "SKIT.FlurlHttpClient.Wechat.TenpayV3": "3.13.0", "SSH.NET": "2025.0.0", "SixLabors.ImageSharp.Web": "3.1.5", "SqlSugar.MongoDbCore": "*********", "SqlSugarCore": "*********", "System.Linq.Dynamic.Core": "1.6.6", "System.Net.Http": "4.3.4", "System.Private.Uri": "4.3.2", "TencentCloudSDK.Sms": "3.0.1273", "UAParser": "3.1.47", "XiHan.Framework.Utils": "0.11.6", "Yitter.IdGenerator": "1.0.14", "log4net": "3.1.0"}, "runtime": {"Admin.NET.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Admin.NET.Plugin.DingTalk/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AlibabaCloud.EndpointUtil/0.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-p/vsdJoVIfc1QREW4JX1tpzKdZZcFdw6/qfrylfcFXc0e2BDMQ2kPrv3nkyr2u+p4BF0PmOYl4EDqRtqLiBc+g==", "path": "alibabacloud.endpointutil/0.1.1", "hashPath": "alibabacloud.endpointutil.0.1.1.nupkg.sha512"}, "AlibabaCloud.GatewaySpi/0.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-vvmA5BrM8rpOZoXwFl/ZWHYLFnZ8EwPp+07z3Eeg9okLv58QJ8+KGWVovTr8tJpuDgPFG1SVdHXRi04A62ehdA==", "path": "alibabacloud.gatewayspi/0.0.3", "hashPath": "alibabacloud.gatewayspi.0.0.3.nupkg.sha512"}, "AlibabaCloud.OpenApiClient/0.1.14": {"type": "package", "serviceable": true, "sha512": "sha512-mmfl2eiPbfMVJBxLM075nMnDnEvMRP74kTdFJ1ud0RfSgZ6gb2wQJjkiT3Q4YVwVeFoiNZzEolM694zdRXe63Q==", "path": "alibabacloud.openapiclient/0.1.14", "hashPath": "alibabacloud.openapiclient.0.1.14.nupkg.sha512"}, "AlibabaCloud.OpenApiUtil/1.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-0Zd2UHm5gUND+7xNma2SAZEqm2Uy8dvAknLvCx72uDUCbOnfHWh+TsnUnNGMKUvmG3s/ZqxUA1UYIdp5BFCn5Q==", "path": "alibabacloud.openapiutil/1.1.2", "hashPath": "alibabacloud.openapiutil.1.1.2.nupkg.sha512"}, "AlibabaCloud.SDK.Dysmsapi20170525/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1zrpRz6X/9ZohWva2X2o5ddNel2x6ZBPGaBxAGodS2UXrELgqlBHhyv1kPLUkiloRwP51vR+WI4m8/AHSIeb0Q==", "path": "alibabacloud.sdk.dysmsapi20170525/4.0.0", "hashPath": "alibabacloud.sdk.dysmsapi20170525.4.0.0.nupkg.sha512"}, "AlibabaCloud.TeaUtil/0.1.19": {"type": "package", "serviceable": true, "sha512": "sha512-gjPboQEC3rSuS/8Ohk4VAw42W54h9NfIZxn4JIuWfoIF3k3mZxVdMJdKKOgIkNrx8YaLOthPSM3Pfb1zfOyFcw==", "path": "alibabacloud.teautil/0.1.19", "hashPath": "alibabacloud.teautil.0.1.19.nupkg.sha512"}, "AlibabaCloud.TeaXML/0.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-mHxE6H3eq4jaeqn3hryIYTI0k8quvPZfZdEE+PdU8rw+NGRvev68D8Aei6xjwW/pArZaTG6yPawYu5c0EaZkfw==", "path": "alibabacloud.teaxml/0.0.5", "hashPath": "alibabacloud.teaxml.0.0.5.nupkg.sha512"}, "AlipaySDKNet.Standard/4.9.627": {"type": "package", "serviceable": true, "sha512": "sha512-4Myu9cw0QUQeCIA/T08QBQ9gHtzpYTO/3LicET+VRhZ1999n2DmZSNYIclivxjmpEBusf8VNDzPlFx0I8vL61w==", "path": "alipaysdknet.standard/4.9.627", "hashPath": "alipaysdknet.standard.4.9.627.nupkg.sha512"}, "Aliyun.Credentials/1.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-fdWhSvNiXXIWz73eDZGZHesBhd5+St85W8CDaD9aP1ifBK+MZKx9YTL/6e6uLq0TN7j8J0IR5beXUXxohqjjjQ==", "path": "aliyun.credentials/1.5.0", "hashPath": "aliyun.credentials.1.5.0.nupkg.sha512"}, "Aliyun.OSS.SDK.NetCore/2.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-ElvJwTAdBqFmgb7K4PdxDXPFbOBCIUI5OvCOMfCoUoDL21aivtWMFUtU1v4Dxc2wcrN8XQdY1EKeGFhJK/zVyQ==", "path": "aliyun.oss.sdk.netcore/2.13.0", "hashPath": "aliyun.oss.sdk.netcore.2.13.0.nupkg.sha512"}, "AngleSharp/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-iHzfn4cK6CmhuURNdEpmSQCq5/HZFldEpkbnmqT9My8+6l2Sz3F+NxoqRA8z/jTkWB+SAu5boRdp4v/WtyjuIQ==", "path": "anglesharp/1.3.0", "hashPath": "anglesharp.1.3.0.nupkg.sha512"}, "AspectCore.Extensions.Reflection/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZQQ0vwV3OvhI8fipppLK+8PwwgeHSnXW1S/f0e6KGdzTHBJefrnMxdgF8xw08OC76BOK5/xeGJ4FKGJfK30H+g==", "path": "aspectcore.extensions.reflection/2.4.0", "hashPath": "aspectcore.extensions.reflection.2.4.0.nupkg.sha512"}, "AspNet.Security.OAuth.Gitee/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-KtJoGBuyEwJfzppfaJTlO4fikxBHeJ1GrgTHAYbvYTXZmBh+y2/wum7YosHuMehQm7MKRslPOfUgOVBTA9pVpw==", "path": "aspnet.security.oauth.gitee/9.4.0", "hashPath": "aspnet.security.oauth.gitee.9.4.0.nupkg.sha512"}, "AspNet.Security.OAuth.Weixin/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-simBDXk7wXeIkqAiNmztABFzIQnNHqCQulNJMSGIS6Y787fPckAKXT8G+oiokzRYIJgvj5PaE/IrvOypTc/6oA==", "path": "aspnet.security.oauth.weixin/9.4.0", "hashPath": "aspnet.security.oauth.weixin.9.4.0.nupkg.sha512"}, "AspNetCoreRateLimit/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6fq9+o1maGADUmpK/PvcF0DtXW2+7bSkIL7MDIo/agbIHKN8XkMQF4oze60DO731WaQmHmK260hB30FwPzCmEg==", "path": "aspnetcoreratelimit/5.0.0", "hashPath": "aspnetcoreratelimit.5.0.0.nupkg.sha512"}, "Azure.Core/1.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "path": "azure.core/1.38.0", "hashPath": "azure.core.1.38.0.nupkg.sha512"}, "Azure.Identity/1.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "path": "azure.identity/1.11.4", "hashPath": "azure.identity.1.11.4.nupkg.sha512"}, "BceSdkDotNetCore/1.0.2.911": {"type": "package", "serviceable": true, "sha512": "sha512-DNa0svdlYlgQABUo2rTSaXuQ6ZMrA/sqMV8dcuiRxEp0mAOkQyJdawwuxise05GmhrTbyysKfpw4l5rPcBMN0Q==", "path": "bcesdkdotnetcore/1.0.2.911", "hashPath": "bcesdkdotnetcore.1.0.2.911.nupkg.sha512"}, "Ben.Demystifier/0.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-axFeEMfmEORy3ipAzOXG/lE+KcNptRbei3F0C4kQCdeiQtW+qJW90K5iIovITGrdLt8AjhNCwk5qLSX9/rFpoA==", "path": "ben.demystifier/0.4.1", "hashPath": "ben.demystifier.0.4.1.nupkg.sha512"}, "BouncyCastle.Cryptography/2.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-vZsG2YILhthgRqO+ZVgRff4ZFKKTl0v7kqaVBLCtRvpREhfBP33pcWrdA3PRYgWuFL1RxiUFvjMUHTdBZlJcoA==", "path": "bouncycastle.cryptography/2.6.1", "hashPath": "bouncycastle.cryptography.2.6.1.nupkg.sha512"}, "CommunityToolkit.HighPerformance/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kgDi65k02hrgnHy3N0xENecsr0spW13RdIA1tipovi9t16gKziI7uZIu3qkxz0GctCHNM4hfeqXYg//6wHJ6Kw==", "path": "communitytoolkit.highperformance/8.1.0", "hashPath": "communitytoolkit.highperformance.8.1.0.nupkg.sha512"}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "DocumentFormat.OpenXml/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-2z9QBzeTLNNKWM9SaOSDMegfQk/7hDuElOsmF77pKZMkFRP/GHA/W/4yOAQD9kn15N/FsFxHn3QVYkatuZghiA==", "path": "documentformat.openxml/3.1.1", "hashPath": "documentformat.openxml.3.1.1.nupkg.sha512"}, "DocumentFormat.OpenXml.Framework/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-6APEp/ElZV58S/4v8mf4Ke3ONEDORs64MqdD64Z7wWpcHANB9oovQsGIwtqjnKihulOj7T0a6IxHIHOfMqKOng==", "path": "documentformat.openxml.framework/3.1.1", "hashPath": "documentformat.openxml.framework.3.1.1.nupkg.sha512"}, "DynamicExpresso.Core/2.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-p6GEP3BphaT9xa59VjpQeozkloXjcDmoL6aPXOInl5S5chWtB82H+GiirV3H1bP39ZeXX2e1UN0w7/pD1wCUlg==", "path": "dynamicexpresso.core/2.3.3", "hashPath": "dynamicexpresso.core.2.3.3.nupkg.sha512"}, "Elastic.Clients.Elasticsearch/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-EdLRCO+/5Q5O2HUQ3fGOnH190/2hin8ykiM/O3gm40jmHs8pRqAbGER+MFZeWU4KoZQklJ1s3Ei/rpeiw4OK1A==", "path": "elastic.clients.elasticsearch/9.0.7", "hashPath": "elastic.clients.elasticsearch.9.0.7.nupkg.sha512"}, "Elastic.Transport/0.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-evL2QL9tsjsWMuV/JAv4EyJmG0S//Np9z35qpWWn70+FiGiWYYsHgZEm7Vs4ad/CQh6YGMZcJ/G3len6+LzjUA==", "path": "elastic.transport/0.9.2", "hashPath": "elastic.transport.0.9.2.nupkg.sha512"}, "Flurl/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rpts69yYgvJqg6PPgqShBQEZ4aNzWQqWpWppcT0oDWxDCIsBqiod4pj6LQZdhk+1OozLFagemldMRACdHF3CsA==", "path": "flurl/4.0.0", "hashPath": "flurl.4.0.0.nupkg.sha512"}, "Flurl.Http/4.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-9vCqFFyceA11yplkFD8AbCFFTvG1Lrw3tpsgOpL5sLUc28p6zcvGszNleuT6nDymRvtt5eS+rqUX+bRztg1fhA==", "path": "flurl.http/4.0.2", "hashPath": "flurl.http.4.0.2.nupkg.sha512"}, "Furion.Extras.Authentication.JwtBearer/*********": {"type": "package", "serviceable": true, "sha512": "sha512-egTNJ5x2GFn0PEltbwKDvXTxvPsNuny2xTetATLn6TZtR9KadF5iHya+dhoUzZ7vWk3YDRuBAfe3qmlr5pMr0Q==", "path": "furion.extras.authentication.jwtbearer/*********", "hashPath": "furion.extras.authentication.jwtbearer.*********.nupkg.sha512"}, "Furion.Extras.ObjectMapper.Mapster/*********": {"type": "package", "serviceable": true, "sha512": "sha512-iYZB4StMBsIcwtmkyWY/ZA8375YkUv9TwGZvBpdhLSue3+XrayK6rgW2M9wrTHpMqzKHGdfGf5U7NO1abvbIkQ==", "path": "furion.extras.objectmapper.mapster/*********", "hashPath": "furion.extras.objectmapper.mapster.*********.nupkg.sha512"}, "Furion.Pure/*********": {"type": "package", "serviceable": true, "sha512": "sha512-mrdvrSEg71Z9z+k8s/gfKp8SlB+b0qN0ffhE1ywj8Ub5zCGGtLKtOx9Et0p0iCu3+JhjNTejfywx1bjym4XXHw==", "path": "furion.pure/*********", "hashPath": "furion.pure.*********.nupkg.sha512"}, "Furion.Pure.Extras.DependencyModel.CodeAnalysis/*********": {"type": "package", "serviceable": true, "sha512": "sha512-N9ol2zndPjWu0yULO4mgoWpX62pkOFSddcYytFwLZI6J+OG1+w7rYVUc67muiMeD+4ta3cfv5SmX2+aABbJ4hg==", "path": "furion.pure.extras.dependencymodel.codeanalysis/*********", "hashPath": "furion.pure.extras.dependencymodel.codeanalysis.*********.nupkg.sha512"}, "Hardware.Info/*********": {"type": "package", "serviceable": true, "sha512": "sha512-ueI3NbMoOL9FVb2Sq0evJihk7Pa9lmJUaxUTaSs2CA0N5LIVzOJf/qHDIEy10GdJKJJl20HJuENKeNfonawuuw==", "path": "hardware.info/*********", "hashPath": "hardware.info.*********.nupkg.sha512"}, "Hashids.net/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-TuQ8ns5c+eeIOHSgJdwCLb0nWzKqn2gCPSLFoz6nWdhw3tS5Wa43qrwRXT4UJT7XVgHqKssacVU1SGlAOWjeIg==", "path": "hashids.net/1.7.0", "hashPath": "hashids.net.1.7.0.nupkg.sha512"}, "Haukcode.WkHtmlToPdfDotNet/1.5.95": {"type": "package", "serviceable": true, "sha512": "sha512-Eiwfe3O+cqqiMxpqYIrt+KbBCkafDc0k9tBBYhIAIBIS1FMJQ5FmlRcf8ElEUhQpIAkn2w5gF4/dYl0+M+cETQ==", "path": "haukcode.wkhtmltopdfdotnet/1.5.95", "hashPath": "haukcode.wkhtmltopdfdotnet.1.5.95.nupkg.sha512"}, "HtmlToOpenXml.dll/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-STr/YomlvHDQky9ilIg6I9dbIwSw7dimDfj1fFo+FFz4n56x9nT18+MB68pBlwBqTOVCKQ2oE2FYBOVJ+rCIrA==", "path": "htmltoopenxml.dll/3.1.0", "hashPath": "htmltoopenxml.dll.3.1.0.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "IP2Region.Ex/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-B8TxhuAw72cPwjgf8pqDf62l1TJL0jXw4J13fXHg4Igq1OwT7SRotQX6gN6puyIgHVYLtKxnmhFf60oUITxmxA==", "path": "ip2region.ex/1.2.0", "hashPath": "ip2region.ex.1.2.0.nupkg.sha512"}, "IPTools.China/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-12VnC92ffiKlLRwr5Ay3uFvZMCB9SDNn77sVlNycQu1OJAunnuCNBOVZTkg9D2UL2cc+iMwra6if9viXhrrt7w==", "path": "iptools.china/1.6.0", "hashPath": "iptools.china.1.6.0.nupkg.sha512"}, "IPTools.Core/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-qO+EY5vEwLKtOkQD1aMweM9pIVSFLwTi/Z2ZnX08qBXI4yPdRuguJJvzT2YVk2Addv999A+bWifIS8ahiwJcTg==", "path": "iptools.core/1.6.0", "hashPath": "iptools.core.1.6.0.nupkg.sha512"}, "IPTools.International/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-3MWjpKGDMDTfAOO5m3+CFvbgIEvSKCPTIP2V96xLlSB1rTkLgf60QIPO0babYic3gU2nf8zKgp2/UGpIgGlKQQ==", "path": "iptools.international/1.6.0", "hashPath": "iptools.international.1.6.0.nupkg.sha512"}, "Json.More.Net/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-izscdjjk8EAHDBCjyz7V7n77SzkrSjh/hUGV6cyR6PlVdjYDh5ohc8yqvwSqJ9+6Uof8W6B24dIHlDKD+I1F8A==", "path": "json.more.net/2.0.2", "hashPath": "json.more.net.2.0.2.nupkg.sha512"}, "JsonPointer.Net/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-H/OtixKadr+ja1j7Fru3WG56V9zP0AKT1Bd0O7RWN/zH1bl8ZIwW9aCa4+xvzuVvt4SPmrvBu3G6NpAkNOwNAA==", "path": "jsonpointer.net/5.0.2", "hashPath": "jsonpointer.net.5.0.2.nupkg.sha512"}, "JsonSchema.Net/7.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-O3KclMcPVFYTZsTeZBpwtKd/lYrNc3AFR+xi9j3Q4CfhDufOUx25TMMWJOcFRrqVklvKQ4Kl+0UhlNX1iDGoRw==", "path": "jsonschema.net/7.2.3", "hashPath": "jsonschema.net.7.2.3.nupkg.sha512"}, "Lazy.Captcha.Core/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-hdsnycZ01/FsmH7gkdd4wOglRwHjAZ1uPZDlNg1Kl4TirzyTP78w4Pw5jUESAn9sVfz/Gq7HsgCQ+6EGo7I3+g==", "path": "lazy.captcha.core/2.1.0", "hashPath": "lazy.captcha.core.2.1.0.nupkg.sha512"}, "log4net/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-GT7ZYyNcBNbMENUSSQsH8HkjrELe55UOwOkxMvUBfoSyq/K/c1SPi5aXCNHFqpyeCPlrq8nVx40z9pBVwnqkmA==", "path": "log4net/3.1.0", "hashPath": "log4net.3.1.0.nupkg.sha512"}, "Magicodes.IE.Core/2.7.6": {"type": "package", "serviceable": true, "sha512": "sha512-HX6Sm1ifcl/y8rMt29O31AICoI944xp00pcXkc0MWs0WfxXRO845QRjQUfYdF32dybAtTvmbfa8XVAkHyykMjg==", "path": "magicodes.ie.core/2.7.6", "hashPath": "magicodes.ie.core.2.7.6.nupkg.sha512"}, "Magicodes.IE.EPPlus/2.7.6": {"type": "package", "serviceable": true, "sha512": "sha512-38brikUY1ULQj2RDdhqw6/tnxhBFTFizRadfnwzoQLaT29JDhk83x9OjVXExoPSi55zo/3uMtswlY+FQ8tXm7A==", "path": "magicodes.ie.epplus/2.7.6", "hashPath": "magicodes.ie.epplus.2.7.6.nupkg.sha512"}, "Magicodes.IE.Excel/2.7.6": {"type": "package", "serviceable": true, "sha512": "sha512-YekKRHrHpFRIyvDt/amex97fjH8/8jvHttw6plP3zpUEBVXXUkfCE9QsUMoturmgqYVKJQgLiD8gTrBXYwlOkw==", "path": "magicodes.ie.excel/2.7.6", "hashPath": "magicodes.ie.excel.2.7.6.nupkg.sha512"}, "Magicodes.IE.Html/2.7.6": {"type": "package", "serviceable": true, "sha512": "sha512-O7z3/ZjgjCSCqri5zoWh8Rh3TIEnezbTgxOsDOczqusc9Sby1AuzODtnCUzDsNGvTOhyol3LSamSD8koAvSpuQ==", "path": "magicodes.ie.html/2.7.6", "hashPath": "magicodes.ie.html.2.7.6.nupkg.sha512"}, "Magicodes.IE.Pdf/2.7.6": {"type": "package", "serviceable": true, "sha512": "sha512-ZWzsHjR78u8YgJgINHYdFfWTBMclo+mtKCFeIzbbFATSVaCupi/8eC4ejv99R2rtuVXFZcdClAbPfF6Q5q5Fsw==", "path": "magicodes.ie.pdf/2.7.6", "hashPath": "magicodes.ie.pdf.2.7.6.nupkg.sha512"}, "Magicodes.IE.Word/2.7.6": {"type": "package", "serviceable": true, "sha512": "sha512-5sIiw1oZW8FcTIEQuhFyu2+u48WMagyDPp7tJjV6JfB8uBW4KIEX/tdYGGbI1P6mYp96SzJRSRNPcR387pcVeg==", "path": "magicodes.ie.word/2.7.6", "hashPath": "magicodes.ie.word.2.7.6.nupkg.sha512"}, "Magicodes.RazorEngine.NetCore/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Cz7MMFjZKmdeYCJKlmtO1lpsR+3tzyScZhX+SZfXCxglp2OoIUD2zB3IyFm1+FQg2Ed1R1nLinSoVk0Zv9oLgg==", "path": "magicodes.razorengine.netcore/2.2.0", "hashPath": "magicodes.razorengine.netcore.2.2.0.nupkg.sha512"}, "MailKit/4.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-GsepEHKkaQvbAuBizlhz93yc0ihJWzVCfoerfnpCeqiKLeS6gsTKInYy3/U2wqgkGE62TKs5OKS1a90pyc+j4g==", "path": "mailkit/4.13.0", "hashPath": "mailkit.4.13.0.nupkg.sha512"}, "Mapster/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-RYGoDqvS4WTKIq0HDyPBBVIj6N0mluOCXQ1Vk95JKseMHEsbCXSEGKSlP95oL+s42IXAXbqvHj7p0YaRBUcfqg==", "path": "mapster/7.4.0", "hashPath": "mapster.7.4.0.nupkg.sha512"}, "Mapster.Core/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-11lokmfliBEMMmjeqxFGNpqGXq6tN96zFqpBmfYeahr4Ybk63oDmeJmOflsATjobYkX248g5Y62oQ2NNnZaeww==", "path": "mapster.core/1.2.1", "hashPath": "mapster.core.1.2.1.nupkg.sha512"}, "Mapster.DependencyInjection/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-LfjnRIwx6WAo3ssq8PFeaHFaUz00BfSG9BhWgXsiDa3H5lDhG0lpMGDF6w2ZnooS4eHYmAv4f77VxmzpvgorNg==", "path": "mapster.dependencyinjection/1.0.1", "hashPath": "mapster.dependencyinjection.1.0.1.nupkg.sha512"}, "Markdig.Signed/0.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-zfi6kNm5QJnsCGm5a0hMG2qw8juYbOfsS4c1OuTcqkbYQUCdkam6d6Nt7nPIrbV4D+U7sHChidSQlg+ViiMPuw==", "path": "markdig.signed/0.38.0", "hashPath": "markdig.signed.0.38.0.nupkg.sha512"}, "MaxMind.Db/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-3x7q8Wir8Jnrl0j4Pw00XeptEH+IpbjNS2FQJqRD/CKDPFhdkUxRNlCKd4pdpUXvYeVRr0Iy0fWkoXORJB0vCw==", "path": "maxmind.db/2.4.0", "hashPath": "maxmind.db.2.4.0.nupkg.sha512"}, "MaxMind.GeoIP2/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-O+4jtK2fA/vLXqsx7RxsaCCQfSUogYfmlFJeh3HocREXz6vs4CFjSg6m0/6CdtaUAO4dGMBjm4utlwmTI1b3jQ==", "path": "maxmind.geoip2/3.0.0", "hashPath": "maxmind.geoip2.3.0.0.nupkg.sha512"}, "MessagePack/2.5.187": {"type": "package", "serviceable": true, "sha512": "sha512-uW4j8m4Nc+2Mk5n6arOChavJ9bLjkis0qWASOj2h2OwmfINuzYv+mjCHUymrYhmyyKTu3N+ObtTXAY4uQ7jIhg==", "path": "messagepack/2.5.187", "hashPath": "messagepack.2.5.187.nupkg.sha512"}, "MessagePack.Annotations/2.5.187": {"type": "package", "serviceable": true, "sha512": "sha512-/IvvMMS8opvlHjEJ/fR2Cal4Co726Kj77Z8KiohFhuHfLHHmb9uUxW5+tSCL4ToKFfkQlrS3HD638mRq83ySqA==", "path": "messagepack.annotations/2.5.187", "hashPath": "messagepack.annotations.2.5.187.nupkg.sha512"}, "Microsoft.ApplicationInsights/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-3AOM9bZtku7RQwHyMEY3tQMrHIgjcfRDa6YQpd/QG2LDGvMydSlL9Di+8LLMt7J2RDdfJ7/2jdYv6yHcMJAnNw==", "path": "microsoft.applicationinsights/2.22.0", "hashPath": "microsoft.applicationinsights.2.22.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bs+1Pq3vQdS2lTyxNUd9fEhtMsq3eLUpK36k2t56iDMVrk6OrAoFtvrQrTK0Y0OetTcJrUkGU7hBlf+ORzHLqQ==", "path": "microsoft.aspnetcore.authentication.jwtbearer/9.0.0", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-D/5amxI2QDtH4M0sll5o9LhlDS5KL6SJgxFYx3WBcSNNn24YZ8aIaJ28B9gHIeFK+8opB9v89BceYu++WuRVTQ==", "path": "microsoft.aspnetcore.connections.abstractions/9.0.7", "hashPath": "microsoft.aspnetcore.connections.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-5di3GKY/a9ceGAdVk78QFGMO55NrFRN1rnh4xNr7MHjOCOMhgcWkyv9051wkPUDfX+g2cNN6+ckHvNnlxUwc2A==", "path": "microsoft.aspnetcore.cryptography.internal/9.0.7", "hashPath": "microsoft.aspnetcore.cryptography.internal.9.0.7.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-uN2Wifo9gzwpivoygO2HvLUecLBUbnJslJRNHyHPSQhhoRF6IShg5L/zLVo27McSqWM9HQxO7lNJhwUqRCQIaQ==", "path": "microsoft.aspnetcore.dataprotection/9.0.7", "hashPath": "microsoft.aspnetcore.dataprotection.9.0.7.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-X7t3R2UOTZrATuIDj4kYbovhDuYtjPe1WOtoJVpd3vcJFv6lsn2ZuI4MvkEqE5oXsUfLlH45oNyxpBYZSqFCjg==", "path": "microsoft.aspnetcore.dataprotection.abstractions/9.0.7", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.StackExchangeRedis/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-HHuBZMeCgBs7xEoKw+V5jliVBgodlcgIiIc7M/5kwHcUcuR+FGbe78xrav9aAvOEYBe8jiHVwocafecjN6/fzw==", "path": "microsoft.aspnetcore.dataprotection.stackexchangeredis/9.0.7", "hashPath": "microsoft.aspnetcore.dataprotection.stackexchangeredis.9.0.7.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y4rs5aMEXY8G7wJo5S3EEt6ltqyOTr/qOeZzfn+hw/fuQj5GppGckMY5psGLETo1U9hcT5MmAhaT5xtusM1b5g==", "path": "microsoft.aspnetcore.html.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.html.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/4UONYoAIeexPoAmbzBPkVGA6KAY7t0BM+1sr0fKss2V1ERCdcM+Llub4X5Ma+LJ60oPp6KzM0e3j+Pp/JHCNw==", "path": "microsoft.aspnetcore.jsonpatch/9.0.0", "hashPath": "microsoft.aspnetcore.jsonpatch.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTFDEmZi3GheCSPrBxzyE63+d5unln2vYldo/nOm1xet/4rpEk2oJYcwpclPQ13E+LZBF9XixkgwYTUwqznlWg==", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/9.0.0", "hashPath": "microsoft.aspnetcore.mvc.newtonsoftjson.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V54PIyDCFl8COnTp9gezNHpUNHk7F9UnerGeZy3UfbnwYvfzbo+ipqQmSgeoESH8e0JvKhRTyQyZquW2EPtCmg==", "path": "microsoft.aspnetcore.razor/2.2.0", "hashPath": "microsoft.aspnetcore.razor.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-n5Mg5D0aRrhHJJ6bJcwKqQydIFcgUq0jTlvuynoJjwA2IvAzh8Aqf9cpYagofQbIlIXILkCP6q6FgbngyVtpYA==", "path": "microsoft.aspnetcore.razor.language/6.0.36", "hashPath": "microsoft.aspnetcore.razor.language.6.0.36.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7YqK+H61lN6yj9RiQUko7oaOhKtRR9Q/kBcoWNRemhJdTIWOh1OmdvJKzZrMWOlff3BAjejkPQm+0V0qXk+B1w==", "path": "microsoft.aspnetcore.razor.runtime/2.2.0", "hashPath": "microsoft.aspnetcore.razor.runtime.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-3VDOgFVkvPH/J6YIV/JPPTojA+zNNGyOZfH+nFqEDqc6kXWHEamrddlIeH0+u31rRa9Ie5CRoe6mLQndJjdRyQ==", "path": "microsoft.aspnetcore.signalr.common/9.0.7", "hashPath": "microsoft.aspnetcore.signalr.common.9.0.7.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-M3Vdz/VSxScjOuaWhY57MFywNwvNex0wzaAWuqA+hBE/ZbJroQ7/eCLME3JJGi6jRS3qcxI9X3hAlKCsGwgVww==", "path": "microsoft.aspnetcore.signalr.protocols.newtonsoftjson/9.0.7", "hashPath": "microsoft.aspnetcore.signalr.protocols.newtonsoftjson.9.0.7.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.StackExchangeRedis/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-GJ2hqXstY26oEzLBkQKf5359absp9Kk3VultL+FmOUrSFBr9scs7MglZ8tTOO//TYLDQQkLHJBXuZJtMATw8uA==", "path": "microsoft.aspnetcore.signalr.stackexchangeredis/9.0.7", "hashPath": "microsoft.aspnetcore.signalr.stackexchangeredis.9.0.7.nupkg.sha512"}, "Microsoft.AspNetCore.StaticFiles/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-byZDrjir6Co5EoWbraQyG0qbPCUG6XgGYQstipMF9lucOAjq/mqnIyt8B8iMWnin/ghZoOln9Y01af4rUAwOhA==", "path": "microsoft.aspnetcore.staticfiles/2.2.0", "hashPath": "microsoft.aspnetcore.staticfiles.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-v/EW3UE8/lbEYHoC2Qq7AR/DnmvpgdtAMndfQNmpuIMx/Mto8L5JnuCfdBYtgvalQOtfNCnxFejxuRrryvUTsg==", "path": "microsoft.codeanalysis.analyzers/3.11.0", "hashPath": "microsoft.codeanalysis.analyzers.3.11.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-djf8ujmqYImFgB04UGtcsEhHrzVqzHowS+EEl/Yunc5LdrYrZhGBWUTXoCF0NzYXJxtfuD+UVQarWpvrNc94Qg==", "path": "microsoft.codeanalysis.common/4.11.0", "hashPath": "microsoft.codeanalysis.common.4.11.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-6XYi2EusI8JT4y2l/F3VVVS+ISoIX9nqHsZRaG6W5aFeJ5BEuBosHfT/ABb73FN0RZ1Z3cj2j7cL28SToJPXOw==", "path": "microsoft.codeanalysis.csharp/4.11.0", "hashPath": "microsoft.codeanalysis.csharp.4.11.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-mtoeRMh7F/OA536c/Cnh8L4H0uLSKB5kSmoi54oN7Fp0hNJDy22IqyMhaMH4PkDCqI7xL//Fvg9ldtuPHG0h5g==", "path": "microsoft.data.sqlclient/5.2.2", "hashPath": "microsoft.data.sqlclient.5.2.2.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-po1jhvFd+8pbfvJR/puh+fkHi0GRanAdvayh/0e47yaM6CXWZ6opUjCMFuYlAnD2LcbyvQE7fPJKvogmaUcN+w==", "path": "microsoft.data.sqlclient.sni.runtime/5.2.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.2.0.nupkg.sha512"}, "Microsoft.Data.Sqlite/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lw6wthgXGx3r/U775k1UkUAWIn0kAT0wj4ZRq0WlhPx4WAOiBsIjgDKgWkXcNTGT0KfHiClkM+tyPVFDvxeObw==", "path": "microsoft.data.sqlite/9.0.0", "hashPath": "microsoft.data.sqlite.9.0.0.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cFfZjFL+tqzGYw9lB31EkV1IWF5xRQNk2k+MQd+Cf86Gl6zTeAoiZIFw5sRB1Z8OxpEC7nu+nTDsLSjieBAPTw==", "path": "microsoft.data.sqlite.core/9.0.0", "hashPath": "microsoft.data.sqlite.core.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Kzzf7pRey40VaUkHN9/uWxrKVkLu2AQjt+GVeeKLLpiEHAJ1xZRsLSh4ZZYEnyS7Kt2OBOPmsXNdU+wbcOl5w==", "path": "microsoft.extensions.apidescription.server/9.0.0", "hashPath": "microsoft.extensions.apidescription.server.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FPWZAa9c0H4dvOj351iR1jkUIs4u9ykL4Bm592yhjDyO5lCoWd+TMAHx2EMbarzUvCvgjWjJIoC6//Q9kH6YhA==", "path": "microsoft.extensions.caching.abstractions/9.0.0", "hashPath": "microsoft.extensions.caching.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zbnPX/JQ0pETRSUG9fNPBvpIq42Aufvs15gGYyNIMhCun9yhmWihz0WgsI7bSDPjxWTKBf8oX/zv6v2uZ3W9OQ==", "path": "microsoft.extensions.caching.memory/9.0.0", "hashPath": "microsoft.extensions.caching.memory.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-lut/kiVvNsQ120VERMUYSFhpXPpKjjql+giy03LesASPBBcC0o6+aoFdzJH9GaYpFTQ3fGVhVjKjvJDoAW5/IQ==", "path": "microsoft.extensions.configuration.abstractions/9.0.7", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RiScL99DcyngY9zJA2ROrri7Br8tn5N4hP4YNvGdTN/bvg1A3dwvDOxHnNZ3Im7x2SJ5i4LkX1uPiR/MfSFBLQ==", "path": "microsoft.extensions.configuration.binder/9.0.0", "hashPath": "microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-iPK1FxbGFr2Xb+4Y+dTYI8Gupu9pOi8I3JPuPsrogUmEhe2hzZ9LpCmolMEBhVDo2ikcSr7G5zYiwaapHSQTew==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.7", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-saxr2XzwgDU77LaQfYFXmddEDRUKHF4DaGMZkNB3qjdVSZlax3//dGJagJkKrGMIPNZs2jVFXITyCCR6UHJNdA==", "path": "microsoft.extensions.dependencymodel/9.0.0", "hashPath": "microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-d39Ov1JpeWCGLCOTinlaDkujhrSAQ0HFxb7Su1BjhCKBfmDcQ6Ia1i3JI6kd3NFgwi1dexTunu82daDNwt7E6w==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.7", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Features/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-6aCgOvUEoeuWvQIx5Wb0qYM7ZG9X3te2beHu85jvdW0eDTAw8NO9+zaaQ7aBDF6KvwQAAa/37mjDcJY3LI24Yw==", "path": "microsoft.extensions.features/9.0.7", "hashPath": "microsoft.extensions.features.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-y9djCca1cz/oz/J8jTxtoecNiNvaiGBJeWd7XOPxonH+FnfHqcfslJMcSr5JMinmWFyS7eh3C9L6m6oURZ5lSA==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.7", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-yG2JCXAR+VqI1mKqynLPNJlNlrUJeEISEpX4UznOp2uM4IEFz3pDDauzyMvTjICutEJtOigJ1yWBvxbaIlibBw==", "path": "microsoft.extensions.hosting.abstractions/9.0.7", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-sMM6NEAdUTE/elJ2wqjOi0iBWqZmSyaTByLF9e8XHv6DRJFFnOe0N+s8Uc6C91E4SboQCfLswaBIZ+9ZXA98AA==", "path": "microsoft.extensions.logging.abstractions/9.0.7", "hashPath": "microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/8.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-oJ5DKTHz+tOypwwppLmtNaffUVDg9ouMndOdnERRRv2BeipuoLTW7i9HIbztGDU7fs/th9wFSJWM5ELw1GiGtA==", "path": "microsoft.extensions.objectpool/8.0.17", "hashPath": "microsoft.extensions.objectpool.8.0.17.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-trJnF6cRWgR5uMmHpGoHmM1wOVFdIYlELlkO9zX+RfieK0321Y55zrcs4AaEymKup7dxgEN/uJU25CAcMNQRXw==", "path": "microsoft.extensions.options/9.0.7", "hashPath": "microsoft.extensions.options.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ob3FXsXkcSMQmGZi7qP07EQ39kZpSBlTcAZLbJLdI4FIf0Jug8biv2HTavWmnTirchctPlq9bl/26CXtQRguzA==", "path": "microsoft.extensions.options.configurationextensions/9.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-ti/zD9BuuO50IqlvhWQs9GHxkCmoph5BHjGiWKdg2t6Or8XoyAfRJiKag+uvd/fpASnNklfsB01WpZ4fhAe0VQ==", "path": "microsoft.extensions.primitives/9.0.7", "hashPath": "microsoft.extensions.primitives.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.WebEncoders/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8XcqYcpcdBAxUhLeyYcuKmxu4CtNQA9IphTnARpQGhkop4A93v2XgM3AtaVVJo3H2cDWxWM6aeO8HxkifREqw==", "path": "microsoft.extensions.webencoders/2.2.0", "hashPath": "microsoft.extensions.webencoders.2.2.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "path": "microsoft.identity.client/4.61.3", "hashPath": "microsoft.identity.client.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "path": "microsoft.identity.client.extensions.msal/4.61.3", "hashPath": "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OtlIWcyX01olfdevPKZdIPfBEvbcioDyBiE/Z2lHsopsMD7twcKtlN9kMevHmI5IIPhFpfwCIiR6qHQz1WHUIw==", "path": "microsoft.identitymodel.abstractions/8.0.1", "hashPath": "microsoft.identitymodel.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s6++gF9x0rQApQzOBbSyp4jUaAlwm+DroKfL8gdOHxs83k8SJfUXhuc46rDB3rNXBQ1MVRxqKUrqFhO/M0E97g==", "path": "microsoft.identitymodel.jsonwebtokens/8.0.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UCPF2exZqBXe7v/6sGNiM6zCQOUXXQ9+v5VTb9gPB8ZSUPnX53BxlN78v2jsbIvK9Dq4GovQxo23x8JgWvm/Qg==", "path": "microsoft.identitymodel.logging/8.0.1", "hashPath": "microsoft.identitymodel.logging.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uA2vpKqU3I2mBBEaeJAWPTjT9v1TZrGWKdgK6G5qJd03CLx83kdiqO9cmiK8/n1erkHzFBwU/RphP83aAe3i3g==", "path": "microsoft.identitymodel.protocols/8.0.1", "hashPath": "microsoft.identitymodel.protocols.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "path": "microsoft.identitymodel.protocols.openidconnect/8.0.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kDimB6Dkd3nkW2oZPDkMkVHfQt3IDqO5gL0oa8WVy3OP4uE8Ij+8TXnqg9TOd9ufjsY3IDiGz7pCUbnfL18tjg==", "path": "microsoft.identitymodel.tokens/8.0.1", "hashPath": "microsoft.identitymodel.tokens.8.0.1.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s/s20YTVY9r9TPfTrN5g8zPF1YhwxyqO6PxUkrYTGI2B+OGPe9AdajWZrLhFqXIvqIW23fnUE4+ztrUWNU1+9g==", "path": "microsoft.io.recyclablememorystream/3.0.1", "hashPath": "microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512"}, "Microsoft.Management.Infrastructure/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cGZi0q5IujCTVYKo9h22Pw+UwfZDV82HXO8HTxMG2HqntPlT3Ls8jY6punLp4YzCypJNpfCAu2kae3TIyuAiJw==", "path": "microsoft.management.infrastructure/3.0.0", "hashPath": "microsoft.management.infrastructure.3.0.0.nupkg.sha512"}, "Microsoft.Management.Infrastructure.CimCmdlets/7.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-N6+cG+hi9rrXXof2LVUBLKdHPU6mfEED9NoeYFppYNe1limqZv0vkvAybX3Exfndzd9vbZc1hgDM/cLcqCXpQA==", "path": "microsoft.management.infrastructure.cimcmdlets/7.5.2", "hashPath": "microsoft.management.infrastructure.cimcmdlets.7.5.2.nupkg.sha512"}, "Microsoft.Management.Infrastructure.Runtime.Unix/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QZE3uEDvZ0m7LabQvcmNOYHp7v1QPBVMpB/ild0WEE8zqUVAP5y9rRI5we37ImI1bQmW5pZ+3HNC70POPm0jBQ==", "path": "microsoft.management.infrastructure.runtime.unix/3.0.0", "hashPath": "microsoft.management.infrastructure.runtime.unix.3.0.0.nupkg.sha512"}, "Microsoft.Management.Infrastructure.Runtime.Win/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwMyWN33+iQ8Wm/n1yoPXgFoiYNd0HzJyoqSVhaQZyJfaQrJR3udgcIHjqa1qbc3lS6kvfuUMN4TrF4U4refCQ==", "path": "microsoft.management.infrastructure.runtime.win/3.0.0", "hashPath": "microsoft.management.infrastructure.runtime.win.3.0.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NET.StringTools/17.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-N0ZIanl1QCgvUumEL1laasU0a7sOE5ZwLZVTn0pAePnfhq8P7SvTjF8Axq+CnavuQkmdQpGNXQ1efZtu5kDFbA==", "path": "microsoft.net.stringtools/17.6.3", "hashPath": "microsoft.net.stringtools.17.6.3.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-TMBuzAHpTenGbGgk0SMTwyEkyijY/Eae4ZGsFNYJvAr/LDn1ku3Etp3FPxChmDp5HHF3kzJuoaa08N0xjqAJfQ==", "path": "microsoft.netcore.platforms/1.1.1", "hashPath": "microsoft.netcore.platforms.1.1.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.OpenApi/1.6.23": {"type": "package", "serviceable": true, "sha512": "sha512-tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g==", "path": "microsoft.openapi/1.6.23", "hashPath": "microsoft.openapi.1.6.23.nupkg.sha512"}, "Microsoft.PowerShell.Commands.Diagnostics/7.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-bSupgqPjrqernPSnCUqaxilv9r2/HmqhilaxwbKSri2N7vyiO1Y55bzXR4i5MdLF5EhPaORMFN9lRLItyZAcQQ==", "path": "microsoft.powershell.commands.diagnostics/7.5.2", "hashPath": "microsoft.powershell.commands.diagnostics.7.5.2.nupkg.sha512"}, "Microsoft.PowerShell.Commands.Management/7.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-kc/b+wzGTOs8/2PCeaCuF0sWdaMzOJ4oPCiVFb6gXijEEm1FnkhPltVdV2ook70+pfH5qLWOSe4Vq7p69590lw==", "path": "microsoft.powershell.commands.management/7.5.2", "hashPath": "microsoft.powershell.commands.management.7.5.2.nupkg.sha512"}, "Microsoft.PowerShell.Commands.Utility/7.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-8rDW1QZDBbIeWPF/8d1JMgMnJzoi3LhuyAoFaa3se+1fgI5BVNIF2R795RBJLqPf4jOwJC7RK3PTLsGTEPZsbA==", "path": "microsoft.powershell.commands.utility/7.5.2", "hashPath": "microsoft.powershell.commands.utility.7.5.2.nupkg.sha512"}, "Microsoft.PowerShell.ConsoleHost/7.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-2kEYdYAl6gvUV8G8i5DkX/BMh1xo3gVCdptkZWXo+b1ciKi8uB3U0s58NEWHQhWSW4Hr+VPatipOzId0aVvx5g==", "path": "microsoft.powershell.consolehost/7.5.2", "hashPath": "microsoft.powershell.consolehost.7.5.2.nupkg.sha512"}, "Microsoft.PowerShell.CoreCLR.Eventing/7.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-HEGIFgdH00wDi8c2Eaf8LuoVL6i3lq82O0Q8ByREEi6IfYtHniuswRFo9WOklHMpv4aZX9MdeiNN/ebeOUvpWg==", "path": "microsoft.powershell.coreclr.eventing/7.5.2", "hashPath": "microsoft.powershell.coreclr.eventing.7.5.2.nupkg.sha512"}, "Microsoft.PowerShell.MarkdownRender/7.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-o5oUwL23R/KnjQPD2Oi49WAG5j4O4VLo1fPRSyM/aq0HuTrY2RnF4B3MCGk13BfcmK51p9kPlHZ1+8a/ZjO4Jg==", "path": "microsoft.powershell.markdownrender/7.2.1", "hashPath": "microsoft.powershell.markdownrender.7.2.1.nupkg.sha512"}, "Microsoft.PowerShell.Native/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-FlaJ3JBWhqFToYT0ycMb/Xxzoof7oTQbNyI4UikgubC7AMWt5ptBNKjIAMPvOcvEHr+ohaO9GvRWp3tiyS3sKw==", "path": "microsoft.powershell.native/7.4.0", "hashPath": "microsoft.powershell.native.7.4.0.nupkg.sha512"}, "Microsoft.PowerShell.SDK/7.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-e/BbyddGog9/Hb1pVhlAH5Elc2Xs4GsoNHeAoUKwEx8wRbyIPdB9cw0POdgJJe4a5JPCNWRa2JSIfuZTcI6tIA==", "path": "microsoft.powershell.sdk/7.5.2", "hashPath": "microsoft.powershell.sdk.7.5.2.nupkg.sha512"}, "Microsoft.PowerShell.Security/7.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-VrvxyPGC664ZuNyqPOTRb1Dg1r4xMSBggwxBvjiCPiAuP5H4DIgLZFMEpglhJnQJCkd4IbZLkpM5G4ecNyZeqg==", "path": "microsoft.powershell.security/7.5.2", "hashPath": "microsoft.powershell.security.7.5.2.nupkg.sha512"}, "Microsoft.Security.Extensions/1.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-MnHXttc0jHbRrGdTJ+yJBbGDoa4OXhtnKXHQw70foMyAooFtPScZX/dN+Nib47nuglc9Gt29Gfb5Zl+1lAuTeA==", "path": "microsoft.security.extensions/1.4.0", "hashPath": "microsoft.security.extensions.1.4.0.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry.AccessControl/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-lEfViJE4XxrIl4DiNn+Hs9ab/A5nN8eDjV3ZVloya1JsXfaKZQepg9/zMjIJXH1MUE7TrzqXdQfZnp3cwr/rSA==", "path": "microsoft.win32.registry.accesscontrol/9.0.6", "hashPath": "microsoft.win32.registry.accesscontrol.9.0.6.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-mUa3Chovao0xQ74j2hUN5kDTn3luY/1e9usJYT0r51cgqfQsgQu5C8Cis9h65SNRbkbBwErfXKlBtHCkMiYa/g==", "path": "microsoft.win32.systemevents/9.0.6", "hashPath": "microsoft.win32.systemevents.9.0.6.nupkg.sha512"}, "Microsoft.Windows.Compatibility/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-nzETMdHB75VmEfifhWnv7je2ma9YLoMjzpIKI5JuB2jTdOlUoQFMYnroASg18w/oQEpqY0VA6I6tr/yzuWKwCQ==", "path": "microsoft.windows.compatibility/9.0.6", "hashPath": "microsoft.windows.compatibility.9.0.6.nupkg.sha512"}, "Microsoft.WSMan.Management/7.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-aIV9FqwpTunQk69PMw5xOT5Lm46EFZTMPslajbFfHGIqQx9pJbOz5FoV92IgCi/dlY+C+1JYfwAdbAw2D+kYTw==", "path": "microsoft.wsman.management/7.5.2", "hashPath": "microsoft.wsman.management.7.5.2.nupkg.sha512"}, "Microsoft.WSMan.Runtime/7.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-G<PERSON>9oxZhtbqpDKxSTyNnoPDuZAy0JS26XP1d3Nl3OORYdk7G0Ku6lh4YViMGRh0GUuXLdoR1KGdsAT2gg5oN8iw==", "path": "microsoft.wsman.runtime/7.5.2", "hashPath": "microsoft.wsman.runtime.7.5.2.nupkg.sha512"}, "MimeKit/4.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-oa4JuhAzJydHnPCc/XWeyBUGd3uiVyWW0NXqOVgkXEHjbHlPVBssklK3mpw9sokjzAaBGdj0bceFsr+NXvAukA==", "path": "mimekit/4.13.0", "hashPath": "mimekit.4.13.0.nupkg.sha512"}, "MiniExcel/1.41.3": {"type": "package", "serviceable": true, "sha512": "sha512-wa6aS2+4Yc9KX0rZRU1M56rjAI3zqIiQWxiL3aNtW/3rq+owh1wwHca7DHfVnNp9XCExMm1tTik3cpUwJvjwqA==", "path": "miniexcel/1.41.3", "hashPath": "miniexcel.1.41.3.nupkg.sha512"}, "Minio/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7tZj90WEuuH60RAP4wBYexjMuJOhCnK7I46hCiX3CtZPackHisLZ8aAJmn3KlwbUX22dBDphwemD+h37vet8Qw==", "path": "minio/5.0.0", "hashPath": "minio.5.0.0.nupkg.sha512"}, "MiniWord/0.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-TDcYSy7bdgRJqMAybx0yxCuQpIdZhPDjImxsZ2kOU+WWeT8FzAcaxYeeKm0JXP9O3FtHeBVbPVuh/fAuQIsjOQ==", "path": "miniword/0.9.2", "hashPath": "miniword.0.9.2.nupkg.sha512"}, "MongoDB.Bson/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6y1uFFmU3O7tIAOcWmOkFImar+VtSeaia3bzaZTDXc30CP1zsPaUNLhM8CzUCzEzGc02rQlEaWQwV/LzA0NlXA==", "path": "mongodb.bson/3.3.0", "hashPath": "mongodb.bson.3.3.0.nupkg.sha512"}, "MongoDB.Driver/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1EVdMMTsUllMJC+4BPQlcxGzn1pB43vAQ03VCx0QpfbhzaYTqnuHKBp3rAPG5aSo2az4JXKXwrQca7GhP/NhUQ==", "path": "mongodb.driver/3.3.0", "hashPath": "mongodb.driver.3.3.0.nupkg.sha512"}, "MySqlConnector/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-6sinY78RvryhHwpup3awdjYO7d5hhWahb5p/1VDODJhSxJggV/sBbYuKK5IQF9TuzXABiddqUbmRfM884tqA3Q==", "path": "mysqlconnector/2.2.5", "hashPath": "mysqlconnector.2.2.5.nupkg.sha512"}, "NewLife.Core/11.5.2025.701": {"type": "package", "serviceable": true, "sha512": "sha512-6n7N/Ef4FcLLiqZo02G3dGeLaxjb57lezKcVWxro6dJrWf3uXL8Nf9XHNP0eBCCdSVTkhbED9f5nGDBCYTdX2Q==", "path": "newlife.core/11.5.2025.701", "hashPath": "newlife.core.11.5.2025.701.nupkg.sha512"}, "NewLife.Redis/6.3.2025.701": {"type": "package", "serviceable": true, "sha512": "sha512-3qcQ7Tp2L0yymLjRvvhpqwMnOtTNgK9kiSx70As2/bFch21sz0m/RKziUlpsESINrvGnxYv+fEHLcUyjhB71Nw==", "path": "newlife.redis/6.3.2025.701", "hashPath": "newlife.redis.6.3.2025.701.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "Novell.Directory.Ldap.NETStandard/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qyaOhuyv5nhp35yK3X0cBa+pnuVyOqec37LKQDzRSmFPECn6d1cnxBCrJdaqgaWuvOQtWa1rqlEaZyp+lpcXZQ==", "path": "novell.directory.ldap.netstandard/4.0.0", "hashPath": "novell.directory.ldap.netstandard.4.0.0.nupkg.sha512"}, "Npgsql/5.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-1u4iCPKL9wtPeSUzChIbgq/BlOhvf44o7xASacdpMY7z0PbqACKpNOF0fjEn9jDV/AJl/HtPY6zk5qasQ1URhw==", "path": "npgsql/5.0.18", "hashPath": "npgsql.5.0.18.nupkg.sha512"}, "OnceMi.AspNetCore.OSS/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ESndB5bQ37n+iTMCCL/HGll0+WhXuHeErfMaUbhnGM/ib0koFMY91cUe1wwLWLTi8EKucQgdAzc/im4+QS5zew==", "path": "oncemi.aspnetcore.oss/1.2.0", "hashPath": "oncemi.aspnetcore.oss.1.2.0.nupkg.sha512"}, "Oracle.ManagedDataAccess.Core/23.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-wgCcndZnwbWySb4Bm0UOAJO8wOagFAs1IG8Aa4ZX38D9N9MrySfryGKJd+yajb2CDGXv/yL3x7yES6mmF4OMWw==", "path": "oracle.manageddataaccess.core/23.8.0", "hashPath": "oracle.manageddataaccess.core.23.8.0.nupkg.sha512"}, "Oscar.Data.SqlClient/4.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-VJ3xVvRjxrPi/mMPT5EqYiMZor0MjFu83mw1qvUveBFWJSudGh9BOKZq7RkhqeNCcL1ud0uK0/TVkw+xTa4q4g==", "path": "oscar.data.sqlclient/4.0.4", "hashPath": "oscar.data.sqlclient.4.0.4.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "path": "pipelines.sockets.unofficial/2.2.8", "hashPath": "pipelines.sockets.unofficial.2.2.8.nupkg.sha512"}, "Portable.BouncyCastle/*******": {"type": "package", "serviceable": true, "sha512": "sha512-iLfdr+T33QrDpsvcKRbMVk0PnjqE4hcrbgc6IH+A1BdPAThZlMB9s5TtFANhVVtdS+0oN3yIivgXRPRET7zuZw==", "path": "portable.bouncycastle/*******", "hashPath": "portable.bouncycastle.*******.nupkg.sha512"}, "Qiniu/8.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-SdmxJ/W/32zWB5/tkUq9Qcn/XA2FW2wdOsBqbJBQ8tN1lWyb/4bD+DdWKy9WGzVrVmxtCxmzWeiJ14p1//ufcA==", "path": "qiniu/8.3.1", "hashPath": "qiniu.8.3.1.nupkg.sha512"}, "QRCoder/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-XmPA81eo+oRxBuyVdswsSkTGTE1d3thfF11Z1PdD7oB56A6HU4G4AAOdySmGRMb/cljwlFTMWUtosGEnwpS6GA==", "path": "qrcoder/1.6.0", "hashPath": "qrcoder.1.6.0.nupkg.sha512"}, "RabbitMQ.Client/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-y3c6ulgULScWthHw5PLM1ShHRLhxg0vCtzX/hh61gRgNecL3ZC3WoBW2HYHoXOVRqTl99Br9E7CZEytGZEsCyQ==", "path": "rabbitmq.client/7.1.2", "hashPath": "rabbitmq.client.7.1.2.nupkg.sha512"}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-S1Gu7KIiBM5Zfkve5iaVZUMPToGZ8fc13IMLNdyU20G9nq9LnKSN5e0xb/TFr4N6IqWkAxmTD4JkcWXdjdk33g==", "path": "runtime.android-arm.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.android-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-B5GL3MQcQ4OI02Q3jXdiISS5N7ZI6LCHyDQTpfJTpzTdf4SDwTMMxrcpGaPSth6l7yVyAtJJbIhgdFDw3PmOhg==", "path": "runtime.android-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.android-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-7Aq/6BqdFCShOlL+f7ltFCQkwo/YFzZ+wOmK8ObpGfzhxWp2Mg7H4DuMoqd1pO+ikdfbOcDm7cfdMmsUwgbKkQ==", "path": "runtime.android-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.android-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-LG0iGAvkdSWD3WT+oZcozJFZKeINQ160n68YfnwVgtmFpMR2O3GIfuMrf9WJjfnZJb6pbaNnLGqOTpXVJTJa2Q==", "path": "runtime.android-x86.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.android-x86.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-RzAA2D77LRuYVTUXKG8wLRbbDF6BA+fHeBtsdar3ARj3cWmqscR3sb5QgROBKtDj1G2Idu3aj+5Bk3TYc+f4XA==", "path": "runtime.linux-arm.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-a9pVVDsO76cdoGN3CYEgxTE54C5udq9hfOjPKVxyfwOk1N12w18VKL2a15deezFMqjGggaVyK0cFBj9qG7pqWw==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Kb8CZNgH0aWd8Ks6jS1F286SmVJ4FmJjiUdrQTvHu1aN1cWpfwLZ1qOARvFI3lbXE/geOzBIHDNWmQjyOAeUlg==", "path": "runtime.linux-bionic-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BCbWsWzSdv9ly9pR1JFY89an+8nc9yZtjAPPlcpH31UUP0AuI27rnrKzcZAuqFXlKy2M8EZVnjV0czSTFueqGA==", "path": "runtime.linux-bionic-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-eapJ2k/eeMK7Q/Mbticy5rgSb+qI884W9Wk6UTDnEdRpd0BvKbhgM845QEmk3vrxT6B8cCr4A8pRseZBdmk4WA==", "path": "runtime.linux-musl-arm.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-+3S2ksd6iFNeyAxtCZ2dGlxXNGQsOgIgGiecu34++UnUTY9KFhkg8T69hyjEMg4+dRQXEWrU4+vP4AI3s5GlMw==", "path": "runtime.linux-musl-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Fa+EbKuQ6W4EzdVRAA/6ffJ3C0eQ93D8bhmnFaVEHBkfDTKNUSZKhjLdYgubvMrSQlsQ8XLGw0Ld1UXMgGCj7w==", "path": "runtime.linux-musl-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-POgI6/WVHtpxbqvfFqVybZckRbgFVp3nE0fOBpIQdKiZ9C3MPKKibyFNEBK81ZlgmtTEpJP0jMvLSuEbA/p95g==", "path": "runtime.linux-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VMJ5KlYwc2OUPEXmuMetGdwU2IKCDH0mYPz+7G0e2psKJ6Q4JVq9VIOK/nnFJ9z0nbw7Cxu5m7nwh8p/ZPr/eA==", "path": "runtime.maccatalyst-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-kbPhYFOoSo7l71lZo4RFx51Vj6BbQAL3QFn4duULbrpV1GEQX5ZrmBSpdxigcvDMit1i/+wDTyMll9t56i/knQ==", "path": "runtime.maccatalyst-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-A8v6PGmk+UGbfWo5Ixup0lPM4swuSwOiayJExZwKIOjTlFFQIsu3QnDXECosBEyrWSPryxBVrdqtJyhK3BaupQ==", "path": "runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Q8gHRVpOlTQSEqKtimf7s3lr8OaYaxXcrXjtF78k+6RogQ0BpEHnUgeBZBoQ53qSiztBAzkF22uPOHq+/+goOA==", "path": "runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-a+KcHUMoNJG1Z9uvf28HDmT8jK5rm0ExTUdScsdP/ukU2KE7ah+vLxNbh4zCxzvGHsx+Z6bVpaWLjuSYNbqilQ==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-ogySJ/MVq9J/X1ugQOAA4dQfoJSUnZtIbPLr8t2tsaGkV7TBgWOnFInRXy1c20o79M6ARyus12UinDKsFaLkwA==", "path": "runtime.osx-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-8eJkROLGh6DBQLR666q2aOpAaean2puZaZ6Ur9YxoyGzjaZhv8OJSxtnDou54+OXMkXtLUdyQC0so47sOsqZjg==", "path": "sixlabors.imagesharp/3.1.8", "hashPath": "sixlabors.imagesharp.3.1.8.nupkg.sha512"}, "SixLabors.ImageSharp.Web/3.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-xC0gpnV14tY1fXtmhOoyhqf0XSy9g48wLzRS89SRgOHkO8enLJDjATv4ghklSsjMtRZuseYHkYXGty80Jp2Eow==", "path": "sixlabors.imagesharp.web/3.1.5", "hashPath": "sixlabors.imagesharp.web.3.1.5.nupkg.sha512"}, "SkiaSharp/3.116.1": {"type": "package", "serviceable": true, "sha512": "sha512-DNDwbRjP+aMo27dV2h/uHCVTcWubWWxHnPLiePNyl24f4Pv43mQ8AQQeseOrKR+J3AOCEs6t0sUjo0aa3j3RWQ==", "path": "skiasharp/3.116.1", "hashPath": "skiasharp.3.116.1.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux.NoDependencies/3.116.1": {"type": "package", "serviceable": true, "sha512": "sha512-yER+zmZzK4G642j0fbOBg3ZfLUs93o1kmHEXEOPZgMCzGVSmLvGmsFYIIlIQaMEpWY/TktFiZGqEKkT1RJ/YjQ==", "path": "skiasharp.nativeassets.linux.nodependencies/3.116.1", "hashPath": "skiasharp.nativeassets.linux.nodependencies.3.116.1.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/3.116.1": {"type": "package", "serviceable": true, "sha512": "sha512-3KPvpKysDmEMt0NnAZPX5U6KFk0LmG/72/IjAIJemIksIZ0Tjs9pGpr3L+zboVCv1MLVoJLKl3nJDXUG6Jda6A==", "path": "skiasharp.nativeassets.macos/3.116.1", "hashPath": "skiasharp.nativeassets.macos.3.116.1.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/3.116.1": {"type": "package", "serviceable": true, "sha512": "sha512-dRQ75MCI8oz6zAs2Y1w6pq6ARs4MhdNG+gf3doOxOxdnueDXffQLGQIxON54GDoxc0WjKOoHMKBR4DhaduwwQw==", "path": "skiasharp.nativeassets.win32/3.116.1", "hashPath": "skiasharp.nativeassets.win32.3.116.1.nupkg.sha512"}, "SKIT.FlurlHttpClient.Common/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZCcSa2+ZrZKwAXvYBdMmzio/SgPFwqB6xjyRvWjjv+P8V5QRk41tMNfjXreKwWsRhj25/mAdKZLc5YfOsofQUQ==", "path": "skit.flurlhttpclient.common/3.1.1", "hashPath": "skit.flurlhttpclient.common.3.1.1.nupkg.sha512"}, "SKIT.FlurlHttpClient.Wechat.Api/3.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-jqMYrZ3ZSAYQ9s72syw1wzBxd58Uu+zlQgl/MDAcQ+7aD2rnGJGAaiH/zC11LLrxDFgxCJwFYNpeGFTmxyWRVw==", "path": "skit.flurlhttpclient.wechat.api/3.11.0", "hashPath": "skit.flurlhttpclient.wechat.api.3.11.0.nupkg.sha512"}, "SKIT.FlurlHttpClient.Wechat.TenpayV3/3.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-nuKt8FN3HbrwblV2TtfrsVnwnMftQmKh2F0O8+IfmeAe13QKlk910mCtF+abpNki6b2/8/tiqkig2a8B6kEJmA==", "path": "skit.flurlhttpclient.wechat.tenpayv3/3.13.0", "hashPath": "skit.flurlhttpclient.wechat.tenpayv3.3.13.0.nupkg.sha512"}, "Snappier/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "path": "snappier/1.0.0", "hashPath": "snappier.1.0.0.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "path": "sqlitepclraw.core/2.1.10", "hashPath": "sqlitepclraw.core.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512"}, "SqlSugar.MongoDbCore/*********": {"type": "package", "serviceable": true, "sha512": "sha512-KlDvd+wzwt3I2m5txqpezP2UNaNKp6C8QEfnRHsDubVWiaGHEQfTAJjVZyfCMbqXrUl4l5RRw0PKpIN2U0Heow==", "path": "sqlsugar.mongodbcore/*********", "hashPath": "sqlsugar.mongodbcore.*********.nupkg.sha512"}, "SqlSugarCore/*********": {"type": "package", "serviceable": true, "sha512": "sha512-6ZRn5ii/X5JD96KT3uc+A/xUQh2YLRW8k3/N4bKNrrvV0AlIV6EMU9fwXzcqy5cwRJA0rTPlSUnnawpefeWS6g==", "path": "sqlsugarcore/*********", "hashPath": "sqlsugarcore.*********.nupkg.sha512"}, "SqlSugarCore.Dm/8.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-M1Wh/JdK/UgaJe9m76/EKTrV0d18sBDvsgh7FCmRB8U0oJYA0pkFJWLchqi9CMYv1RJjc3f4tB0tFzKjlRV5rQ==", "path": "sqlsugarcore.dm/8.8.0", "hashPath": "sqlsugarcore.dm.8.8.0.nupkg.sha512"}, "SqlSugarCore.Kdbndp/9.3.7.613": {"type": "package", "serviceable": true, "sha512": "sha512-kfG7lNyEI6UI9TtmMww9kYRsVOfeUuuf4rFu/0e7ZtUGi+JIS5m/D1hyTEAjYtTVGZp2CxFoVb/1/c4t7t5Qow==", "path": "sqlsugarcore.kdbndp/9.3.7.613", "hashPath": "sqlsugarcore.kdbndp.9.3.7.613.nupkg.sha512"}, "SSH.NET/2025.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AKYbB+q2zFkNQbBFx5gXdv+Wje0baBtADQ35WnMKi4bg1ka74wTQtWoPd+fOWcydohdfsD0nfT8ErMOAPxtSfA==", "path": "ssh.net/2025.0.0", "hashPath": "ssh.net.2025.0.0.nupkg.sha512"}, "StackExchange.Redis/2.7.27": {"type": "package", "serviceable": true, "sha512": "sha512-Uqc2OQHglqj9/FfGQ6RkKFkZfHySfZlfmbCl+hc+u2I/IqunfelQ7QJi7ZhvAJxUtu80pildVX6NPLdDaUffOw==", "path": "stackexchange.redis/2.7.27", "hashPath": "stackexchange.redis.2.7.27.nupkg.sha512"}, "Swashbuckle.AspNetCore/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-Akk4oFgy0ST8Q8pZTfPbrt045tWNyMMiKhlbYjG3qnjQZLz645IL5vhQm7NLicc2sAAQ+vftArIlsYWFevmb2g==", "path": "swashbuckle.aspnetcore/9.0.3", "hashPath": "swashbuckle.aspnetcore.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-CGpkZDWj1g/yH/0wYkxUtBhiFo5TY/Esq2fS0vlBvLOs1UL2Jzef9tdtYmTdd3zBPtnMyXQcsXjMt9yCxz4VaA==", "path": "swashbuckle.aspnetcore.swagger/9.0.3", "hashPath": "swashbuckle.aspnetcore.swagger.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-STqjhw1TZiEGmIRgE6jcJUOcgU/Fjquc6dP4GqbuwBzqWZAWr/9T7FZOGWYEwKnmkMplzlUNepGHwnUrfTP0fw==", "path": "swashbuckle.aspnetcore.swaggergen/9.0.3", "hashPath": "swashbuckle.aspnetcore.swaggergen.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-DgJKJASz5OAygeKv2+N0FCZVhQylESqLXrtrRAqIT0vKpX7t5ImJ1FL6+6OqxKiamGkL0jchRXR8OgpMSsMh8w==", "path": "swashbuckle.aspnetcore.swaggerui/9.0.3", "hashPath": "swashbuckle.aspnetcore.swaggerui.9.0.3.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.CodeDom/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-9u1pyEykc0bWBHf1cIVwRoMqrEtxtXdC2ss1K02pvrkwHPcyYmy1glO+kLbyqPO9ehCTl+2dFyUuTUNl1Fde5g==", "path": "system.codedom/9.0.6", "hashPath": "system.codedom.9.0.6.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-0YFqjhp/mYkDGpU0Ye1GjE53HMp9UVfGN7seGpAMttAC0C40v5gw598jCgpbBLMmCo0E5YRLBv5Z2doypO49ZQ==", "path": "system.componentmodel.annotations/4.7.0", "hashPath": "system.componentmodel.annotations.4.7.0.nupkg.sha512"}, "System.ComponentModel.Composition/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VWAMVgDbk/iD8KRV+aFBgE2EmDbDK3xYeHaDTs68PXRKdjx/s/0q03YCw6oZGVnY/heBs07KUnCYaZbUsnmBCA==", "path": "system.componentmodel.composition/9.0.6", "hashPath": "system.componentmodel.composition.9.0.6.nupkg.sha512"}, "System.ComponentModel.Composition.Registration/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Esfx8XiOJNy3YkIRIEcPd5DIDORkvD+NyE3gqzwyNdY9Wz8V6y9boMOzKzb4mGfn4ZqqyRdvJluh8WxKbm2Tpw==", "path": "system.componentmodel.composition.registration/9.0.6", "hashPath": "system.componentmodel.composition.registration.9.0.6.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-GQYhl3XCSGhxQvbRCjQiuGbJjm1tlq2lu98SGItjeingM7D+uKNnmKlK0MMuAU1asT7YtJ8uoT83WnGCuna9Qg==", "path": "system.configuration.configurationmanager/9.0.6", "hashPath": "system.configuration.configurationmanager.9.0.6.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Data.Odbc/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-IvXvGdpQUs/+GAaBcOTiKyJ+08/Da54HpN+2JcxgKVdL0v3RW2YvSZR5H4IHgUUuRk/16vwohnLQc9tnNbzw8g==", "path": "system.data.odbc/9.0.6", "hashPath": "system.data.odbc.9.0.6.nupkg.sha512"}, "System.Data.OleDb/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-hUIdIhMPpcuo9V47iYjvQAoW/iwIZHj2AJmKos2MW/4ZGjQhpSxXfjJUprYOAyzsvFO0EE8oP46D0vNmawREPg==", "path": "system.data.oledb/9.0.6", "hashPath": "system.data.oledb.9.0.6.nupkg.sha512"}, "System.Data.SqlClient/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-j4KJO+vC62NyUtNHz854njEqXbT8OmAa5jb1nrGfYWBOcggyYUQE0w/snXeaCjdvkSKWuUD+hfvlbN8pTrJTXg==", "path": "system.data.sqlclient/4.9.0", "hashPath": "system.data.sqlclient.4.9.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-nikkwAKqpwWUvV5J8S9fnOPYg8k75Lf9fAI4bd6pyhyqNma0Py9kt+zcqXbe4TjJ4sTPcdYpPg81shYTrXnUZQ==", "path": "system.diagnostics.diagnosticsource/9.0.6", "hashPath": "system.diagnostics.diagnosticsource.9.0.6.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-lum+Dv+8S4gqN5H1C576UcQe0M2buoRjEUVs4TctXRSWjBH3ay3w2KyQrOo1yPdRs1I+xK69STz+4mjIisFI5w==", "path": "system.diagnostics.eventlog/9.0.6", "hashPath": "system.diagnostics.eventlog.9.0.6.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-CrOJB8Iw+XsgD5Tt+PETBdZzWpLEw0FgobKESfcyrn4kxcg9FNz3r1cP5n1c35cTrZNbYCbOnk/xB/0AL1UtLw==", "path": "system.diagnostics.performancecounter/9.0.6", "hashPath": "system.diagnostics.performancecounter.9.0.6.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.DirectoryServices/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-jb9CXtAkrgs7xnp7cv4JEWoOvhIIO1BDub7pBdg456RoFiIVFBaj46twJ6kOsq0QKJKxj4P2GMyBERLHr6OPPA==", "path": "system.directoryservices/9.0.6", "hashPath": "system.directoryservices.9.0.6.nupkg.sha512"}, "System.DirectoryServices.AccountManagement/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-xxbRffj3vJLrG39HDPk10V2Ia805bIsof8a7I4guX/1kN+diC849HGS9FePMukzNjBcJWihtCZHf6bJiJnYrtw==", "path": "system.directoryservices.accountmanagement/9.0.6", "hashPath": "system.directoryservices.accountmanagement.9.0.6.nupkg.sha512"}, "System.DirectoryServices.Protocols/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-6xlvg/xYleDEs/mG+lRDFqRs9pvep2Ejd2bSf+NdudLTETL0ggKKDxYeYrEQavMO70kryvTmA7fHuLG8Q0hFYw==", "path": "system.directoryservices.protocols/9.0.6", "hashPath": "system.directoryservices.protocols.9.0.6.nupkg.sha512"}, "System.Drawing.Common/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-KpPB6e7PYCalslhmq/dVa2tJgCcfUDhEK83j1Eix+BmEcNPP00JJBvFrBT7jGcHLmQzsIA4AthwNFkk+cD3RKA==", "path": "system.drawing.common/9.0.6", "hashPath": "system.drawing.common.9.0.6.nupkg.sha512"}, "System.Formats.Asn1/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqKba7Mm/koKSjKMfW82olQdmfbI5yqeoLV/tidRp7fbh5rmHAQ5raDI/7SU0swTzv+jgqtUGkzmFxuUg0it1A==", "path": "system.formats.asn1/8.0.1", "hashPath": "system.formats.asn1.8.0.1.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GJw3bYkWpOgvN3tJo5X4lYUeIFA2HD293FPUhKmp7qxS+g5ywAb34Dnd3cDAFLkcMohy5XTpoaZ4uAHuw0uSPQ==", "path": "system.identitymodel.tokens.jwt/8.0.1", "hashPath": "system.identitymodel.tokens.jwt.8.0.1.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Hashing/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-sDnWM0N3AMCa86LrKTWeF3BZLD2sgWyYUc7HL6z4+xyDZNQRwzmxbo4qP2rX2MqC+Sy1/gOSRDah5ltxY5jPxw==", "path": "system.io.hashing/7.0.0", "hashPath": "system.io.hashing.7.0.0.nupkg.sha512"}, "System.IO.Packaging/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-WPpjTjDayc21MzH4E6reg3wJZ2L14ZbStT5mhG1eBR0V0bqvTkjAMj9akjx4go52mFPEcy74ZjXMF/1S8lXjnQ==", "path": "system.io.packaging/9.0.6", "hashPath": "system.io.packaging.9.0.6.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-D1nZmsZfUfKQ9/AqiAEafmnYijUoJvXtl0RWZ2P+q/Wq3gXgEtp+NzKTpabw2s0aiuPAsdx8SujQY06W2X4ucQ==", "path": "system.io.ports/9.0.6", "hashPath": "system.io.ports.9.0.6.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.6.6": {"type": "package", "serviceable": true, "sha512": "sha512-DalpBR6IiZsT61Z9h3ujGxhOALmJUE6hrbeWjRUL24AxDIvhxzhxftSrRrF1G8vNggpadDfRiej6ImrpYS8sJA==", "path": "system.linq.dynamic.core/1.6.6", "hashPath": "system.linq.dynamic.core.1.6.6.nupkg.sha512"}, "System.Management/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-pOc5bCurWL3qVDsPP0iycUCRfXBhI/fVe44SiAlVqoZDIbHP080CLyCzfXV1UbdGKN0hQSLSHqr7OI3BhLBRbA==", "path": "system.management/9.0.6", "hashPath": "system.management.9.0.6.nupkg.sha512"}, "System.Management.Automation/7.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-blBOGSal9OpK2KknHf2Vf1tIQQqolD7mo70vyL8vs64FW2lKyTFxcuFEoZY/4tbGbwRmuepb6+1DLLymQVYkJw==", "path": "system.management.automation/7.5.2", "hashPath": "system.management.automation.7.5.2.nupkg.sha512"}, "System.Memory/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-OEkbBQoklHngJ8UD8ez2AERSk2g+/qpAaSWWCBFbpH727HxDq5ydVkuncBaKcKfwRqXGWx64dS6G1SUScMsitg==", "path": "system.memory/4.6.0", "hashPath": "system.memory.4.6.0.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Net.Http/4.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "path": "system.net.http/4.3.4", "hashPath": "system.net.http.4.3.4.nupkg.sha512"}, "System.Net.Http.WinHttpHandler/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-pf888F5zVMHVFfxfs4HNgtd3+kYwetxg9lr3DamFsU/sPdflxzDCLL3vgwwyC3StlLvux/zHPWKLpbS6L3ZFvA==", "path": "system.net.http.winhttphandler/9.0.6", "hashPath": "system.net.http.winhttphandler.9.0.6.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Private.ServiceModel/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-BcUV7OERlLqGxDXZuIyIMMmk1PbqBblLRbAoigmzIUx/M8A+8epvyPyXRpbgoucKH7QmfYdQIev04Phx2Co08A==", "path": "system.private.servicemodel/4.10.3", "hashPath": "system.private.servicemodel.4.10.3.nupkg.sha512"}, "System.Private.Uri/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-o1+7RJnu3Ik3PazR7Z7tJhjPdE000Eq2KGLLWhqJJKXj04wrS8lwb1OFtDF9jzXXADhUuZNJZlPc98uwwqmpFA==", "path": "system.private.uri/4.3.2", "hashPath": "system.private.uri.4.3.2.nupkg.sha512"}, "System.Reactive/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-erBZjkQHWL9jpasCE/0qKAryzVBJFxGHVBAvgRN1bzM0q2s1S4oYREEEL0Vb+1kA/6BKb5FjUZMp5VXmy+gzkQ==", "path": "system.reactive/5.0.0", "hashPath": "system.reactive.5.0.0.nupkg.sha512"}, "System.Reactive.Linq/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IB4/qlV4T1WhZvM11RVoFUSZXPow9VWVeQ1uDkSKgz6bAO+gCf65H/vjrYlwyXmojSSxvfHndF9qdH43P/IuAw==", "path": "system.reactive.linq/5.0.0", "hashPath": "system.reactive.linq.5.0.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Context/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-f9DgmWmXiSQC4CmJudIPO1zjVgC9MEHHbTPY00pJ/2nOqAagr6PXeXkSTb9XEh8y/sWnkL1GbMpYGrUoh2kosA==", "path": "system.reflection.context/9.0.6", "hashPath": "system.reflection.context.9.0.6.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-C1sMLwIG6ILQ2bmOT4gh62V6oJlyF4BlHcVMrOoor49p0Ji2tA8QAoqyMcIhAdH6OHKJ8m7BU+r4LK2CUEOKqw==", "path": "system.reflection.dispatchproxy/4.7.1", "hashPath": "system.reflection.dispatchproxy.4.7.1.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+4sz5vGHPlo+5NpAxf2IlABnqVvOHOxv17b4dONv4hVwyNeFAeBevT14DIn7X3YWQ+eQFYO3YeTBNCleAblOKA==", "path": "system.reflection.metadata/8.0.1", "hashPath": "system.reflection.metadata.8.0.1.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VybpaOQQhqE6siHppMktjfGBw1GCwvCqiufqmP8F1nj7fTUNtW35LOEt3UZTEsECfo+ELAl/9o9nJx3U91i7vA==", "path": "system.reflection.typeextensions/4.7.0", "hashPath": "system.reflection.typeextensions.4.7.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.Caching/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-3U3JgtbP8+1+yDsGC4f5wFMi2S1ERaA5fsy0CPph0hdeMDaWDvGnUvH5VDkQZ1bCtV9UZN4hlYMxtMQ4TjluaQ==", "path": "system.runtime.caching/9.0.6", "hashPath": "system.runtime.caching.9.0.6.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-IQ4NXP/B3Ayzvw0rDQzVTYsCKyy0Jp9KI6aYcK7UnGVlR9+Awz++TIPCQtPYfLJfOpm8ajowMR09V7quD3sEHw==", "path": "system.security.accesscontrol/6.0.1", "hashPath": "system.security.accesscontrol.6.0.1.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "path": "system.security.cryptography.cng/4.3.0", "hashPath": "system.security.cryptography.cng.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-6LROjtkO4UCv0MVnnR8Z6FwnRryT+Sz5dz8ZefZG6/yz1kQGaVUd6gAdXZtqNPORQwuXFrNGTWvi7f8hWRkiyg==", "path": "system.security.cryptography.pkcs/9.0.7", "hashPath": "system.security.cryptography.pkcs.9.0.7.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-yErfw/3pZkJE/VKza/Cm5idTpIKOy/vsmVi59Ta5SruPVtubzxb8CtnE8tyUpzs5pr0Y28GUFfSVzAhCLN3F/Q==", "path": "system.security.cryptography.protecteddata/9.0.6", "hashPath": "system.security.cryptography.protecteddata.9.0.6.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-xHcfATHdJTB7lKYiQZ6yn4y9QKV4pD19j3Ws2fACPR9QCrE3qtOTedYfNkp2ghaUm2GZZgicJv/rQbiWQeNnMw==", "path": "system.security.cryptography.xml/9.0.7", "hashPath": "system.security.cryptography.xml.9.0.7.nupkg.sha512"}, "System.Security.Permissions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Ldz76otOJi3B+6k2nZ4m0t/76WvvO9AjlkWGP3wQf8CnzoqMm3O2KjEstahIH6m2z1rWlB6m05YzPTS08FXiRA==", "path": "system.security.permissions/9.0.6", "hashPath": "system.security.permissions.9.0.6.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.ServiceModel.Duplex/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-IZ8ZahvTenWML7/jGUXSCm6jHlxpMbcb+Hy+h5p1WP9YVtb+Er7FHRRGizqQMINEdK6HhWpD6rzr5PdxNyusdg==", "path": "system.servicemodel.duplex/4.10.3", "hashPath": "system.servicemodel.duplex.4.10.3.nupkg.sha512"}, "System.ServiceModel.Http/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-hodkn0rPTYmoZ9EIPwcleUrOi1gZBPvU0uFvzmJbyxl1lIpVM5GxTrs/pCETStjOXCiXhBDoZQYajquOEfeW/w==", "path": "system.servicemodel.http/4.10.3", "hashPath": "system.servicemodel.http.4.10.3.nupkg.sha512"}, "System.ServiceModel.NetTcp/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-tP7GN7ehqSIQEz7yOJEtY8ziTpfavf2IQMPKa7r9KGQ75+uEW6/wSlWez7oKQwGYuAHbcGhpJvdG6WoVMKYgkw==", "path": "system.servicemodel.nettcp/4.10.3", "hashPath": "system.servicemodel.nettcp.4.10.3.nupkg.sha512"}, "System.ServiceModel.Primitives/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-aNcdry95wIP1J+/HcLQM/f/AA73LnBQDNc2uCoZ+c1//KpVRp8nMZv5ApMwK+eDNVdCK8G0NLInF+xG3mfQL+g==", "path": "system.servicemodel.primitives/4.10.3", "hashPath": "system.servicemodel.primitives.4.10.3.nupkg.sha512"}, "System.ServiceModel.Security/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-vqelKb7DvP2inb6LDJ5Igi8wpOYdtLXn5luDW5qEaqkV2sYO1pKlVYBpr6g6m5SevzbdZlVNu67dQiD/H6EdGQ==", "path": "system.servicemodel.security/4.10.3", "hashPath": "system.servicemodel.security.4.10.3.nupkg.sha512"}, "System.ServiceModel.Syndication/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-x1fabx4YAzfUFMTUZvNYk2I61yw3+9Mfl+Qkue9HAnMnrlV84srcuzTAcagr3a+OaAWGQLUNv1OIFF/L8ZLw+g==", "path": "system.servicemodel.syndication/9.0.6", "hashPath": "system.servicemodel.syndication.9.0.6.nupkg.sha512"}, "System.ServiceProcess.ServiceController/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-ba3cPFu9mNtSJ5DTYsDWIkAheBIOENxnsDsrvtk7TbfgcaYMP9oV5+kPH0c/ENkuD1TSpBT5U2tC1Pe7OiDWLA==", "path": "system.serviceprocess.servicecontroller/9.0.6", "hashPath": "system.serviceprocess.servicecontroller.9.0.6.nupkg.sha512"}, "System.Speech/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Cyc9GLB8OpPNFFSlvnaLzkLSyGCuIENllB47w0WwP+SzVfDWvGdTEK1iIaMQEoRjpdl4sRYrchCDYx6GqNlu/w==", "path": "system.speech/9.0.6", "hashPath": "system.speech.9.0.6.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0VlUQoPaGOpiFppKjvhAiMZ5WngPGcbL3EQlxDSe60WcleFKNHVdqiwRDSJmjA7xtMURWfcvfjb2AtFBkwqZEQ==", "path": "system.text.encoding.codepages/9.0.6", "hashPath": "system.text.encoding.codepages.9.0.6.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-uWRgViw2yJAUyGxrzDLCc6fkzE2dZIoXxs8V6YjCujKsJuP0pnpYSlbm2/7tKd0SjBnMtwfDQhLenk3bXonVOA==", "path": "system.text.encodings.web/9.0.6", "hashPath": "system.text.encodings.web.9.0.6.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "path": "system.text.regularexpressions/4.3.1", "hashPath": "system.text.regularexpressions.4.3.1.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.AccessControl/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-cwFASP4ODq8qdzWm+HkzFtzu0c6AtMLEuhctck1HMOwUdQNwL07IEZxOJaNtsg2urOaa7SN6EbDDUZOgATbjNA==", "path": "system.threading.accesscontrol/9.0.6", "hashPath": "system.threading.accesscontrol.9.0.6.nupkg.sha512"}, "System.Threading.RateLimiting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7mu9v0QDv66ar3DpGSZHg9NuNcxDaaAcnMULuZlaTpP9+hwXhrxNGsF5GmLkSHxFdb5bBc1TzeujsRgTrPWi+Q==", "path": "system.threading.ratelimiting/8.0.0", "hashPath": "system.threading.ratelimiting.8.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.Web.Services.Description/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6pwntR5vqLOzUPU9GcLVNEASAVf0GFeXoRF4p/SWIiU3073ZbWJ6dJM5cpXgylcbJDjlwPqNx9f5Y4Od0cNfDA==", "path": "system.web.services.description/8.0.0", "hashPath": "system.web.services.description.8.0.0.nupkg.sha512"}, "System.Windows.Extensions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-t0NLE1cAXUmO2aiwCjesf9TVwttjaHnkKpiFBTm/d10QnQD6aiGYmrWLhS0HBIo4utB/jPkgRF0SJVbgrgfZ/Q==", "path": "system.windows.extensions/9.0.6", "hashPath": "system.windows.extensions.9.0.6.nupkg.sha512"}, "Tea/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-vwpYRSIcbNeRDrrRp6eG1rB2rAE5vOZyrQAjMPCxDTisptsIq4ApdmFc9jvthCIgOeBwTyOe4Y/TYTD88Zq+Yw==", "path": "tea/1.1.3", "hashPath": "tea.1.1.3.nupkg.sha512"}, "Tencent.QCloud.Cos.Sdk/5.4.34": {"type": "package", "serviceable": true, "sha512": "sha512-2bBeaa6fGHUI0zKRORNFK/s3SrEdQxEkqCEui6w/DCYnjaF+/XF09BGdxcIOn0h2EHHNjXPf10Iwh28gS0O93w==", "path": "tencent.qcloud.cos.sdk/5.4.34", "hashPath": "tencent.qcloud.cos.sdk.5.4.34.nupkg.sha512"}, "TencentCloudSDK.Common/3.0.1273": {"type": "package", "serviceable": true, "sha512": "sha512-1uX3OCKPa8onJOw2qgH663UsKIWuUZ37yAINnTXr2Gw7+fYkKzAUySksXmumitd2dVAlvH8XUffKzX4gFhqnlQ==", "path": "tencentcloudsdk.common/3.0.1273", "hashPath": "tencentcloudsdk.common.3.0.1273.nupkg.sha512"}, "TencentCloudSDK.Sms/3.0.1273": {"type": "package", "serviceable": true, "sha512": "sha512-FcLRn6Hf65MoopTcrg195HQraBzX/yaLtKpAQy7aAhywMSleBJ8KSGfeimrE/dYD15VPKhwGPjYqaqSwGOejgg==", "path": "tencentcloudsdk.sms/3.0.1273", "hashPath": "tencentcloudsdk.sms.3.0.1273.nupkg.sha512"}, "UAParser/3.1.47": {"type": "package", "serviceable": true, "sha512": "sha512-I68Jl/Vs5RQZdz9BbmYtnXgujg0jVd61LhKbyNZOCm9lBxZFGxLbiQo6yFj21VYi7DzPvEvrVOmeC6v41AoLfw==", "path": "uaparser/3.1.47", "hashPath": "uaparser.3.1.47.nupkg.sha512"}, "XiHan.Framework/0.11.6": {"type": "package", "serviceable": true, "sha512": "sha512-u8OnnXQt2rlzFo1jKJHbX/XE21VZjGECmkr0MMsWwTXfhQ5wH5+nr5PcEEG3ziblflkc7goMv4ftHwCIp2XXhg==", "path": "xihan.framework/0.11.6", "hashPath": "xihan.framework.0.11.6.nupkg.sha512"}, "XiHan.Framework.Utils/0.11.6": {"type": "package", "serviceable": true, "sha512": "sha512-5B/Qarj1G9f5XcExBSPJXGTjUyUHH+XX+qTbc1RA9uWVBajdbZnSglsEf9V/i0zVPxFrYi5zs3R847EOnj9eGQ==", "path": "xihan.framework.utils/0.11.6", "hashPath": "xihan.framework.utils.0.11.6.nupkg.sha512"}, "Yitter.IdGenerator/1.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-F4nOJ7Geq41vgNWX9E6/vkxRzFInACGpDp4Kad2mA2WIKhEwgPyE9FpulBAuEmDByrfHHz6mOII3IIeLJAh91g==", "path": "yitter.idgenerator/1.0.14", "hashPath": "yitter.idgenerator.1.0.14.nupkg.sha512"}, "ZstdSharp.Port/0.7.3": {"type": "package", "serviceable": true, "sha512": "sha512-U9Ix4l4cl58Kzz1rJzj5hoVTjmbx1qGMwzAcbv1j/d3NzrFaESIurQyg+ow4mivCgkE3S413y+U9k4WdnEIkRA==", "path": "zstdsharp.port/0.7.3", "hashPath": "zstdsharp.port.0.7.3.nupkg.sha512"}, "Admin.NET.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}