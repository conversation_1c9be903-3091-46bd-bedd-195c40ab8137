{"ContentRoots": ["D:\\Project\\欧信MQTT中台\\mqtt-middle-platform\\Admin.NET\\Admin.NET.Web.Entry\\wwwroot\\"], "Root": {"Children": {"signalr-test.html": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "signalr-test.html"}, "Patterns": null}, "images": {"Children": {"logo.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "images/logo.png"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "template": {"Children": {"api.ts.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "template/api.ts.vm"}, "Patterns": null}, "data.data.ts.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "template/data.data.ts.vm"}, "Patterns": null}, "dataModal.vue.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "template/dataModal.vue.vm"}, "Patterns": null}, "Dto.cs.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "template/Dto.cs.vm"}, "Patterns": null}, "editDialog.vue.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "template/editDialog.vue.vm"}, "Patterns": null}, "Entity.cs.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "template/Entity.cs.vm"}, "Patterns": null}, "index.vue.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "template/index.vue.vm"}, "Patterns": null}, "Input.cs.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "template/Input.cs.vm"}, "Patterns": null}, "Output.cs.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "template/Output.cs.vm"}, "Patterns": null}, "SeedData.cs.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "template/SeedData.cs.vm"}, "Patterns": null}, "Service.cs.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "template/Service.cs.vm"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "upload": {"Children": {"logo.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "upload/logo.png"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": [{"ContentRootIndex": 0, "Pattern": "**", "Depth": 0}]}}