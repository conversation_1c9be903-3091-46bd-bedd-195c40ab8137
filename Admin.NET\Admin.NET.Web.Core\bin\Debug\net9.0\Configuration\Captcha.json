{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",

  // Lazy.Captcha.Core 组件详细文档(https://api.gitee.com/pojianbing/lazy-captcha/)
  "CaptchaOptions": {
    "CaptchaType": 10, // 验证码类型0、1、2、3、4、5、6、7、8、9、10、11
    "CodeLength": 1, // 验证码长度, 要放在CaptchaType设置后  当类型为算术表达式时，长度代表操作的个数, 例如2
    "ExpirySeconds": 60, // 验证码过期秒数
    "IgnoreCase": true, // 比较时是否忽略大小写
    "StoreageKeyPrefix": "", // 存储键前缀
    "ImageOption": {
      "Animation": true, // 是否启用动画
      "FontSize": 36, // 字体大小
      "Width": 150, // 验证码宽度
      "Height": 50, // 验证码高度
      "BubbleMinRadius": 5, // 气泡最小半径
      "BubbleMaxRadius": 10, // 气泡最大半径
      "BubbleCount": 3, // 气泡数量
      "BubbleThickness": 1.0, // 气泡边沿厚度
      "InterferenceLineCount": 3, // 干扰线数量
      "FontFamily": "kaiti", // 包含actionj,epilog,fresnel,headache,lexo,prefix,progbot,ransom,robot,scandal,kaiti
      "FrameDelay": 300, // 每帧延迟,Animation=true时有效, 默认300
      "BackgroundColor": "#ffffff", //  格式: rgb, rgba, rrggbb, or rrggbbaa format to match web syntax, 默认#fff
      "ForegroundColors": "", //  颜色格式同BackgroundColor,多个颜色逗号分割，随机选取。不填，空值，则使用默认颜色集
      "Quality": 100, // 图片质量（质量越高图片越大，gif调整无效可能会更大）
      "TextBold": true // 粗体
    }
  }
}